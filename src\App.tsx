import { AdvancedToastProvider } from "@/components/toast/AdvancedToastProvider";
import { TooltipProvider } from "@/components/ui/tooltip";
import { BrowserRouter, Routes, Route, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { App as CapacitorApp } from "@capacitor/app";
import { Capacitor } from "@capacitor/core";
import { supabase } from "@/integrations/supabase/client";
import { waitForOAuthSession } from "@/services/authService";
import { toast } from "@/hooks/use-toast";
import Index from "./pages/Index";
import Game from "./pages/Game";
import Rules from "./pages/Rules";
import FeedbackBoard from "./pages/FeedbackBoard";
import Lobby from "./pages/Lobby";
import NotFound from "./pages/NotFound";
import { AuthProvider } from "./context/auth-context";
import { UserStateProvider } from "./context/simplified-user-state-context";
import { setupGlobalErrorHandlers } from "./utils/errorHandlers";
import SplashScreen from "@/components/SplashScreen";
import { useAppLoading } from "@/hooks/useAppLoading";
import AppLayout from "@/components/layout/AppLayout";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsOfService from "./pages/TermsOfService";
import React from "react";

// 🛡️ Inizializza gestori errori globali per filtrare errori delle estensioni
setupGlobalErrorHandlers();

const TestGameOver = React.lazy(() => import("@/pages/TestGameOver"));

// Componente per gestire la navigazione dei deep link
const AppNavigationHandler = () => {
  const navigate = useNavigate();
  const [isProcessingAuth, setIsProcessingAuth] = useState(false);

  useEffect(() => {
    // Monitora i cambiamenti di stato dell'autenticazione
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log(
          "🔄 Auth state change:",
          event,
          session?.user?.email || "no user"
        );
      }
    );

    if (Capacitor.isNativePlatform()) {
      // Gestisce l'apertura dell'app tramite deep link
      const handleAppUrlOpen = async (event: { url: string }) => {
        console.log("🔗 Deep link ricevuto:", event.url);

        // Verifica se è un deep link OAuth per account
        if (event.url.includes("com.eliazavatta.maraffa://account")) {
          try {
            setIsProcessingAuth(true);

            setTimeout(() => {
              if (Capacitor.isNativePlatform()) {
                window.location.replace(window.location.href);
              } else {
                window.location.reload();
              }
            }, 1000);

            // Estrai i parametri dall'URL del deep link
            const url = new URL(event.url);

            // Log di tutti i parametri per debug
            console.log("📋 Parametri URL ricevuti:");
            url.searchParams.forEach((value, key) => {
              console.log(`  ${key}: ${value}`);
            });

            const access_token = url.searchParams.get("access_token");
            const refresh_token = url.searchParams.get("refresh_token");
            const code = url.searchParams.get("code");
            const error_param = url.searchParams.get("error");
            const error_description = url.searchParams.get("error_description");

            // Gestisci eventuali errori OAuth
            if (error_param) {
              console.error(
                "❌ Errore OAuth ricevuto:",
                error_param,
                error_description
              );

              toast({
                title: "❌ Errore durante il login",
                description:
                  error_description ||
                  "Si è verificato un errore durante l'autenticazione",
                variant: "destructive",
                duration: 5000,
              });

              setIsProcessingAuth(false);
              setTimeout(() => navigate("/account"), 500);
              return;
            }

            // Caso 1: Token diretti (access_token + refresh_token)
            if (access_token && refresh_token) {
              console.log("🔑 Processando token OAuth diretti...");

              const { data, error } = await supabase.auth.setSession({
                access_token,
                refresh_token,
              });

              if (error) {
                console.error("❌ Errore nel processare i token OAuth:", error);

                toast({
                  title: "❌ Errore token",
                  description:
                    "Impossibile processare i token di autenticazione",
                  variant: "destructive",
                  duration: 5000,
                });
              } else {
                console.log("✅ Login OAuth completato con successo:", data);

                toast({
                  title: "✅ Login completato!",
                  description:
                    "Benvenuto! L'autenticazione è avvenuta con successo",
                  duration: 4000,
                });
              }
            }
            // Caso 2: Authorization code (da scambiare per token)
            else if (code) {
              console.log("🔄 Processando authorization code...");

              const { data, error } =
                await supabase.auth.exchangeCodeForSession(code);

              if (error) {
                console.error(
                  "❌ Errore nello scambio code per sessione:",
                  error
                );

                toast({
                  title: "❌ Errore codice autorizzazione",
                  description:
                    "Impossibile scambiare il codice per la sessione",
                  variant: "destructive",
                  duration: 5000,
                });
              } else {
                console.log(
                  "✅ Login OAuth via code completato con successo:",
                  data
                );

                toast({
                  title: "✅ Login completato!",
                  description:
                    "Benvenuto! L'autenticazione è avvenuta con successo",
                  duration: 4000,
                });
              }
            }
            // Caso 3: URL hash fragment (alcuni provider passano i token nel fragment)
            else if (event.url.includes("#")) {
              console.log("🔍 Tentativo di parsing fragment per token...");
              const hashParams = new URLSearchParams(event.url.split("#")[1]);

              console.log("📋 Parametri fragment ricevuti:");
              hashParams.forEach((value, key) => {
                console.log(`  ${key}: ${value}`);
              });

              const fragmentAccessToken = hashParams.get("access_token");
              const fragmentRefreshToken = hashParams.get("refresh_token");

              if (fragmentAccessToken && fragmentRefreshToken) {
                console.log("🔑 Processando token OAuth da fragment...");

                const { data, error } = await supabase.auth.setSession({
                  access_token: fragmentAccessToken,
                  refresh_token: fragmentRefreshToken,
                });

                if (error) {
                  console.error(
                    "❌ Errore nel processare i token OAuth da fragment:",
                    error
                  );

                  toast({
                    title: "❌ Errore token fragment",
                    description:
                      "Impossibile processare i token dal fragment URL",
                    variant: "destructive",
                    duration: 5000,
                  });
                } else {
                  console.log(
                    "✅ Login OAuth da fragment completato con successo:",
                    data
                  );

                  toast({
                    title: "✅ Login completato!",
                    description:
                      "Benvenuto! L'autenticazione è avvenuta con successo",
                    duration: 4000,
                  });
                }
              }
            } else {
              console.warn(
                "⚠️ Nessun token o code trovato nel deep link. Verifica la configurazione OAuth."
              );

              toast({
                title: "⚠️ Parametri mancanti",
                description:
                  "Nessun token di autenticazione trovato nel deep link",
                variant: "destructive",
                duration: 5000,
              });
            }

            // Verifica lo stato della sessione prima di navigare usando la funzione robusta
            const { data: sessionData } = await supabase.auth.getSession();
            console.log("🔍 Stato sessione dopo processamento:", sessionData);

            // Se non c'è sessione valida, usa la funzione di polling robusta
            if (!sessionData.session) {
              console.log(
                "⏳ Nessuna sessione trovata immediatamente, inizio polling avanzato..."
              );

              const { session, user } = await waitForOAuthSession(10, 500);

              if (!session || !user) {
                console.warn(
                  "⚠️ Nessuna sessione trovata dopo polling. Login potrebbe non essere completato."
                );
                toast({
                  title: "⚠️ Sessione non trovata",
                  description:
                    "Il login potrebbe non essere completato. Riprova.",
                  variant: "destructive",
                  duration: 5000,
                });
              } else {
                console.log("✅ Sessione confermata tramite polling avanzato!");
                toast({
                  title: "✅ Sessione confermata!",
                  description: "Login completato con successo.",
                  duration: 3000,
                });
              }
            } else {
              console.log("✅ Sessione valida trovata immediatamente!");
            }

            setIsProcessingAuth(false);

            // Naviga alla pagina account dopo aver processato l'auth
            setTimeout(() => {
              navigate("/account");
            }, 1000);
          } catch (error) {
            console.error("❌ Errore nel processare deep link OAuth:", error);

            setIsProcessingAuth(false);

            toast({
              title: "❌ Errore imprevisto",
              description:
                "Si è verificato un errore durante l'elaborazione del login",
              variant: "destructive",
              duration: 5000,
            });

            // Naviga comunque alla pagina account dopo un breve delay
            setTimeout(() => navigate("/account"), 500);
          }
        }
      };

      // Aggiungi il listener per deep link
      CapacitorApp.addListener("appUrlOpen", handleAppUrlOpen);

      // Cleanup dei listener
      return () => {
        CapacitorApp.removeAllListeners();
        authListener.subscription.unsubscribe();
      };
    } else {
      // Cleanup solo del listener auth su web
      return () => {
        authListener.subscription.unsubscribe();
      };
    }
  }, [navigate]);
  return null; // Questo componente non renderizza nulla
};

// Componente wrapper per gestire il caricamento iniziale
const AppWithLoading = () => {
  const { isInitialLoading, isReady } = useAppLoading();
  const [showSplash, setShowSplash] = useState(true);

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  if (showSplash) {
    return (
      <SplashScreen onComplete={handleSplashComplete} isAppReady={isReady} />
    );
  }

  return (
    <AppLayout>
      <AppNavigationHandler />
      <Routes>
        <Route path="/" element={<Index />} />
        <Route path="/account" element={<Index />} />
        <Route path="/friends" element={<Index />} />
        <Route path="/shop" element={<Index />} />
        <Route path="/game" element={<Game />} />
        <Route path="/play" element={<Game />} />
        <Route path="/lobby" element={<Lobby />} />
        <Route path="/rules" element={<Rules />} />
        <Route path="/feedback" element={<FeedbackBoard />} />
        <Route path="/privacy-policy" element={<PrivacyPolicy />} />
        <Route path="/terms" element={<TermsOfService />} />
        <Route
          path="/test-gameover"
          element={
            <React.Suspense
              fallback={<div className="p-8 text-center">Caricamento…</div>}
            >
              <TestGameOver />
            </React.Suspense>
          }
        />
        {/* Audio test route removed for production */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </AppLayout>
  );
};

const App = () => (
  <AuthProvider>
    <UserStateProvider>
      <TooltipProvider>
        <AdvancedToastProvider />
        <BrowserRouter>
          <AppWithLoading />
        </BrowserRouter>
      </TooltipProvider>
    </UserStateProvider>
  </AuthProvider>
);

export default App;
