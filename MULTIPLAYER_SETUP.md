# 🎮 Setup Multiplayer Privato - Maraffa Romagnola

## 📋 Prerequisiti

- ✅ Progetto Maraffa Romagnola esistente
- ✅ Supabase configurato e funzionante
- ✅ Autenticazione utenti attiva
- ✅ Node.js e npm/yarn installati

## 🗄️ Setup Database

### 1. <PERSON>se<PERSON><PERSON> lo script SQL

Esegui il file `sql/private_games_schema.sql` nel tuo database Supabase:

```bash
# Copia il contenuto del file e incollalo nell'editor SQL di Supabase
# Oppure usa la CLI di Supabase
supabase db reset
```

### 2. Verifica le tabelle create

Controlla che siano state create le tabelle:
- `game_sessions`
- `game_players`

### 3. Verifica le policy RLS

Assicurati che le policy di Row Level Security siano attive e configurate correttamente.

## 🔧 Configurazione Applicazione

### 1. Variabili di ambiente

Aggiungi al tuo `.env.local`:

```env
# Multiplayer settings (opzionali, hanno valori di default)
VITE_DEFAULT_GAME_TIMER=60
VITE_DEFAULT_VICTORY_POINTS=31
VITE_ROOM_EXPIRY_MINUTES=10
VITE_MAX_RECONNECT_ATTEMPTS=5
```

### 2. Configurazione Supabase Realtime

Assicurati che Supabase Realtime sia abilitato per le tabelle:

```sql
-- Abilita realtime per le tabelle
ALTER PUBLICATION supabase_realtime ADD TABLE game_sessions;
ALTER PUBLICATION supabase_realtime ADD TABLE game_players;
```

## 🚀 Test dell'implementazione

### 1. Test creazione stanza

1. Accedi all'app con un utente
2. Vai al menu principale
3. Clicca "Multiplayer"
4. Seleziona "Crea Partita Privata"
5. Inserisci nome stanza e configurazioni
6. Verifica che venga creata la lobby

### 2. Test unirsi a stanza

1. Con un secondo utente/dispositivo
2. Clicca "Unisciti a Partita Privata"
3. Inserisci il nome della stanza creata
4. Verifica che entri nella lobby

### 3. Test gameplay

1. Aggiungi 4 giocatori totali
2. Bilancia le squadre (2 gialli, 2 rossi)
3. L'host avvia la partita
4. Verifica timer di turno e gameplay

## 🔍 Troubleshooting

### Errore: "Tabelle non trovate"

```bash
# Verifica che le tabelle esistano
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('game_sessions', 'game_players');
```

### Errore: "Permission denied"

```bash
# Verifica le policy RLS
SELECT * FROM pg_policies 
WHERE tablename IN ('game_sessions', 'game_players');
```

### Errore: "Realtime non funziona"

1. Verifica che Realtime sia abilitato nel dashboard Supabase
2. Controlla che le tabelle siano aggiunte alla pubblicazione
3. Verifica la connessione WebSocket nel browser

### Errore: "Stanze non si eliminano"

```sql
-- Esegui manualmente la pulizia
SELECT cleanup_expired_sessions();

-- Verifica il cron job (se abilitato)
SELECT * FROM cron.job WHERE jobname = 'cleanup-expired-sessions';
```

## 📊 Monitoraggio

### Dashboard Supabase

Monitora:
- Numero di sessioni attive
- Connessioni Realtime
- Utilizzo banda
- Errori nei log

### Log dell'applicazione

Cerca questi log nel browser:
- `🎮 Creating private game session`
- `✅ Private game session created`
- `🔄 Realtime update`
- `🔌 Player disconnected/reconnected`

## 🔒 Sicurezza

### Rate Limiting

Implementa rate limiting per:
- Creazione stanze (max 5/ora per utente)
- Heartbeat (max 1 ogni 10 secondi)
- Riconnessioni (backoff esponenziale)

### Validazione Input

Tutti gli input sono validati:
- Nome stanza: 3-20 caratteri
- Punti vittoria: 21, 31, 41
- Timer: 30, 60, 90 secondi

### Cleanup Automatico

- Stanze scadute: ogni 5 minuti
- Giocatori disconnessi: ogni 30 secondi
- Sessioni vuote: immediato

## 🎨 Personalizzazione

### Colori squadre

Modifica in `src/config/multiplayer.ts`:

```typescript
TEAM_COLORS: {
  0: {
    name: 'Squadra Blu',
    bg: 'bg-blue-100',
    // ...
  }
}
```

### Timer configurazioni

Aggiungi nuove opzioni in `TIMER_OPTIONS`:

```typescript
{ value: 120, label: '2m', description: 'Molto rilassato' }
```

### Messaggi personalizzati

Modifica `ERROR_MESSAGES` e `SUCCESS_MESSAGES` per la tua lingua/stile.

## 📱 Mobile Optimization

### Performance

- Heartbeat ottimizzato per batteria
- Cleanup automatico memoria
- Gestione orientamento schermo

### UX Mobile

- Touch-friendly per selezione squadre
- Swipe gestures per navigazione
- Feedback tattile (se supportato)

## 🔄 Aggiornamenti Futuri

### Roadmap

- [ ] Spettatori nelle partite
- [ ] Chat in-game
- [ ] Replay delle partite
- [ ] Tornei privati
- [ ] Statistiche avanzate

### Migrazione Database

Per aggiornamenti futuri, crea script di migrazione:

```sql
-- migration_001_add_spectators.sql
ALTER TABLE game_sessions ADD COLUMN allow_spectators BOOLEAN DEFAULT FALSE;
```

## 📞 Supporto

### Log Debug

Abilita log dettagliati:

```typescript
// In development
localStorage.setItem('debug', 'multiplayer:*');
```

### Segnalazione Bug

Include sempre:
- Browser e versione
- Log della console
- Passi per riprodurre
- Screenshot/video se possibile

---

## ✅ Checklist Installazione

- [ ] Database schema applicato
- [ ] Policy RLS configurate
- [ ] Realtime abilitato
- [ ] Variabili ambiente impostate
- [ ] Test creazione stanza OK
- [ ] Test unirsi stanza OK
- [ ] Test gameplay completo OK
- [ ] Monitoraggio configurato
- [ ] Cleanup automatico funzionante

🎉 **Congratulazioni! Il multiplayer privato è ora attivo!**
