import { Card, Suit } from "../../game/cardUtils";
import { GameState } from "../../game/gameLogic";
import { CardMemory, CardWasteAnalysis, OptimalCardsResult } from "../types";
import {
  getCardValue,
  canWinCurrentTrick,
  getCurrentTrickWinner,
  isCardDominantInSuit,
} from "./cardUtils";
import { analyzeCardMemory } from "../memory/analyzer";
import { CardEvaluator } from "../core/cardEvaluator";

/**
 * 🔥 SISTEMA ANTI-SPRECO DEDICATO
 * Modulo specializzato per prevenire lo spreco di carte di valore
 */

/**
 * Analizza se una carta rappresenta uno spreco in base al contesto attuale
 */
export const analyzeCardWaste = (
  card: Card,
  availableCards: Card[],
  currentTrick: Card[],
  state: GameState,
  playerIndex: number,
  memory: CardMemory | null = null
): CardWasteAnalysis => {
  const cardValue = getCardValue(card);
  const trickValue = currentTrick.reduce(
    (sum, trickCard) => sum + getCardValue(trickCard),
    0
  );
  // 🚨 REGOLA ASSOLUTA: Se ci sono punti sul tavolo e posso vincere, NON È MAI SPRECO!
  if (trickValue >= 1) {
    const canWin = canWinCurrentTrick(
      card,
      currentTrick,
      state.leadSuit,
      state.trumpSuit
    );

    if (canWin) {
      return {
        isWaste: false,
        reason: `REGOLA ASSOLUTA: ${trickValue.toFixed(
          1
        )} punti sul tavolo e posso vincere - MAI SPRECO!`,
        severity: "low",
        alternativesExist: false,
      };
    }

    // 🤝 REGOLA ASSOLUTA BIS: Se il compagno sta vincendo con punti, aiutarlo MAI è spreco!
    if (currentTrick.length > 0 && state.leadPlayer !== undefined) {
      const winnerIndex = getCurrentTrickWinner(
        currentTrick,
        state.leadSuit,
        state.trumpSuit,
        state.leadPlayer
      );
      const winnerPlayerIndex = (state.leadPlayer + winnerIndex) % 4;
      const myTeam = state.players[playerIndex].team;
      const winnerTeam = state.players[winnerPlayerIndex].team;
      const teammateIsWinning =
        winnerTeam === myTeam && winnerPlayerIndex !== playerIndex;

      if (teammateIsWinning) {
        return {
          isWaste: false,
          reason: `REGOLA ASSOLUTA BIS: ${trickValue.toFixed(
            1
          )} punti e compagno vince - aiutare MAI è spreco!`,
          severity: "low",
          alternativesExist: false,
        };
      }
    }
  }

  // Le carte senza valore non sono mai spreco
  if (cardValue === 0) {
    return {
      isWaste: false,
      reason: "Carta senza valore - sempre sicura da giocare",
      severity: "low",
      alternativesExist: false,
    };
  }

  // CONTROLLO 1: SPRECO AGLI AVVERSARI (CRITICO)
  if (card.rank === "3" || card.rank === "2" || card.rank === "A") {
    const canWin = canWinCurrentTrick(
      card,
      currentTrick,
      state.leadSuit,
      state.trumpSuit
    );

    // 🔥 NUOVO: Usa CardEvaluator per rilevare situazioni di vincita ovvia
    if (canWin) {
      const evaluator = new CardEvaluator();
      const isObviousWin = evaluator.isObviousWinSituation(
        card,
        currentTrick,
        state.leadSuit,
        state.trumpSuit,
        trickValue
      );

      if (isObviousWin) {
        return {
          isWaste: false,
          reason: `Situazione di vincita ovvia con ${card.rank} - sempre giustificato`,
          severity: "low",
          alternativesExist: false,
        };
      }

      // Logica differenziata per briscole vs non-briscole
      const isTrump = card.suit === state.trumpSuit;
      const isTwo = card.rank === "2"; // Solo i 2 sono strategici da conservare
      const isThreeNonTrump = card.rank === "3" && !isTrump; // I 3 non di briscola sono per prendere punti

      if (isTwo) {
        // I 2 sono sempre strategici da conservare
        if (isTrump) {
          // Per briscole strategiche (2): soglia più alta (≥2 punti)
          if (trickValue >= 2) {
            return {
              isWaste: false,
              reason: `Briscola strategica (${card.rank}) giustificata con ${trickValue} punti sul tavolo`,
              severity: "low",
              alternativesExist: false,
            };
          }
        } else {
          // Per 2 non di briscola: sono strategici da conservare (soglia ≥2 punti)
          if (trickValue >= 2) {
            return {
              isWaste: false,
              reason: `2 strategico giustificato con ${trickValue} punti sul tavolo`,
              severity: "low",
              alternativesExist: false,
            };
          }
        }
      } else if (isThreeNonTrump) {
        // 🎯 I 3 non di briscola sono OTTIMI per prendere punti!
        // Logica più permissiva per incentivare il loro uso

        // Caso 1: C'è almeno 1 punto sul tavolo -> SEMPRE giusto usare un 3
        if (trickValue >= 1) {
          return {
            isWaste: false,
            reason: `3 non di briscola OTTIMO per prendere ${trickValue} punti`,
            severity: "low",
            alternativesExist: false,
          };
        }

        // Caso 2: Siamo primi o secondi a giocare (posizione strategica per aprire)
        const isEarlyPosition = currentTrick.length <= 1;
        if (isEarlyPosition) {
          return {
            isWaste: false,
            reason: `3 non di briscola per controllo e apertura (${
              currentTrick.length + 1
            }° a giocare)`,
            severity: "low",
            alternativesExist: false,
          };
        }
      }

      // Caso 3: Posizione tardiva ma nessuna carta forte giocata (probabile asso negli avversari)
      if (currentTrick.length >= 2) {
        const hasHighCardsInTrick = currentTrick.some(
          (c) => c.rank === "A" || c.rank === "3" || c.rank === "2"
        );

        if (!hasHighCardsInTrick) {
          return {
            isWaste: false,
            reason: `Carta strategica (${card.rank}) per anticipare possibili assi avversari`,
            severity: "low",
            alternativesExist: false,
          };
        }
      }
    }

    let teammateIsWinning = false;
    if (currentTrick.length > 0 && state.leadPlayer !== undefined) {
      const winnerIndex = getCurrentTrickWinner(
        currentTrick,
        state.leadSuit,
        state.trumpSuit,
        state.leadPlayer
      );
      const winnerPlayerIndex = (state.leadPlayer + winnerIndex) % 4;
      const myTeam = state.players[playerIndex].team;
      const winnerTeam = state.players[winnerPlayerIndex].team;
      teammateIsWinning =
        winnerTeam === myTeam && winnerPlayerIndex !== playerIndex;
    }

    if (!canWin && !teammateIsWinning) {
      return {
        isWaste: true,
        reason: `CRITICO: ${card.rank} andrebbe agli avversari!`,
        severity: "critical",
        alternativesExist: availableCards.length > 1,
      };
    }
  }

  // CONTROLLO 2: CARTE DOMINANTI
  if (memory && isCardDominantInSuit(card, memory, state)) {
    const canWin = canWinCurrentTrick(
      card,
      currentTrick,
      state.leadSuit,
      state.trumpSuit
    );

    if (!canWin) {
      let hasAlternatives = false;
      if (state.leadSuit && card.suit === state.leadSuit) {
        const leadSuitCards = availableCards.filter(
          (c) => c.suit === state.leadSuit
        );
        hasAlternatives =
          leadSuitCards.filter((c) => c.id !== card.id).length > 0;
      } else {
        hasAlternatives =
          availableCards.filter((c) => c.id !== card.id).length > 0;
      }

      if (hasAlternatives) {
        return {
          isWaste: true,
          reason: `DOMINANZA: Carta dominante sprecata, ho alternative`,
          severity: "high",
          alternativesExist: true,
        };
      }
    }
  }

  // CONTROLLO 3: VALORE PRESA VS VALORE CARTA
  if (trickValue === 0) {
    return {
      isWaste: true,
      reason: `PRESA SENZA VALORE: Non do ${cardValue} punti per 0 punti`,
      severity: "medium",
      alternativesExist: availableCards.some((c) => getCardValue(c) === 0),
    };
  }

  // Soglie specifiche per tipo di carta
  if (card.rank === "A" && trickValue < 3) {
    return {
      isWaste: true,
      reason: `ASSO: Richiede almeno 3 punti, presa vale ${trickValue}`,
      severity: "high",
      alternativesExist: availableCards.some((c) => c.rank !== "A"),
    };
  }

  if (card.rank === "K" && trickValue < 2) {
    return {
      isWaste: true,
      reason: `RE: Richiede almeno 2 punti, presa vale ${trickValue}`,
      severity: "medium",
      alternativesExist: availableCards.some((c) => c.rank !== "K"),
    };
  }

  if (cardValue > 0 && trickValue < cardValue * 0.8) {
    return {
      isWaste: true,
      reason: `VALORE: Carta vale ${cardValue}, presa solo ${trickValue}`,
      severity: "low",
      alternativesExist: availableCards.some(
        (c) => getCardValue(c) < cardValue
      ),
    };
  }

  return {
    isWaste: false,
    reason: "Carta appropriata per questa situazione",
    severity: "low",
    alternativesExist: false,
  };
};

/**
 * Sistema unificato per ottenere carte ottimali senza sprechi
 */
export const getOptimalCards = (
  cards: Card[],
  currentTrick: Card[],
  state: GameState,
  playerIndex: number
): OptimalCardsResult => {
  // Ottieni memoria una sola volta
  let memory: CardMemory | null = null;
  try {
    memory = analyzeCardMemory(state);
  } catch (error) {
    console.warn("Errore nell'analisi memoria:", error);
  }

  // Analizza ogni carta una sola volta
  const wasteAnalysis = cards.map((card) =>
    analyzeCardWaste(card, cards, currentTrick, state, playerIndex, memory)
  );

  // Filtra carte non-spreco
  const optimalCards = cards.filter(
    (_, index) => !wasteAnalysis[index].isWaste
  );

  // Se tutte le carte sono spreco, prendi quella con severity più bassa
  let finalCards = optimalCards;
  if (optimalCards.length === 0) {
    console.log(
      `[SISTEMA ANTI-SPRECO] ⚠️ Tutte le carte sono spreco, scelgo male minore`
    );

    const minSeverity = Math.min(
      ...wasteAnalysis.map((analysis) => {
        const severityMap = { low: 1, medium: 2, high: 3, critical: 4 };
        return severityMap[analysis.severity];
      })
    );

    finalCards = cards.filter((_, index) => {
      const severityMap = { low: 1, medium: 2, high: 3, critical: 4 };
      return severityMap[wasteAnalysis[index].severity] === minSeverity;
    });
  }

  // Raccomandazione intelligente
  let bestCard: Card | null = null;
  let reason = "";
  let confidence = 0;

  if (finalCards.length > 0) {
    if (currentTrick.length === 0) {
      // Prima a giocare
      bestCard = finalCards[0];
      reason = "Carta strategica per aprire";
      confidence = 0.7;
    } else {
      // Seguire: cerca carta che può vincere
      const winningCards = finalCards.filter((card) =>
        canWinCurrentTrick(card, currentTrick, state.leadSuit, state.trumpSuit)
      );

      if (winningCards.length > 0) {
        winningCards.sort((a, b) => getCardValue(a) - getCardValue(b));
        bestCard = winningCards[0];
        reason = "Carta più debole che può vincere";
        confidence = 0.85;
      } else {
        // 🔥 CORREZIONE CRITICA: Evita briscole che non possono vincere quando non segui briscola
        const isLeadSuitTrump = state.leadSuit === state.trumpSuit;
        const nonWasteTrumpCards = finalCards.filter(
          (card) => card.suit === state.trumpSuit && !isLeadSuitTrump
        );

        if (nonWasteTrumpCards.length > 0) {
          // Se ci sono briscole che non possono vincere e non stiamo seguendo briscola, evitale
          const nonTrumpCards = finalCards.filter(
            (card) => card.suit !== state.trumpSuit
          );
          if (nonTrumpCards.length > 0) {
            nonTrumpCards.sort((a, b) => getCardValue(a) - getCardValue(b));
            bestCard = nonTrumpCards[0];
            reason = "Evito briscole non vincenti, scarto carta normale";
            confidence = 0.8;
          } else {
            // Solo briscole disponibili, prendi quella di minor valore
            finalCards.sort((a, b) => getCardValue(a) - getCardValue(b));
            bestCard = finalCards[0];
            reason = "Solo briscole disponibili, uso quella di minor valore";
            confidence = 0.5;
          }
        } else {
          finalCards.sort((a, b) => getCardValue(a) - getCardValue(b));
          bestCard = finalCards[0];
          reason = "Carta di minor valore (non posso vincere)";
          confidence = 0.6;
        }
      }
    }
  }

  return {
    optimalCards: finalCards,
    wasteAnalysis,
    recommendation: {
      bestCard,
      reason,
      confidence,
    },
  };
};

/**
 * Controllo rigido per non dare mai carte preziose agli avversari
 */
export const isCardWasteToOpponent = (
  card: Card,
  availableCards: Card[],
  currentTrick: Card[],
  state: GameState,
  playerIndex: number
): boolean => {
  const cardValue = getCardValue(card);

  // Le carte senza valore non sono mai spreco
  if (cardValue === 0) {
    return false;
  }

  // REGOLA ASSOLUTA POTENZIATA: NON DARE carte preziose agli avversari
  const isPreciousCard =
    card.rank === "3" ||
    card.rank === "2" ||
    card.rank === "A" ||
    card.rank === "K" ||
    card.rank === "H" ||
    card.rank === "J";
  if (isPreciousCard) {
    // 🚨 PRIORITÀ ASSOLUTA: ASSO SUL TAVOLO = SEMPRE OK USARE QUALSIASI CARTA!
    const hasAceOnTable = currentTrick.some(
      (trickCard) => trickCard.rank === "A"
    );
    if (hasAceOnTable) {
      console.log(
        `[ANTI-SPRECO] ✅ ASSO SUL TAVOLO! ${card.rank} OK per prendere 1 punto!`
      );
      return false; // NON è spreco quando c'è un asso sul tavolo!
    }

    const canWin = canWinCurrentTrick(
      card,
      currentTrick,
      state.leadSuit,
      state.trumpSuit
    );

    let teammateIsWinning = false;
    if (currentTrick.length > 0 && state.leadPlayer !== undefined) {
      const winnerIndex = getCurrentTrickWinner(
        currentTrick,
        state.leadSuit,
        state.trumpSuit,
        state.leadPlayer
      );
      const winnerPlayerIndex = (state.leadPlayer + winnerIndex) % 4;
      const myTeam = state.players[playerIndex].team;
      const winnerTeam = state.players[winnerPlayerIndex].team;
      teammateIsWinning =
        winnerTeam === myTeam && winnerPlayerIndex !== playerIndex;
    }

    // CONTROLLO RIGOROSO
    if (!canWin && !teammateIsWinning) {
      const hasNoValueAlternatives = availableCards.some(
        (c) => getCardValue(c) === 0
      );

      if (hasNoValueAlternatives) {
        return true; // È uno spreco GRAVE!
      } else {
        // Anche senza alternative, blocca comunque 3, 2, A se la presa vale poco
        if (card.rank === "3" || card.rank === "2" || card.rank === "A") {
          const trickValue = currentTrick.reduce(
            (sum, trickCard) => sum + getCardValue(trickCard),
            0
          );

          if (trickValue < 2) {
            return true;
          }
        }
      }
    }
  }

  // Per tutte le carte con valore, verifica se la presa vale almeno qualcosa
  if (cardValue > 0) {
    const trickValue = currentTrick.reduce(
      (sum, trickCard) => sum + getCardValue(trickCard),
      0
    );

    if (trickValue === 0) {
      return true;
    }

    if (cardValue > trickValue * 1.5) {
      return true;
    }
  }

  return false;
};

/**
 * Trova la migliore carta di scarto per prese senza valore
 */
export const findBestDiscardForWorthlessTrick = (
  cards: Card[],
  leadSuit: Suit | null,
  trumpSuit: Suit | null
): Card => {
  // PRIORITÀ MASSIMA: carte senza valore non-briscola
  const noValueNonTrumps = cards.filter(
    (card) => getCardValue(card) === 0 && card.suit !== trumpSuit
  );

  if (noValueNonTrumps.length > 0) {
    if (leadSuit) {
      const noValueLeadSuit = noValueNonTrumps.filter(
        (card) => card.suit === leadSuit
      );
      if (noValueLeadSuit.length > 0) {
        return noValueLeadSuit[0];
      }
    }
    return noValueNonTrumps[0];
  }

  // PRIORITÀ SECONDA: carte senza valore (anche briscole)
  const noValueCards = cards.filter((card) => getCardValue(card) === 0);
  if (noValueCards.length > 0) {
    if (leadSuit) {
      const noValueLeadSuit = noValueCards.filter(
        (card) => card.suit === leadSuit
      );
      if (noValueLeadSuit.length > 0) {
        return noValueLeadSuit[0];
      }
    }
    return noValueCards[0];
  }

  // PRIORITÀ TERZA: carte di minor valore del seme richiesto
  if (leadSuit) {
    const leadSuitCards = cards.filter((card) => card.suit === leadSuit);
    if (leadSuitCards.length > 0) {
      return leadSuitCards.reduce((lowest, card) =>
        getCardValue(card) < getCardValue(lowest) ? card : lowest
      );
    }
  }

  // ULTIMA RISORSA: carta di minor valore disponibile
  return cards.reduce((lowest, card) => {
    const lowestValue =
      getCardValue(lowest) + (lowest.suit === trumpSuit ? 20 : 0);
    const cardValue = getCardValue(card) + (card.suit === trumpSuit ? 20 : 0);
    return cardValue < lowestValue ? card : lowest;
  });
};
