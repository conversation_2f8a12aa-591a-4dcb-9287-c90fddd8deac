import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface SplashScreenProps {
  onComplete: () => void;
  isAppReady: boolean;
}

const SplashScreen: React.FC<SplashScreenProps> = ({
  onComplete,
  isAppReady,
}) => {
  const [showSplash, setShowSplash] = useState(true);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // Timer minimo di 1.7 secondi per coprire completamente il caricamento
    const minTimer = setTimeout(() => {
      if (isAppReady) {
        setIsExiting(true);
        // Wait for exit animation to complete before calling onComplete
        setTimeout(() => {
          setShowSplash(false);
          onComplete();
        }, 800); // Animation duration
      }
    }, 1700);

    // Timer massimo di 6 secondi (fallback) - aumentato per dare più tempo
    const maxTimer = setTimeout(() => {
      setIsExiting(true);
      setTimeout(() => {
        setShowSplash(false);
        onComplete();
      }, 800);
    }, 6000); // Aumentato da 5000 a 6000ms

    return () => {
      clearTimeout(minTimer);
      clearTimeout(maxTimer);
    };
  }, [isAppReady, onComplete]);

  if (!showSplash) return null;

  return (
    <AnimatePresence>
      {showSplash && (
        <motion.div
          initial={{ opacity: 1 }}
          animate={{ opacity: isExiting ? 0 : 1 }}
          exit={{ opacity: 0 }}
          transition={{
            duration: 0.8,
            ease: "easeInOut",
          }}
          className="fixed inset-0 z-50 flex items-center justify-center"
          style={{
            background: `radial-gradient(
              ellipse at center,
              #fefce8 0%,
              #fef3c7 25%,
              #fed7aa 60%,
              #fed7d7 85%,
              #fecaca 100%
            )`,
            backgroundAttachment: "fixed",
          }}
        >
          {/* Pattern di sfondo con loghi */}
          <div
            className="fixed inset-0 pointer-events-none"
            style={{
              backgroundImage:
                'url("/images/logos/logo_bastoni_compressed.png")',
              backgroundRepeat: "space",
              opacity: 0.08,
              filter:
                "sepia(80%) saturate(120%) hue-rotate(15deg) brightness(0.7) contrast(1.1)",
              transform: "rotate(8deg) scale(1.1)",
              transformOrigin: "center center",
              backgroundSize: "60px 60px",
              backgroundPosition: "0 0",
              zIndex: -1,
            }}
          />

          <motion.div
            className="text-center relative z-10"
            initial={{ opacity: 0, y: 20 }}
            animate={{
              opacity: isExiting ? 0 : 1,
              y: isExiting ? -20 : 0,
              scale: isExiting ? 0.9 : 1,
            }}
            transition={{
              duration: 0.8,
              ease: "easeInOut",
            }}
          >
            {/* Logo principale */}
            <motion.div
              className="mb-8"
              animate={{
                y: isExiting ? -30 : [0, -10, 0],
                rotate: isExiting ? -5 : 0,
                scale: isExiting ? 0.8 : 1,
              }}
              transition={{
                duration: isExiting ? 0.8 : 2,
                repeat: isExiting ? 0 : Infinity,
                ease: isExiting ? "easeInOut" : "easeInOut",
              }}
            >
              <img
                src="/images/logos/logo-new_nobg.png"
                alt="Marafone Romagnolo"
                className="w-32 h-32 mx-auto drop-shadow-2xl"
                style={{
                  filter: "drop-shadow(0 12px 24px rgba(0,0,0,0.4))",
                }}
              />
            </motion.div>

            {/* Titolo */}
            <h1
              className="text-4xl font-bold text-romagna-darkWood mb-2 drop-shadow-lg"
              style={{
                fontFamily: "'DynaPuff', cursive",
                textShadow: "2px 2px 4px rgba(0,0,0,0.3)",
              }}
            >
              Marafone
            </h1>
            <h2
              className="text-2xl font-semibold text-amber-700 drop-shadow-md"
              style={{
                fontFamily: "'DynaPuff', cursive",
                textShadow: "1px 1px 2px rgba(0,0,0,0.2)",
              }}
            >
              Romagnolo
            </h2>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SplashScreen;
