# 🎮 Partite Private Multiplayer - Maraffa Romagnola

## 📋 Panoramica

Il sistema di partite private permette ai giocatori di creare stanze private per giocare con amici specifici, con gestione realtime delle connessioni, timer di turno e sincronizzazione automatica.

## 🏗️ Architettura

### Componenti Principali

- **NewMobileMenu**: Menu principale con opzioni multiplayer
- **CreatePrivateGameModal**: Modal per creare partite private
- **JoinPrivateGameModal**: Modal per unirsi a partite private
- **PrivateGameLobby**: Lobby per gestire giocatori e squadre
- **TurnTimer**: Timer di turno con progress bar circolare
- **ConnectionStatus**: Indicatore stato connessioni

### Servizi

- **onlineGameService**: Gestione sessioni di gioco e operazioni CRUD
- **connectionService**: Monitoraggio connessioni e heartbeat
- **Supabase Realtime**: Sincronizzazione in tempo reale

## 🎯 Flusso di Utilizzo

### 1. Creazione Partita Privata

1. Utente clicca "Multiplayer" nel menu principale
2. Seleziona "Crea Partita Privata"
3. Inserisce nome stanza, punti vittoria, timer turno
4. Viene reindirizzato alla lobby come host

### 2. Unirsi a Partita Privata

1. Utente clicca "Multiplayer" nel menu principale
2. Seleziona "Unisciti a Partita Privata"
3. Inserisce nome esatto della stanza
4. Viene reindirizzato alla lobby

### 3. Lobby di Gioco

1. Visualizzazione giocatori connessi
2. Selezione squadre (gialla/rossa)
3. Host può avviare quando 4 giocatori, 2 per squadra
4. Countdown di 5 secondi prima dell'inizio

### 4. Gameplay

1. Timer di turno attivo (default 60s)
2. Auto-play carta casuale allo scadere
3. Monitoraggio connessioni in tempo reale
4. Gestione disconnessioni con sostituzione CPU

## 🗄️ Schema Database

### Tabella `game_sessions`

```sql
- id: UUID (PK)
- created_by: UUID (FK to auth.users)
- status: TEXT ('waiting', 'active', 'completed')
- is_private: BOOLEAN
- room_name: TEXT
- host_id: UUID (FK to auth.users)
- max_players: INTEGER (default 4)
- victory_points: INTEGER (default 31)
- game_timer: INTEGER (default 60)
- expires_at: TIMESTAMP (auto-cleanup dopo 10 min)
- current_state: JSONB
- winner_team: INTEGER (0 o 1)
```

### Tabella `game_players`

```sql
- id: UUID (PK)
- session_id: UUID (FK to game_sessions)
- user_id: UUID (FK to auth.users)
- username: TEXT
- position: TEXT ('north', 'east', 'south', 'west')
- team: INTEGER (0 o 1)
- connected: BOOLEAN
- is_host: BOOLEAN
- joined_at: TIMESTAMP
- last_seen: TIMESTAMP (per heartbeat)
- disconnected_at: TIMESTAMP
```

## 🔧 Configurazione

### Variabili di Ambiente

```typescript
// Timer di turno (secondi)
const DEFAULT_GAME_TIMER = 60;

// Soglia disconnessione (millisecondi)
const DISCONNECTION_THRESHOLD = 60000;

// Intervallo heartbeat (millisecondi)
const HEARTBEAT_INTERVAL = 30000;

// Scadenza stanze (millisecondi)
const ROOM_EXPIRY = 10 * 60 * 1000; // 10 minuti
```

### Row Level Security (RLS)

Le policy RLS garantiscono che:
- Gli utenti possano vedere solo le sessioni di cui fanno parte
- Solo l'host può modificare/eliminare la sessione
- I giocatori possono modificare solo i propri dati

## 🚀 Funzionalità Avanzate

### Timer di Turno

- Progress bar circolare con colori dinamici
- Avviso negli ultimi 10 secondi
- Auto-play carta casuale allo scadere
- Sincronizzazione realtime tra giocatori

### Gestione Connessioni

- Heartbeat ogni 30 secondi
- Rilevamento disconnessioni automatico
- Sostituzione con CPU opzionale
- Riconnessione automatica

### Cleanup Automatico

- Stanze scadute eliminate ogni 5 minuti
- Sessioni vuote eliminate automaticamente
- Controllo giocatori disconnessi ogni 30 secondi

## 🔍 Monitoraggio e Debug

### Log Events

```typescript
// Creazione sessione
console.log('🎮 Creating private game session:', settings);

// Connessioni
console.log('🔌 Player disconnected:', playerId);
console.log('🔌 Player reconnected:', playerId);

// Timer
console.log('⏰ Timer expired, auto-playing card:', card);

// Cleanup
console.log('🧹 Cleaning up expired game sessions...');
```

### Realtime Events

- `player_joined`: Nuovo giocatore nella sessione
- `player_left`: Giocatore ha lasciato la sessione
- `team_changed`: Giocatore ha cambiato squadra
- `game_starting`: Countdown iniziato
- `game_started`: Partita iniziata
- `player_disconnected`: Giocatore disconnesso
- `turn_timer`: Aggiornamenti timer turno

## 🛠️ Manutenzione

### Pulizia Database

```sql
-- Elimina sessioni scadute
DELETE FROM game_sessions 
WHERE is_private = TRUE 
AND status = 'waiting' 
AND expires_at < NOW();

-- Elimina giocatori orfani
DELETE FROM game_players 
WHERE session_id NOT IN (SELECT id FROM game_sessions);
```

### Monitoraggio Performance

- Numero sessioni attive
- Tempo medio di connessione
- Frequenza disconnessioni
- Utilizzo banda Supabase Realtime

## 🔒 Sicurezza

### Validazioni

- Nome stanza: 3-20 caratteri
- Massimo 4 giocatori per stanza
- Solo host può avviare partita
- Verifica appartenenza alla sessione

### Rate Limiting

- Creazione stanze: max 5 per utente/ora
- Heartbeat: max 1 ogni 10 secondi
- Riconnessioni: backoff esponenziale

## 📱 Compatibilità Mobile

- Layout responsive per tutti i componenti
- Touch-friendly per selezione squadre
- Gestione orientamento schermo
- Ottimizzazione batteria per heartbeat

## 🎨 UI/UX

### Design System

- Colori squadre: Giallo (#FCD34D) e Rosso (#EF4444)
- Font: DynaPuff per titoli
- Animazioni: Transizioni smooth 200ms
- Feedback visivo per tutte le azioni

### Accessibilità

- Contrasto colori WCAG AA
- Supporto screen reader
- Navigazione da tastiera
- Indicatori stato chiari

## 🚦 Stati dell'Applicazione

### Lobby States

- `waiting`: In attesa giocatori
- `ready`: 4 giocatori, squadre bilanciate
- `starting`: Countdown attivo
- `error`: Errore configurazione

### Connection States

- `online`: Connesso e sincronizzato
- `offline`: Disconnesso dalla rete
- `reconnecting`: Tentativo riconnessione
- `failed`: Riconnessione fallita

### Game States

- `lobby`: In lobby
- `playing`: Partita in corso
- `paused`: Partita in pausa (disconnessioni)
- `completed`: Partita terminata
