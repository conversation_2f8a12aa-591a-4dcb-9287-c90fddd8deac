import React, { useEffect, useState, useCallback } from "react";
import { Clock } from "lucide-react";
import { getTimerColor, MULTIPLAYER_CONFIG } from "@/config/multiplayer";

interface TurnTimerProps {
  isActive: boolean;
  duration: number; // seconds
  currentPlayer: number;
  playerName?: string;
  onTimeUp: () => void;
  onTick?: (timeRemaining: number) => void;
}

const TurnTimer: React.FC<TurnTimerProps> = ({
  isActive,
  duration,
  currentPlayer,
  playerName,
  onTimeUp,
  onTick,
}) => {
  const [timeRemaining, setTimeRemaining] = useState(duration);
  const [isRunning, setIsRunning] = useState(false);

  // Reset timer when active state changes or current player changes
  useEffect(() => {
    if (isActive) {
      setTimeRemaining(duration);
      setIsRunning(true);
    } else {
      setIsRunning(false);
    }
  }, [isActive, currentPlayer, duration]);

  // Timer countdown logic
  useEffect(() => {
    if (!isRunning || !isActive) return;

    const interval = setInterval(() => {
      setTimeRemaining((prev) => {
        const newTime = prev - 1;

        // Call onTick callback
        if (onTick) {
          onTick(newTime);
        }

        // Time's up
        if (newTime <= 0) {
          setIsRunning(false);
          onTimeUp();
          return 0;
        }

        return newTime;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isRunning, isActive, onTimeUp, onTick]);

  // Calculate progress percentage
  const progress = (timeRemaining / duration) * 100;

  // Get timer color based on remaining time
  const timerColor = getTimerColor(timeRemaining, duration);

  // Format time display
  const formatTime = (seconds: number) => {
    return `${seconds}s`;
  };

  if (!isActive) return null;

  return (
    <div className="fixed top-4 right-4 z-40">
      <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-xl border-2 border-white/50">
        <div className="flex items-center gap-3">
          {/* Circular Progress */}
          <div className="relative w-16 h-16">
            <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
              {/* Background circle */}
              <circle
                cx="32"
                cy="32"
                r="28"
                fill="none"
                stroke="currentColor"
                strokeWidth="4"
                className="text-gray-200"
              />
              {/* Progress circle */}
              <circle
                cx="32"
                cy="32"
                r="28"
                fill="none"
                stroke="currentColor"
                strokeWidth="4"
                strokeLinecap="round"
                className={timerColor}
                style={{
                  strokeDasharray: `${2 * Math.PI * 28}`,
                  strokeDashoffset: `${
                    2 * Math.PI * 28 * (1 - progress / 100)
                  }`,
                  transition: "stroke-dashoffset 1s linear",
                }}
              />
            </svg>

            {/* Time display */}
            <div className="absolute inset-0 flex items-center justify-center">
              <span className={`text-lg font-bold ${timerColor}`}>
                {formatTime(timeRemaining)}
              </span>
            </div>
          </div>

          {/* Player info */}
          <div className="flex flex-col">
            <div className="flex items-center gap-2 text-gray-700">
              <Clock className="h-4 w-4" />
              <span className="text-sm font-semibold">Turno di:</span>
            </div>
            <span className="text-lg font-bold text-gray-800">
              {playerName || `Giocatore ${currentPlayer + 1}`}
            </span>
          </div>
        </div>

        {/* Warning message for low time */}
        {timeRemaining <= MULTIPLAYER_CONFIG.TIMER_WARNING_THRESHOLD && (
          <div className="mt-2 text-center">
            <span className="text-xs text-red-600 font-semibold animate-pulse">
              Tempo quasi scaduto!
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default TurnTimer;
