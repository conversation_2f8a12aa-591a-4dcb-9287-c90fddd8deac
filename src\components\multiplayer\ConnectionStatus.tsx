import React from 'react';
import { Wifi, WifiOff, Users, AlertTriangle } from 'lucide-react';
import { GamePlayer } from '@/types/game';

interface ConnectionStatusProps {
  isConnected: boolean;
  players: GamePlayer[];
  disconnectedPlayers: string[];
  className?: string;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  players,
  disconnectedPlayers,
  className = '',
}) => {
  const connectedPlayersCount = players.filter(p => p.connected).length;
  const totalPlayers = players.length;

  // Don't show if all players are connected and user is online
  if (isConnected && disconnectedPlayers.length === 0) {
    return null;
  }

  return (
    <div className={`fixed top-4 left-4 z-40 ${className}`}>
      <div className="bg-white/90 backdrop-blur-sm rounded-xl p-3 shadow-lg border-2 border-white/50">
        <div className="flex items-center gap-3">
          {/* Connection Icon */}
          <div className="flex items-center gap-2">
            {isConnected ? (
              <Wifi className="h-5 w-5 text-green-500" />
            ) : (
              <WifiOff className="h-5 w-5 text-red-500" />
            )}
            <span className={`text-sm font-semibold ${
              isConnected ? 'text-green-700' : 'text-red-700'
            }`}>
              {isConnected ? 'Online' : 'Offline'}
            </span>
          </div>

          {/* Players Status */}
          <div className="flex items-center gap-2 border-l border-gray-300 pl-3">
            <Users className="h-4 w-4 text-gray-600" />
            <span className="text-sm text-gray-700">
              {connectedPlayersCount}/{totalPlayers}
            </span>
          </div>

          {/* Disconnection Warning */}
          {disconnectedPlayers.length > 0 && (
            <div className="flex items-center gap-2 border-l border-gray-300 pl-3">
              <AlertTriangle className="h-4 w-4 text-amber-500" />
              <span className="text-sm text-amber-700">
                {disconnectedPlayers.length} disconnessi
              </span>
            </div>
          )}
        </div>

        {/* Disconnected Players List */}
        {disconnectedPlayers.length > 0 && (
          <div className="mt-2 pt-2 border-t border-gray-200">
            <p className="text-xs text-gray-600 mb-1">Giocatori disconnessi:</p>
            <div className="space-y-1">
              {disconnectedPlayers.map(playerId => {
                const player = players.find(p => p.user_id === playerId);
                return (
                  <div key={playerId} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-xs text-gray-700">
                      {player?.username || 'Giocatore sconosciuto'}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Offline Message */}
        {!isConnected && (
          <div className="mt-2 pt-2 border-t border-gray-200">
            <p className="text-xs text-red-600">
              Connessione persa. Tentativo di riconnessione...
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConnectionStatus;
