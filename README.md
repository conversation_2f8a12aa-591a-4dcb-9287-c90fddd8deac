# 🐓 Marafone Romagnolo

Un'implementazione digitale moderna del tradizionale gioco di carte romagnolo "Marafone", sviluppa<PERSON> con React, TypeScript e tecnologie all'avanguardia.

---

## 🚀 Quick Start

```bash
# Clona il repository
git clone <YOUR_GIT_URL>
cd marafone-romagnolo

# Installa dipendenze
npm install

# Avvia sviluppo
npm run dev

# Build produzione
npm run build
```

**📱 Build Android APK:**

```powershell
.\scripts\build-apk.ps1
```

---

## 🎮 Caratteristiche Principali

### ✨ **Gioco Autentico**

- 🎯 **Regole fedeli** del Marafone Romagnolo tradizionale
- 🃏 **40 carte** con semi tradizionali (Bastoni, Coppe, Denari, Spade)
- 🏆 **Sistema punteggi** completo con Marafone (A-2-3 di briscola)
- 📢 **Annunci strategici** (Busso, Striscio, Volo)

### 🤖 **AI Avanzata**

- 🎚️ **3 livelli difficoltà**: Principiante, Esperto, Maestro
- 🧠 **Strategia adattiva** basata su situazione di gioco
- 🤝 **Cooperazione intelligente** tra compagni di squadra
- 📊 **Memoria di gioco** per decisioni strategiche

### 🎨 **Design & UX**

- 🏞️ **Tema rustico romagnolo** con texture autentiche
- 📱 **Mobile-first** responsive per tutti i dispositivi
- ⚡ **Performance ottimizzate** con cache intelligente
- 🎭 **Animazioni fluide** e feedback visivi
- 🌐 **PWA Ready** - installabile come app nativa

---

## 🛠️ Sviluppo e Contributi

### 💻 **Opzioni di Sviluppo**

| Metodo                   | Descrizione                 | Requisiti                                                                                 |
| ------------------------ | --------------------------- | ----------------------------------------------------------------------------------------- |
| **🖥️ IDE Locale**        | Clona e sviluppa localmente | Node.js + npm ([installa con nvm](https://github.com/nvm-sh/nvm#installing-and-updating)) |
| **🌐 GitHub Codespaces** | Ambiente cloud integrato    | Account GitHub                                                                            |
| **✏️ Edit Diretto**      | Modifica file su GitHub     | Account GitHub                                                                            |

### 🚀 **Setup Locale**

```bash
# 1. Clona repository
git clone <YOUR_GIT_URL>
cd marafone-romagnolo

# 2. Installa dipendenze
npm install

# 3. Avvia sviluppo (con hot-reload)
npm run dev

# 4. Build produzione
npm run build

# 5. Preview build
npm run preview
```

### 📱 **Build Mobile (Android)**

```powershell
# Script PowerShell ottimizzato (raccomandato)
.\scripts\build-apk.ps1

# Script batch alternativo
.\scripts\build-apk.bat
```

**Requisiti Android:**

- Android Studio + SDK
- Java Development Kit (JDK)
- Capacitor CLI: `npm install -g @capacitor/cli`

## 🏗️ Architettura del Progetto

```
├── 📁 docs/              # Documentazione completa
│   ├── GUIDA_REPOSITORY.md      # Architettura e guida sviluppatori
│   ├── logica-di-gioco.md       # Regole e logica implementata
│   └── OTTIMIZZAZIONI_IMMAGINI.md # Performance e ottimizzazioni
├── 📁 scripts/           # Script di utilità e build
├── 📁 src/
│   ├── 📁 components/     # Componenti React
│   ├── 📁 utils/
│   │   ├── 📁 game/       # Logica di gioco e carte
│   │   ├── 📁 ai/         # Sistema di intelligenza artificiale
│   │   └── 📁 ui/         # Utility UI e ottimizzazioni immagini
│   ├── 📁 pages/          # Pagine dell'applicazione
│   ├── 📁 hooks/          # Custom React hooks
│   └── 📁 types/          # Definizioni TypeScript
└── 📁 public/
    └── 📁 images/         # Assets grafici (carte, loghi, semi)
```

## 🎯 Funzionalità Principali

### 🎲 Sistema di Gioco

- **Regole autentiche** del Marafone Romagnolo
- **Gestione completa** di punteggi, round e partite
- **Sistema di annunci** (busso, striscio, volo)
- **Logica Marafone** (asso, due, tre di briscola)

### 🤖 Intelligenza Artificiale

- **3 livelli di difficoltà** con strategie diverse
- **AI adattiva** che analizza la situazione di gioco
- **Gestione avanzata** di cooperazione tra compagni di squadra

### 🎨 Design e UX

- **Tema rustico romagnolo** con texture autentiche
- **Animazioni fluide** e feedback visivi
- **Design responsive** ottimizzato per mobile e desktop
- **Accessibilità** con indicatori chiari per azioni disponibili

## 🛠️ Tecnologie Utilizzate

### Core

- **React 18** - Libreria UI moderna
- **TypeScript** - Type safety e developer experience
- **Vite** - Build tool veloce e moderno

### Styling

- **Tailwind CSS** - Framework CSS utility-first
- **shadcn/ui** - Componenti UI pre-costruiti
- **CSS Custom Properties** - Variabili per temi personalizzati

### Build e Deploy

- **Capacitor** - Wrapper per app native
- **Android SDK** - Build APK per dispositivi Android
- **PWA** - Progressive Web App capabilities

## 📖 Documentazione

La documentazione completa del progetto è disponibile nella cartella `docs/`:

- **[📚 Indice Documentazione](./docs/README.md)** - Panoramica completa di tutta la documentazione
- **[📋 Panoramica App](./docs/APP_OVERVIEW.md)** - Funzionalità e caratteristiche principali
- **[🏗️ Guida Repository](./docs/GUIDA_REPOSITORY.md)** - Architettura e guida per sviluppatori
- **[🎮 Logica di Gioco](./docs/logica-di-gioco.md)** - Regole e implementazione del gioco
- **[⚡ Ottimizzazioni](./docs/OTTIMIZZAZIONI_IMMAGINI.md)** - Sistema di performance avanzato

### 🛠️ Per Sviluppatori

- **[Build Scripts](./docs/BUILD_SCRIPTS.md)** - Tool di sviluppo e deployment
- **[Assets Guide](./docs/IMAGE_ASSETS.md)** - Gestione immagini e risorse

### 📱 Per Publishing

- **[Play Store](./docs/PLAY_STORE_LISTING.md)** - Informazioni per la pubblicazione
- **[Privacy Policy](./docs/PRIVACY_POLICY.md)** - Policy sulla privacy
