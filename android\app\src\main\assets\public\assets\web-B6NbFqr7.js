import{W as a}from"./game-hrinnRPS.js";import"./audioManager-oMWoQbcF.js";import"./audio-5UOY9KLB.js";import"./vendor-COrNHRvO.js";class s extends a{async canShare(){return typeof navigator>"u"||!navigator.share?{value:!1}:{value:!0}}async share(e){if(typeof navigator>"u"||!navigator.share)throw this.unavailable("Share API not available in this browser");return await navigator.share({title:e.title,text:e.text,url:e.url}),{}}}export{s as ShareWeb};
