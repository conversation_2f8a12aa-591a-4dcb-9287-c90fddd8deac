import{r as f,a as Lr,R as _r,b as Es,d as j}from"./vendor-COrNHRvO.js";import{_ as le,a as Nr,b as Ts}from"./audio-5UOY9KLB.js";var $r={exports:{}},St={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ps=f,As=Symbol.for("react.element"),Ms=Symbol.for("react.fragment"),Rs=Object.prototype.hasOwnProperty,ks=Ps.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Os={key:!0,ref:!0,__self:!0,__source:!0};function jr(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Rs.call(t,r)&&!Os.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:As,type:e,key:i,ref:s,props:o,_owner:ks.current}}St.Fragment=Ms;St.jsx=jr;St.jsxs=jr;$r.exports=St;var y=$r.exports;/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Is=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),zr=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ds={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ls=f.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:s,...a},l)=>f.createElement("svg",{ref:l,...Ds,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:zr("lucide",o),...a},[...s.map(([c,d])=>f.createElement(c,d)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=(e,t)=>{const n=f.forwardRef(({className:r,...o},i)=>f.createElement(Ls,{ref:i,iconNode:t,className:zr(`lucide-${Is(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const up=D("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fp=D("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pp=D("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hp=D("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mp=D("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vp=D("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gp=D("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wp=D("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yp=D("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xp=D("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sp=D("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bp=D("CirclePlay",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"10 8 16 12 10 16 10 8",key:"1cimsy"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cp=D("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ep=D("Clapperboard",[["path",{d:"M20.2 6 3 11l-.9-2.4c-.3-1.1.3-2.2 1.3-2.5l13.5-4c1.1-.3 2.2.3 2.5 1.3Z",key:"1tn4o7"}],["path",{d:"m6.2 5.3 3.1 3.9",key:"iuk76l"}],["path",{d:"m12.4 3.4 3.1 4",key:"6hsd6n"}],["path",{d:"M3 11h18v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2Z",key:"ltgou9"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tp=D("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pp=D("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ap=D("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mp=D("Diamond",[["path",{d:"M2.7 10.3a2.41 2.41 0 0 0 0 3.41l7.59 7.59a2.41 2.41 0 0 0 3.41 0l7.59-7.59a2.41 2.41 0 0 0 0-3.41l-7.59-7.59a2.41 2.41 0 0 0-3.41 0Z",key:"1f1r0c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rp=D("Dices",[["rect",{width:"12",height:"12",x:"2",y:"10",rx:"2",ry:"2",key:"6agr2n"}],["path",{d:"m17.92 14 3.5-3.5a2.24 2.24 0 0 0 0-3l-5-4.92a2.24 2.24 0 0 0-3 0L10 6",key:"1o487t"}],["path",{d:"M6 18h.01",key:"uhywen"}],["path",{d:"M10 14h.01",key:"ssrbsk"}],["path",{d:"M15 6h.01",key:"cblpky"}],["path",{d:"M18 9h.01",key:"2061c0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kp=D("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Op=D("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ip=D("Gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dp=D("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lp=D("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _p=D("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Np=D("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $p=D("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jp=D("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zp=D("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fp=D("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vp=D("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bp=D("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hp=D("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wp=D("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gp=D("RefreshCcw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Up=D("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yp=D("ScrollText",[["path",{d:"M15 12h-5",key:"r7krc0"}],["path",{d:"M15 8h-5",key:"1khuty"}],["path",{d:"M19 17V5a2 2 0 0 0-2-2H4",key:"zz82l3"}],["path",{d:"M8 21h12a2 2 0 0 0 2-2v-1a1 1 0 0 0-1-1H11a1 1 0 0 0-1 1v1a2 2 0 1 1-4 0V5a2 2 0 1 0-4 0v2a1 1 0 0 0 1 1h3",key:"1ph1d7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xp=D("Scroll",[["path",{d:"M19 17V5a2 2 0 0 0-2-2H4",key:"zz82l3"}],["path",{d:"M8 21h12a2 2 0 0 0 2-2v-1a1 1 0 0 0-1-1H11a1 1 0 0 0-1 1v1a2 2 0 1 1-4 0V5a2 2 0 1 0-4 0v2a1 1 0 0 0 1 1h3",key:"1ph1d7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qp=D("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kp=D("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zp=D("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qp=D("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jp=D("Shuffle",[["path",{d:"m18 14 4 4-4 4",key:"10pe0f"}],["path",{d:"m18 2 4 4-4 4",key:"pucp1d"}],["path",{d:"M2 18h1.973a4 4 0 0 0 3.3-1.7l5.454-8.6a4 4 0 0 1 3.3-1.7H22",key:"1ailkh"}],["path",{d:"M2 6h1.972a4 4 0 0 1 3.6 2.2",key:"km57vx"}],["path",{d:"M22 18h-6.041a4 4 0 0 1-3.3-1.8l-.359-.45",key:"os18l9"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eh=D("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const th=D("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nh=D("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rh=D("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oh=D("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ih=D("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sh=D("UserMinus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ah=D("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lh=D("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ch=D("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dh=D("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);function V(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function _s(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Fr(...e){return t=>e.forEach(n=>_s(n,t))}function B(...e){return f.useCallback(Fr(...e),e)}function Ns(e,t){const n=f.createContext(t),r=i=>{const{children:s,...a}=i,l=f.useMemo(()=>a,Object.values(a));return y.jsx(n.Provider,{value:l,children:s})};r.displayName=e+"Provider";function o(i){const s=f.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,o]}function be(e,t=[]){let n=[];function r(i,s){const a=f.createContext(s),l=n.length;n=[...n,s];const c=u=>{var g;const{scope:p,children:h,...v}=u,m=((g=p==null?void 0:p[e])==null?void 0:g[l])||a,w=f.useMemo(()=>v,Object.values(v));return y.jsx(m.Provider,{value:w,children:h})};c.displayName=i+"Provider";function d(u,p){var m;const h=((m=p==null?void 0:p[e])==null?void 0:m[l])||a,v=f.useContext(h);if(v)return v;if(s!==void 0)return s;throw new Error(`\`${u}\` must be used within \`${i}\``)}return[c,d]}const o=()=>{const i=n.map(s=>f.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return f.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,$s(o,...t)]}function $s(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:c})=>{const u=l(i)[`__scope${c}`];return{...a,...u}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var He=f.forwardRef((e,t)=>{const{children:n,...r}=e,o=f.Children.toArray(n),i=o.find(js);if(i){const s=i.props.children,a=o.map(l=>l===i?f.Children.count(s)>1?f.Children.only(null):f.isValidElement(s)?s.props.children:null:l);return y.jsx(Kt,{...r,ref:t,children:f.isValidElement(s)?f.cloneElement(s,void 0,a):null})}return y.jsx(Kt,{...r,ref:t,children:n})});He.displayName="Slot";var Kt=f.forwardRef((e,t)=>{const{children:n,...r}=e;if(f.isValidElement(n)){const o=Fs(n);return f.cloneElement(n,{...zs(r,n.props),ref:t?Fr(t,o):o})}return f.Children.count(n)>1?f.Children.only(null):null});Kt.displayName="SlotClone";var hn=({children:e})=>y.jsx(y.Fragment,{children:e});function js(e){return f.isValidElement(e)&&e.type===hn}function zs(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function Fs(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Vs=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],F=Vs.reduce((e,t)=>{const n=f.forwardRef((r,o)=>{const{asChild:i,...s}=r,a=i?He:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),y.jsx(a,{...s,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Bs(e,t){e&&Lr.flushSync(()=>e.dispatchEvent(t))}function ue(e){const t=f.useRef(e);return f.useEffect(()=>{t.current=e}),f.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Hs(e,t=globalThis==null?void 0:globalThis.document){const n=ue(e);f.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Ws="DismissableLayer",Zt="dismissableLayer.update",Gs="dismissableLayer.pointerDownOutside",Us="dismissableLayer.focusOutside",Jn,Vr=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),mn=f.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:a,...l}=e,c=f.useContext(Vr),[d,u]=f.useState(null),p=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,h]=f.useState({}),v=B(t,P=>u(P)),m=Array.from(c.layers),[w]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),g=m.indexOf(w),x=d?m.indexOf(d):-1,S=c.layersWithOutsidePointerEventsDisabled.size>0,b=x>=g,C=qs(P=>{const M=P.target,R=[...c.branches].some(E=>E.contains(M));!b||R||(o==null||o(P),s==null||s(P),P.defaultPrevented||a==null||a())},p),T=Ks(P=>{const M=P.target;[...c.branches].some(E=>E.contains(M))||(i==null||i(P),s==null||s(P),P.defaultPrevented||a==null||a())},p);return Hs(P=>{x===c.layers.size-1&&(r==null||r(P),!P.defaultPrevented&&a&&(P.preventDefault(),a()))},p),f.useEffect(()=>{if(d)return n&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(Jn=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(d)),c.layers.add(d),er(),()=>{n&&c.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=Jn)}},[d,p,n,c]),f.useEffect(()=>()=>{d&&(c.layers.delete(d),c.layersWithOutsidePointerEventsDisabled.delete(d),er())},[d,c]),f.useEffect(()=>{const P=()=>h({});return document.addEventListener(Zt,P),()=>document.removeEventListener(Zt,P)},[]),y.jsx(F.div,{...l,ref:v,style:{pointerEvents:S?b?"auto":"none":void 0,...e.style},onFocusCapture:V(e.onFocusCapture,T.onFocusCapture),onBlurCapture:V(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:V(e.onPointerDownCapture,C.onPointerDownCapture)})});mn.displayName=Ws;var Ys="DismissableLayerBranch",Xs=f.forwardRef((e,t)=>{const n=f.useContext(Vr),r=f.useRef(null),o=B(t,r);return f.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),y.jsx(F.div,{...e,ref:o})});Xs.displayName=Ys;function qs(e,t=globalThis==null?void 0:globalThis.document){const n=ue(e),r=f.useRef(!1),o=f.useRef(()=>{});return f.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let l=function(){Br(Gs,n,c,{discrete:!0})};const c={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Ks(e,t=globalThis==null?void 0:globalThis.document){const n=ue(e),r=f.useRef(!1);return f.useEffect(()=>{const o=i=>{i.target&&!r.current&&Br(Us,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function er(){const e=new CustomEvent(Zt);document.dispatchEvent(e)}function Br(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Bs(o,i):o.dispatchEvent(i)}var fe=globalThis!=null&&globalThis.document?f.useLayoutEffect:()=>{},Zs=_r.useId||(()=>{}),Qs=0;function Be(e){const[t,n]=f.useState(Zs());return fe(()=>{n(r=>r??String(Qs++))},[e]),t?`radix-${t}`:""}const Js=["top","right","bottom","left"],pe=Math.min,U=Math.max,dt=Math.round,Ze=Math.floor,he=e=>({x:e,y:e}),ea={left:"right",right:"left",bottom:"top",top:"bottom"},ta={start:"end",end:"start"};function Qt(e,t,n){return U(e,pe(t,n))}function re(e,t){return typeof e=="function"?e(t):e}function oe(e){return e.split("-")[0]}function $e(e){return e.split("-")[1]}function vn(e){return e==="x"?"y":"x"}function gn(e){return e==="y"?"height":"width"}function me(e){return["top","bottom"].includes(oe(e))?"y":"x"}function wn(e){return vn(me(e))}function na(e,t,n){n===void 0&&(n=!1);const r=$e(e),o=wn(e),i=gn(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=ut(s)),[s,ut(s)]}function ra(e){const t=ut(e);return[Jt(e),t,Jt(t)]}function Jt(e){return e.replace(/start|end/g,t=>ta[t])}function oa(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}function ia(e,t,n,r){const o=$e(e);let i=oa(oe(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(Jt)))),i}function ut(e){return e.replace(/left|right|bottom|top/g,t=>ea[t])}function sa(e){return{top:0,right:0,bottom:0,left:0,...e}}function Hr(e){return typeof e!="number"?sa(e):{top:e,right:e,bottom:e,left:e}}function ft(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function tr(e,t,n){let{reference:r,floating:o}=e;const i=me(t),s=wn(t),a=gn(s),l=oe(t),c=i==="y",d=r.x+r.width/2-o.width/2,u=r.y+r.height/2-o.height/2,p=r[a]/2-o[a]/2;let h;switch(l){case"top":h={x:d,y:r.y-o.height};break;case"bottom":h={x:d,y:r.y+r.height};break;case"right":h={x:r.x+r.width,y:u};break;case"left":h={x:r.x-o.width,y:u};break;default:h={x:r.x,y:r.y}}switch($e(t)){case"start":h[s]-=p*(n&&c?-1:1);break;case"end":h[s]+=p*(n&&c?-1:1);break}return h}const aa=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(t));let c=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:u}=tr(c,r,l),p=r,h={},v=0;for(let m=0;m<a.length;m++){const{name:w,fn:g}=a[m],{x,y:S,data:b,reset:C}=await g({x:d,y:u,initialPlacement:r,placement:p,strategy:o,middlewareData:h,rects:c,platform:s,elements:{reference:e,floating:t}});d=x??d,u=S??u,h={...h,[w]:{...h[w],...b}},C&&v<=50&&(v++,typeof C=="object"&&(C.placement&&(p=C.placement),C.rects&&(c=C.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:d,y:u}=tr(c,p,l)),m=-1)}return{x:d,y:u,placement:p,strategy:o,middlewareData:h}};async function We(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:a,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:p=!1,padding:h=0}=re(t,e),v=Hr(h),w=a[p?u==="floating"?"reference":"floating":u],g=ft(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(w)))==null||n?w:w.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:d,strategy:l})),x=u==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,S=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),b=await(i.isElement==null?void 0:i.isElement(S))?await(i.getScale==null?void 0:i.getScale(S))||{x:1,y:1}:{x:1,y:1},C=ft(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:x,offsetParent:S,strategy:l}):x);return{top:(g.top-C.top+v.top)/b.y,bottom:(C.bottom-g.bottom+v.bottom)/b.y,left:(g.left-C.left+v.left)/b.x,right:(C.right-g.right+v.right)/b.x}}const la=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:a,middlewareData:l}=t,{element:c,padding:d=0}=re(e,t)||{};if(c==null)return{};const u=Hr(d),p={x:n,y:r},h=wn(o),v=gn(h),m=await s.getDimensions(c),w=h==="y",g=w?"top":"left",x=w?"bottom":"right",S=w?"clientHeight":"clientWidth",b=i.reference[v]+i.reference[h]-p[h]-i.floating[v],C=p[h]-i.reference[h],T=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c));let P=T?T[S]:0;(!P||!await(s.isElement==null?void 0:s.isElement(T)))&&(P=a.floating[S]||i.floating[v]);const M=b/2-C/2,R=P/2-m[v]/2-1,E=pe(u[g],R),A=pe(u[x],R),O=E,L=P-m[v]-A,N=P/2-m[v]/2+M,z=Qt(O,N,L),k=!l.arrow&&$e(o)!=null&&N!==z&&i.reference[v]/2-(N<O?E:A)-m[v]/2<0,_=k?N<O?N-O:N-L:0;return{[h]:p[h]+_,data:{[h]:z,centerOffset:N-z-_,...k&&{alignmentOffset:_}},reset:k}}}),ca=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:a,platform:l,elements:c}=t,{mainAxis:d=!0,crossAxis:u=!0,fallbackPlacements:p,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:m=!0,...w}=re(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const g=oe(o),x=me(a),S=oe(a)===a,b=await(l.isRTL==null?void 0:l.isRTL(c.floating)),C=p||(S||!m?[ut(a)]:ra(a)),T=v!=="none";!p&&T&&C.push(...ia(a,m,v,b));const P=[a,...C],M=await We(t,w),R=[];let E=((r=i.flip)==null?void 0:r.overflows)||[];if(d&&R.push(M[g]),u){const N=na(o,s,b);R.push(M[N[0]],M[N[1]])}if(E=[...E,{placement:o,overflows:R}],!R.every(N=>N<=0)){var A,O;const N=(((A=i.flip)==null?void 0:A.index)||0)+1,z=P[N];if(z)return{data:{index:N,overflows:E},reset:{placement:z}};let k=(O=E.filter(_=>_.overflows[0]<=0).sort((_,I)=>_.overflows[1]-I.overflows[1])[0])==null?void 0:O.placement;if(!k)switch(h){case"bestFit":{var L;const _=(L=E.filter(I=>{if(T){const $=me(I.placement);return $===x||$==="y"}return!0}).map(I=>[I.placement,I.overflows.filter($=>$>0).reduce(($,W)=>$+W,0)]).sort((I,$)=>I[1]-$[1])[0])==null?void 0:L[0];_&&(k=_);break}case"initialPlacement":k=a;break}if(o!==k)return{reset:{placement:k}}}return{}}}};function nr(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function rr(e){return Js.some(t=>e[t]>=0)}const da=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=re(e,t);switch(r){case"referenceHidden":{const i=await We(t,{...o,elementContext:"reference"}),s=nr(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:rr(s)}}}case"escaped":{const i=await We(t,{...o,altBoundary:!0}),s=nr(i,n.floating);return{data:{escapedOffsets:s,escaped:rr(s)}}}default:return{}}}}};async function ua(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=oe(n),a=$e(n),l=me(n)==="y",c=["left","top"].includes(s)?-1:1,d=i&&l?-1:1,u=re(t,e);let{mainAxis:p,crossAxis:h,alignmentAxis:v}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return a&&typeof v=="number"&&(h=a==="end"?v*-1:v),l?{x:h*d,y:p*c}:{x:p*c,y:h*d}}const fa=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:a}=t,l=await ua(t,e);return s===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:s}}}}},pa=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:w=>{let{x:g,y:x}=w;return{x:g,y:x}}},...l}=re(e,t),c={x:n,y:r},d=await We(t,l),u=me(oe(o)),p=vn(u);let h=c[p],v=c[u];if(i){const w=p==="y"?"top":"left",g=p==="y"?"bottom":"right",x=h+d[w],S=h-d[g];h=Qt(x,h,S)}if(s){const w=u==="y"?"top":"left",g=u==="y"?"bottom":"right",x=v+d[w],S=v-d[g];v=Qt(x,v,S)}const m=a.fn({...t,[p]:h,[u]:v});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[p]:i,[u]:s}}}}}},ha=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:a=0,mainAxis:l=!0,crossAxis:c=!0}=re(e,t),d={x:n,y:r},u=me(o),p=vn(u);let h=d[p],v=d[u];const m=re(a,t),w=typeof m=="number"?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){const S=p==="y"?"height":"width",b=i.reference[p]-i.floating[S]+w.mainAxis,C=i.reference[p]+i.reference[S]-w.mainAxis;h<b?h=b:h>C&&(h=C)}if(c){var g,x;const S=p==="y"?"width":"height",b=["top","left"].includes(oe(o)),C=i.reference[u]-i.floating[S]+(b&&((g=s.offset)==null?void 0:g[u])||0)+(b?0:w.crossAxis),T=i.reference[u]+i.reference[S]+(b?0:((x=s.offset)==null?void 0:x[u])||0)-(b?w.crossAxis:0);v<C?v=C:v>T&&(v=T)}return{[p]:h,[u]:v}}}},ma=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:a}=t,{apply:l=()=>{},...c}=re(e,t),d=await We(t,c),u=oe(o),p=$e(o),h=me(o)==="y",{width:v,height:m}=i.floating;let w,g;u==="top"||u==="bottom"?(w=u,g=p===(await(s.isRTL==null?void 0:s.isRTL(a.floating))?"start":"end")?"left":"right"):(g=u,w=p==="end"?"top":"bottom");const x=m-d.top-d.bottom,S=v-d.left-d.right,b=pe(m-d[w],x),C=pe(v-d[g],S),T=!t.middlewareData.shift;let P=b,M=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(M=S),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(P=x),T&&!p){const E=U(d.left,0),A=U(d.right,0),O=U(d.top,0),L=U(d.bottom,0);h?M=v-2*(E!==0||A!==0?E+A:U(d.left,d.right)):P=m-2*(O!==0||L!==0?O+L:U(d.top,d.bottom))}await l({...t,availableWidth:M,availableHeight:P});const R=await s.getDimensions(a.floating);return v!==R.width||m!==R.height?{reset:{rects:!0}}:{}}}};function bt(){return typeof window<"u"}function je(e){return Wr(e)?(e.nodeName||"").toLowerCase():"#document"}function Y(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ee(e){var t;return(t=(Wr(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Wr(e){return bt()?e instanceof Node||e instanceof Y(e).Node:!1}function K(e){return bt()?e instanceof Element||e instanceof Y(e).Element:!1}function J(e){return bt()?e instanceof HTMLElement||e instanceof Y(e).HTMLElement:!1}function or(e){return!bt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Y(e).ShadowRoot}function Ue(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function va(e){return["table","td","th"].includes(je(e))}function Ct(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function yn(e){const t=xn(),n=K(e)?Z(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function ga(e){let t=ve(e);for(;J(t)&&!Le(t);){if(yn(t))return t;if(Ct(t))return null;t=ve(t)}return null}function xn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Le(e){return["html","body","#document"].includes(je(e))}function Z(e){return Y(e).getComputedStyle(e)}function Et(e){return K(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ve(e){if(je(e)==="html")return e;const t=e.assignedSlot||e.parentNode||or(e)&&e.host||ee(e);return or(t)?t.host:t}function Gr(e){const t=ve(e);return Le(t)?e.ownerDocument?e.ownerDocument.body:e.body:J(t)&&Ue(t)?t:Gr(t)}function Ge(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Gr(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=Y(o);if(i){const a=en(s);return t.concat(s,s.visualViewport||[],Ue(o)?o:[],a&&n?Ge(a):[])}return t.concat(o,Ge(o,[],n))}function en(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Ur(e){const t=Z(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=J(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,a=dt(n)!==i||dt(r)!==s;return a&&(n=i,r=s),{width:n,height:r,$:a}}function Sn(e){return K(e)?e:e.contextElement}function Oe(e){const t=Sn(e);if(!J(t))return he(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=Ur(t);let s=(i?dt(n.width):n.width)/r,a=(i?dt(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const wa=he(0);function Yr(e){const t=Y(e);return!xn()||!t.visualViewport?wa:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function ya(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Y(e)?!1:t}function xe(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=Sn(e);let s=he(1);t&&(r?K(r)&&(s=Oe(r)):s=Oe(e));const a=ya(i,n,r)?Yr(i):he(0);let l=(o.left+a.x)/s.x,c=(o.top+a.y)/s.y,d=o.width/s.x,u=o.height/s.y;if(i){const p=Y(i),h=r&&K(r)?Y(r):r;let v=p,m=en(v);for(;m&&r&&h!==v;){const w=Oe(m),g=m.getBoundingClientRect(),x=Z(m),S=g.left+(m.clientLeft+parseFloat(x.paddingLeft))*w.x,b=g.top+(m.clientTop+parseFloat(x.paddingTop))*w.y;l*=w.x,c*=w.y,d*=w.x,u*=w.y,l+=S,c+=b,v=Y(m),m=en(v)}}return ft({width:d,height:u,x:l,y:c})}function xa(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=ee(r),a=t?Ct(t.floating):!1;if(r===s||a&&i)return n;let l={scrollLeft:0,scrollTop:0},c=he(1);const d=he(0),u=J(r);if((u||!u&&!i)&&((je(r)!=="body"||Ue(s))&&(l=Et(r)),J(r))){const p=xe(r);c=Oe(r),d.x=p.x+r.clientLeft,d.y=p.y+r.clientTop}return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+d.x,y:n.y*c.y-l.scrollTop*c.y+d.y}}function Sa(e){return Array.from(e.getClientRects())}function tn(e,t){const n=Et(e).scrollLeft;return t?t.left+n:xe(ee(e)).left+n}function ba(e){const t=ee(e),n=Et(e),r=e.ownerDocument.body,o=U(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=U(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+tn(e);const a=-n.scrollTop;return Z(r).direction==="rtl"&&(s+=U(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:a}}function Ca(e,t){const n=Y(e),r=ee(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,a=0,l=0;if(o){i=o.width,s=o.height;const c=xn();(!c||c&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:i,height:s,x:a,y:l}}function Ea(e,t){const n=xe(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=J(e)?Oe(e):he(1),s=e.clientWidth*i.x,a=e.clientHeight*i.y,l=o*i.x,c=r*i.y;return{width:s,height:a,x:l,y:c}}function ir(e,t,n){let r;if(t==="viewport")r=Ca(e,n);else if(t==="document")r=ba(ee(e));else if(K(t))r=Ea(t,n);else{const o=Yr(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return ft(r)}function Xr(e,t){const n=ve(e);return n===t||!K(n)||Le(n)?!1:Z(n).position==="fixed"||Xr(n,t)}function Ta(e,t){const n=t.get(e);if(n)return n;let r=Ge(e,[],!1).filter(a=>K(a)&&je(a)!=="body"),o=null;const i=Z(e).position==="fixed";let s=i?ve(e):e;for(;K(s)&&!Le(s);){const a=Z(s),l=yn(s);!l&&a.position==="fixed"&&(o=null),(i?!l&&!o:!l&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Ue(s)&&!l&&Xr(e,s))?r=r.filter(d=>d!==s):o=a,s=ve(s)}return t.set(e,r),r}function Pa(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?Ct(t)?[]:Ta(t,this._c):[].concat(n),r],a=s[0],l=s.reduce((c,d)=>{const u=ir(t,d,o);return c.top=U(u.top,c.top),c.right=pe(u.right,c.right),c.bottom=pe(u.bottom,c.bottom),c.left=U(u.left,c.left),c},ir(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Aa(e){const{width:t,height:n}=Ur(e);return{width:t,height:n}}function Ma(e,t,n){const r=J(t),o=ee(t),i=n==="fixed",s=xe(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const l=he(0);if(r||!r&&!i)if((je(t)!=="body"||Ue(o))&&(a=Et(t)),r){const h=xe(t,!0,i,t);l.x=h.x+t.clientLeft,l.y=h.y+t.clientTop}else o&&(l.x=tn(o));let c=0,d=0;if(o&&!r&&!i){const h=o.getBoundingClientRect();d=h.top+a.scrollTop,c=h.left+a.scrollLeft-tn(o,h)}const u=s.left+a.scrollLeft-l.x-c,p=s.top+a.scrollTop-l.y-d;return{x:u,y:p,width:s.width,height:s.height}}function Lt(e){return Z(e).position==="static"}function sr(e,t){if(!J(e)||Z(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return ee(e)===n&&(n=n.ownerDocument.body),n}function qr(e,t){const n=Y(e);if(Ct(e))return n;if(!J(e)){let o=ve(e);for(;o&&!Le(o);){if(K(o)&&!Lt(o))return o;o=ve(o)}return n}let r=sr(e,t);for(;r&&va(r)&&Lt(r);)r=sr(r,t);return r&&Le(r)&&Lt(r)&&!yn(r)?n:r||ga(e)||n}const Ra=async function(e){const t=this.getOffsetParent||qr,n=this.getDimensions,r=await n(e.floating);return{reference:Ma(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function ka(e){return Z(e).direction==="rtl"}const Oa={convertOffsetParentRelativeRectToViewportRelativeRect:xa,getDocumentElement:ee,getClippingRect:Pa,getOffsetParent:qr,getElementRects:Ra,getClientRects:Sa,getDimensions:Aa,getScale:Oe,isElement:K,isRTL:ka};function Ia(e,t){let n=null,r;const o=ee(e);function i(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function s(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),i();const{left:c,top:d,width:u,height:p}=e.getBoundingClientRect();if(a||t(),!u||!p)return;const h=Ze(d),v=Ze(o.clientWidth-(c+u)),m=Ze(o.clientHeight-(d+p)),w=Ze(c),x={rootMargin:-h+"px "+-v+"px "+-m+"px "+-w+"px",threshold:U(0,pe(1,l))||1};let S=!0;function b(C){const T=C[0].intersectionRatio;if(T!==l){if(!S)return s();T?s(!1,T):r=setTimeout(()=>{s(!1,1e-7)},1e3)}S=!1}try{n=new IntersectionObserver(b,{...x,root:o.ownerDocument})}catch{n=new IntersectionObserver(b,x)}n.observe(e)}return s(!0),i}function Da(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,c=Sn(e),d=o||i?[...c?Ge(c):[],...Ge(t)]:[];d.forEach(g=>{o&&g.addEventListener("scroll",n,{passive:!0}),i&&g.addEventListener("resize",n)});const u=c&&a?Ia(c,n):null;let p=-1,h=null;s&&(h=new ResizeObserver(g=>{let[x]=g;x&&x.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var S;(S=h)==null||S.observe(t)})),n()}),c&&!l&&h.observe(c),h.observe(t));let v,m=l?xe(e):null;l&&w();function w(){const g=xe(e);m&&(g.x!==m.x||g.y!==m.y||g.width!==m.width||g.height!==m.height)&&n(),m=g,v=requestAnimationFrame(w)}return n(),()=>{var g;d.forEach(x=>{o&&x.removeEventListener("scroll",n),i&&x.removeEventListener("resize",n)}),u==null||u(),(g=h)==null||g.disconnect(),h=null,l&&cancelAnimationFrame(v)}}const La=fa,_a=pa,Na=ca,$a=ma,ja=da,ar=la,za=ha,Fa=(e,t,n)=>{const r=new Map,o={platform:Oa,...n},i={...o.platform,_c:r};return aa(e,t,{...o,platform:i})};var st=typeof document<"u"?f.useLayoutEffect:f.useEffect;function pt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!pt(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!pt(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Kr(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function lr(e,t){const n=Kr(e);return Math.round(t*n)/n}function _t(e){const t=f.useRef(e);return st(()=>{t.current=e}),t}function Va(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:a=!0,whileElementsMounted:l,open:c}=e,[d,u]=f.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=f.useState(r);pt(p,r)||h(r);const[v,m]=f.useState(null),[w,g]=f.useState(null),x=f.useCallback(I=>{I!==T.current&&(T.current=I,m(I))},[]),S=f.useCallback(I=>{I!==P.current&&(P.current=I,g(I))},[]),b=i||v,C=s||w,T=f.useRef(null),P=f.useRef(null),M=f.useRef(d),R=l!=null,E=_t(l),A=_t(o),O=_t(c),L=f.useCallback(()=>{if(!T.current||!P.current)return;const I={placement:t,strategy:n,middleware:p};A.current&&(I.platform=A.current),Fa(T.current,P.current,I).then($=>{const W={...$,isPositioned:O.current!==!1};N.current&&!pt(M.current,W)&&(M.current=W,Lr.flushSync(()=>{u(W)}))})},[p,t,n,A,O]);st(()=>{c===!1&&M.current.isPositioned&&(M.current.isPositioned=!1,u(I=>({...I,isPositioned:!1})))},[c]);const N=f.useRef(!1);st(()=>(N.current=!0,()=>{N.current=!1}),[]),st(()=>{if(b&&(T.current=b),C&&(P.current=C),b&&C){if(E.current)return E.current(b,C,L);L()}},[b,C,L,E,R]);const z=f.useMemo(()=>({reference:T,floating:P,setReference:x,setFloating:S}),[x,S]),k=f.useMemo(()=>({reference:b,floating:C}),[b,C]),_=f.useMemo(()=>{const I={position:n,left:0,top:0};if(!k.floating)return I;const $=lr(k.floating,d.x),W=lr(k.floating,d.y);return a?{...I,transform:"translate("+$+"px, "+W+"px)",...Kr(k.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:$,top:W}},[n,a,k.floating,d.x,d.y]);return f.useMemo(()=>({...d,update:L,refs:z,elements:k,floatingStyles:_}),[d,L,z,k,_])}const Ba=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?ar({element:r.current,padding:o}).fn(n):{}:r?ar({element:r,padding:o}).fn(n):{}}}},Ha=(e,t)=>({...La(e),options:[e,t]}),Wa=(e,t)=>({..._a(e),options:[e,t]}),Ga=(e,t)=>({...za(e),options:[e,t]}),Ua=(e,t)=>({...Na(e),options:[e,t]}),Ya=(e,t)=>({...$a(e),options:[e,t]}),Xa=(e,t)=>({...ja(e),options:[e,t]}),qa=(e,t)=>({...Ba(e),options:[e,t]});var Ka="Arrow",Zr=f.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return y.jsx(F.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:y.jsx("polygon",{points:"0,0 30,0 15,10"})})});Zr.displayName=Ka;var Za=Zr;function Qa(e,t=[]){let n=[];function r(i,s){const a=f.createContext(s),l=n.length;n=[...n,s];function c(u){const{scope:p,children:h,...v}=u,m=(p==null?void 0:p[e][l])||a,w=f.useMemo(()=>v,Object.values(v));return y.jsx(m.Provider,{value:w,children:h})}function d(u,p){const h=(p==null?void 0:p[e][l])||a,v=f.useContext(h);if(v)return v;if(s!==void 0)return s;throw new Error(`\`${u}\` must be used within \`${i}\``)}return c.displayName=i+"Provider",[c,d]}const o=()=>{const i=n.map(s=>f.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return f.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,Ja(o,...t)]}function Ja(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:c})=>{const u=l(i)[`__scope${c}`];return{...a,...u}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function bn(e){const[t,n]=f.useState(void 0);return fe(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,a;if("borderBoxSize"in i){const l=i.borderBoxSize,c=Array.isArray(l)?l[0]:l;s=c.inlineSize,a=c.blockSize}else s=e.offsetWidth,a=e.offsetHeight;n({width:s,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var Qr="Popper",[Jr,eo]=Qa(Qr),[uh,to]=Jr(Qr),no="PopperAnchor",ro=f.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=to(no,n),s=f.useRef(null),a=B(t,s);return f.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||s.current)}),r?null:y.jsx(F.div,{...o,ref:a})});ro.displayName=no;var Cn="PopperContent",[el,tl]=Jr(Cn),oo=f.forwardRef((e,t)=>{var Un,Yn,Xn,qn,Kn,Zn;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:c=[],collisionPadding:d=0,sticky:u="partial",hideWhenDetached:p=!1,updatePositionStrategy:h="optimized",onPlaced:v,...m}=e,w=to(Cn,n),[g,x]=f.useState(null),S=B(t,Ve=>x(Ve)),[b,C]=f.useState(null),T=bn(b),P=(T==null?void 0:T.width)??0,M=(T==null?void 0:T.height)??0,R=r+(i!=="center"?"-"+i:""),E=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},A=Array.isArray(c)?c:[c],O=A.length>0,L={padding:E,boundary:A.filter(rl),altBoundary:O},{refs:N,floatingStyles:z,placement:k,isPositioned:_,middlewareData:I}=Va({strategy:"fixed",placement:R,whileElementsMounted:(...Ve)=>Da(...Ve,{animationFrame:h==="always"}),elements:{reference:w.anchor},middleware:[Ha({mainAxis:o+M,alignmentAxis:s}),l&&Wa({mainAxis:!0,crossAxis:!1,limiter:u==="partial"?Ga():void 0,...L}),l&&Ua({...L}),Ya({...L,apply:({elements:Ve,rects:Qn,availableWidth:xs,availableHeight:Ss})=>{const{width:bs,height:Cs}=Qn.reference,Ke=Ve.floating.style;Ke.setProperty("--radix-popper-available-width",`${xs}px`),Ke.setProperty("--radix-popper-available-height",`${Ss}px`),Ke.setProperty("--radix-popper-anchor-width",`${bs}px`),Ke.setProperty("--radix-popper-anchor-height",`${Cs}px`)}}),b&&qa({element:b,padding:a}),ol({arrowWidth:P,arrowHeight:M}),p&&Xa({strategy:"referenceHidden",...L})]}),[$,W]=ao(k),Pe=ue(v);fe(()=>{_&&(Pe==null||Pe())},[_,Pe]);const Dt=(Un=I.arrow)==null?void 0:Un.x,vs=(Yn=I.arrow)==null?void 0:Yn.y,gs=((Xn=I.arrow)==null?void 0:Xn.centerOffset)!==0,[ws,ys]=f.useState();return fe(()=>{g&&ys(window.getComputedStyle(g).zIndex)},[g]),y.jsx("div",{ref:N.setFloating,"data-radix-popper-content-wrapper":"",style:{...z,transform:_?z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ws,"--radix-popper-transform-origin":[(qn=I.transformOrigin)==null?void 0:qn.x,(Kn=I.transformOrigin)==null?void 0:Kn.y].join(" "),...((Zn=I.hide)==null?void 0:Zn.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:y.jsx(el,{scope:n,placedSide:$,onArrowChange:C,arrowX:Dt,arrowY:vs,shouldHideArrow:gs,children:y.jsx(F.div,{"data-side":$,"data-align":W,...m,ref:S,style:{...m.style,animation:_?void 0:"none"}})})})});oo.displayName=Cn;var io="PopperArrow",nl={top:"bottom",right:"left",bottom:"top",left:"right"},so=f.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=tl(io,r),s=nl[i.placedSide];return y.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:y.jsx(Za,{...o,ref:n,style:{...o.style,display:"block"}})})});so.displayName=io;function rl(e){return e!==null}var ol=e=>({name:"transformOrigin",options:e,fn(t){var w,g,x;const{placement:n,rects:r,middlewareData:o}=t,s=((w=o.arrow)==null?void 0:w.centerOffset)!==0,a=s?0:e.arrowWidth,l=s?0:e.arrowHeight,[c,d]=ao(n),u={start:"0%",center:"50%",end:"100%"}[d],p=(((g=o.arrow)==null?void 0:g.x)??0)+a/2,h=(((x=o.arrow)==null?void 0:x.y)??0)+l/2;let v="",m="";return c==="bottom"?(v=s?u:`${p}px`,m=`${-l}px`):c==="top"?(v=s?u:`${p}px`,m=`${r.floating.height+l}px`):c==="right"?(v=`${-l}px`,m=s?u:`${h}px`):c==="left"&&(v=`${r.floating.width+l}px`,m=s?u:`${h}px`),{data:{x:v,y:m}}}});function ao(e){const[t,n="center"]=e.split("-");return[t,n]}var il=ro,sl=oo,al=so,ll="Portal",lo=f.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,i]=f.useState(!1);fe(()=>i(!0),[]);const s=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?Es.createPortal(y.jsx(F.div,{...r,ref:t}),s):null});lo.displayName=ll;function cl(e,t){return f.useReducer((n,r)=>t[n][r]??n,e)}var Ce=e=>{const{present:t,children:n}=e,r=dl(t),o=typeof n=="function"?n({present:r.isPresent}):f.Children.only(n),i=B(r.ref,ul(o));return typeof n=="function"||r.isPresent?f.cloneElement(o,{ref:i}):null};Ce.displayName="Presence";function dl(e){const[t,n]=f.useState(),r=f.useRef({}),o=f.useRef(e),i=f.useRef("none"),s=e?"mounted":"unmounted",[a,l]=cl(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return f.useEffect(()=>{const c=Qe(r.current);i.current=a==="mounted"?c:"none"},[a]),fe(()=>{const c=r.current,d=o.current;if(d!==e){const p=i.current,h=Qe(c);e?l("MOUNT"):h==="none"||(c==null?void 0:c.display)==="none"?l("UNMOUNT"):l(d&&p!==h?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),fe(()=>{if(t){let c;const d=t.ownerDocument.defaultView??window,u=h=>{const m=Qe(r.current).includes(h.animationName);if(h.target===t&&m&&(l("ANIMATION_END"),!o.current)){const w=t.style.animationFillMode;t.style.animationFillMode="forwards",c=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=w)})}},p=h=>{h.target===t&&(i.current=Qe(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",u),t.addEventListener("animationend",u),()=>{d.clearTimeout(c),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",u),t.removeEventListener("animationend",u)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:f.useCallback(c=>{c&&(r.current=getComputedStyle(c)),n(c)},[])}}function Qe(e){return(e==null?void 0:e.animationName)||"none"}function ul(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function ze({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=fl({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:r,a=ue(n),l=f.useCallback(c=>{if(i){const u=typeof c=="function"?c(e):c;u!==e&&a(u)}else o(c)},[i,e,o,a]);return[s,l]}function fl({defaultProp:e,onChange:t}){const n=f.useState(e),[r]=n,o=f.useRef(r),i=ue(t);return f.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}var pl="VisuallyHidden",co=f.forwardRef((e,t)=>y.jsx(F.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));co.displayName=pl;var hl=co,[Tt,fh]=be("Tooltip",[eo]),En=eo(),uo="TooltipProvider",ml=700,cr="tooltip.open",[vl,fo]=Tt(uo),po=e=>{const{__scopeTooltip:t,delayDuration:n=ml,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:i}=e,[s,a]=f.useState(!0),l=f.useRef(!1),c=f.useRef(0);return f.useEffect(()=>{const d=c.current;return()=>window.clearTimeout(d)},[]),y.jsx(vl,{scope:t,isOpenDelayed:s,delayDuration:n,onOpen:f.useCallback(()=>{window.clearTimeout(c.current),a(!1)},[]),onClose:f.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>a(!0),r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:f.useCallback(d=>{l.current=d},[]),disableHoverableContent:o,children:i})};po.displayName=uo;var ho="Tooltip",[ph,Pt]=Tt(ho),nn="TooltipTrigger",gl=f.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Pt(nn,n),i=fo(nn,n),s=En(n),a=f.useRef(null),l=B(t,a,o.onTriggerChange),c=f.useRef(!1),d=f.useRef(!1),u=f.useCallback(()=>c.current=!1,[]);return f.useEffect(()=>()=>document.removeEventListener("pointerup",u),[u]),y.jsx(il,{asChild:!0,...s,children:y.jsx(F.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:l,onPointerMove:V(e.onPointerMove,p=>{p.pointerType!=="touch"&&!d.current&&!i.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:V(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:V(e.onPointerDown,()=>{c.current=!0,document.addEventListener("pointerup",u,{once:!0})}),onFocus:V(e.onFocus,()=>{c.current||o.onOpen()}),onBlur:V(e.onBlur,o.onClose),onClick:V(e.onClick,o.onClose)})})});gl.displayName=nn;var wl="TooltipPortal",[hh,yl]=Tt(wl,{forceMount:void 0}),_e="TooltipContent",mo=f.forwardRef((e,t)=>{const n=yl(_e,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,s=Pt(_e,e.__scopeTooltip);return y.jsx(Ce,{present:r||s.open,children:s.disableHoverableContent?y.jsx(vo,{side:o,...i,ref:t}):y.jsx(xl,{side:o,...i,ref:t})})}),xl=f.forwardRef((e,t)=>{const n=Pt(_e,e.__scopeTooltip),r=fo(_e,e.__scopeTooltip),o=f.useRef(null),i=B(t,o),[s,a]=f.useState(null),{trigger:l,onClose:c}=n,d=o.current,{onPointerInTransitChange:u}=r,p=f.useCallback(()=>{a(null),u(!1)},[u]),h=f.useCallback((v,m)=>{const w=v.currentTarget,g={x:v.clientX,y:v.clientY},x=El(g,w.getBoundingClientRect()),S=Tl(g,x),b=Pl(m.getBoundingClientRect()),C=Ml([...S,...b]);a(C),u(!0)},[u]);return f.useEffect(()=>()=>p(),[p]),f.useEffect(()=>{if(l&&d){const v=w=>h(w,d),m=w=>h(w,l);return l.addEventListener("pointerleave",v),d.addEventListener("pointerleave",m),()=>{l.removeEventListener("pointerleave",v),d.removeEventListener("pointerleave",m)}}},[l,d,h,p]),f.useEffect(()=>{if(s){const v=m=>{const w=m.target,g={x:m.clientX,y:m.clientY},x=(l==null?void 0:l.contains(w))||(d==null?void 0:d.contains(w)),S=!Al(g,s);x?p():S&&(p(),c())};return document.addEventListener("pointermove",v),()=>document.removeEventListener("pointermove",v)}},[l,d,s,c,p]),y.jsx(vo,{...e,ref:i})}),[Sl,bl]=Tt(ho,{isInside:!1}),vo=f.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:s,...a}=e,l=Pt(_e,n),c=En(n),{onClose:d}=l;return f.useEffect(()=>(document.addEventListener(cr,d),()=>document.removeEventListener(cr,d)),[d]),f.useEffect(()=>{if(l.trigger){const u=p=>{const h=p.target;h!=null&&h.contains(l.trigger)&&d()};return window.addEventListener("scroll",u,{capture:!0}),()=>window.removeEventListener("scroll",u,{capture:!0})}},[l.trigger,d]),y.jsx(mn,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:u=>u.preventDefault(),onDismiss:d,children:y.jsxs(sl,{"data-state":l.stateAttribute,...c,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[y.jsx(hn,{children:r}),y.jsx(Sl,{scope:n,isInside:!0,children:y.jsx(hl,{id:l.contentId,role:"tooltip",children:o||r})})]})})});mo.displayName=_e;var go="TooltipArrow",Cl=f.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=En(n);return bl(go,n).isInside?null:y.jsx(al,{...o,...r,ref:t})});Cl.displayName=go;function El(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Tl(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function Pl(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function Al(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,s=t.length-1;i<t.length;s=i++){const a=t[i].x,l=t[i].y,c=t[s].x,d=t[s].y;l>r!=d>r&&n<(c-a)*(r-l)/(d-l)+a&&(o=!o)}return o}function Ml(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Rl(t)}function Rl(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const i=t[t.length-1],s=t[t.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const i=n[n.length-1],s=n[n.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var mh=po,vh=mo;function dr(e){return e!==null&&typeof e=="object"&&"constructor"in e&&e.constructor===Object}function Tn(e,t){e===void 0&&(e={}),t===void 0&&(t={});const n=["__proto__","constructor","prototype"];Object.keys(t).filter(r=>n.indexOf(r)<0).forEach(r=>{typeof e[r]>"u"?e[r]=t[r]:dr(t[r])&&dr(e[r])&&Object.keys(t[r]).length>0&&Tn(e[r],t[r])})}const wo={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function Ee(){const e=typeof document<"u"?document:{};return Tn(e,wo),e}const kl={document:wo,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(e){return typeof setTimeout>"u"?(e(),null):setTimeout(e,0)},cancelAnimationFrame(e){typeof setTimeout>"u"||clearTimeout(e)}};function H(){const e=typeof window<"u"?window:{};return Tn(e,kl),e}function Ol(e){return e===void 0&&(e=""),e.trim().split(" ").filter(t=>!!t.trim())}function Il(e){const t=e;Object.keys(t).forEach(n=>{try{t[n]=null}catch{}try{delete t[n]}catch{}})}function rn(e,t){return t===void 0&&(t=0),setTimeout(e,t)}function ht(){return Date.now()}function Dl(e){const t=H();let n;return t.getComputedStyle&&(n=t.getComputedStyle(e,null)),!n&&e.currentStyle&&(n=e.currentStyle),n||(n=e.style),n}function Ll(e,t){t===void 0&&(t="x");const n=H();let r,o,i;const s=Dl(e);return n.WebKitCSSMatrix?(o=s.transform||s.webkitTransform,o.split(",").length>6&&(o=o.split(", ").map(a=>a.replace(",",".")).join(", ")),i=new n.WebKitCSSMatrix(o==="none"?"":o)):(i=s.MozTransform||s.OTransform||s.MsTransform||s.msTransform||s.transform||s.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),r=i.toString().split(",")),t==="x"&&(n.WebKitCSSMatrix?o=i.m41:r.length===16?o=parseFloat(r[12]):o=parseFloat(r[4])),t==="y"&&(n.WebKitCSSMatrix?o=i.m42:r.length===16?o=parseFloat(r[13]):o=parseFloat(r[5])),o||0}function Je(e){return typeof e=="object"&&e!==null&&e.constructor&&Object.prototype.toString.call(e).slice(8,-1)==="Object"}function _l(e){return typeof window<"u"&&typeof window.HTMLElement<"u"?e instanceof HTMLElement:e&&(e.nodeType===1||e.nodeType===11)}function G(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let n=1;n<arguments.length;n+=1){const r=n<0||arguments.length<=n?void 0:arguments[n];if(r!=null&&!_l(r)){const o=Object.keys(Object(r)).filter(i=>t.indexOf(i)<0);for(let i=0,s=o.length;i<s;i+=1){const a=o[i],l=Object.getOwnPropertyDescriptor(r,a);l!==void 0&&l.enumerable&&(Je(e[a])&&Je(r[a])?r[a].__swiper__?e[a]=r[a]:G(e[a],r[a]):!Je(e[a])&&Je(r[a])?(e[a]={},r[a].__swiper__?e[a]=r[a]:G(e[a],r[a])):e[a]=r[a])}}}return e}function et(e,t,n){e.style.setProperty(t,n)}function yo(e){let{swiper:t,targetPosition:n,side:r}=e;const o=H(),i=-t.translate;let s=null,a;const l=t.params.speed;t.wrapperEl.style.scrollSnapType="none",o.cancelAnimationFrame(t.cssModeFrameID);const c=n>i?"next":"prev",d=(p,h)=>c==="next"&&p>=h||c==="prev"&&p<=h,u=()=>{a=new Date().getTime(),s===null&&(s=a);const p=Math.max(Math.min((a-s)/l,1),0),h=.5-Math.cos(p*Math.PI)/2;let v=i+h*(n-i);if(d(v,n)&&(v=n),t.wrapperEl.scrollTo({[r]:v}),d(v,n)){t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout(()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[r]:v})}),o.cancelAnimationFrame(t.cssModeFrameID);return}t.cssModeFrameID=o.requestAnimationFrame(u)};u()}function gh(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function ne(e,t){t===void 0&&(t="");const n=H(),r=[...e.children];return n.HTMLSlotElement&&e instanceof HTMLSlotElement&&r.push(...e.assignedElements()),t?r.filter(o=>o.matches(t)):r}function Nl(e,t){const n=[t];for(;n.length>0;){const r=n.shift();if(e===r)return!0;n.push(...r.children,...r.shadowRoot?r.shadowRoot.children:[],...r.assignedElements?r.assignedElements():[])}}function $l(e,t){const n=H();let r=t.contains(e);return!r&&n.HTMLSlotElement&&t instanceof HTMLSlotElement&&(r=[...t.assignedElements()].includes(e),r||(r=Nl(e,t))),r}function mt(e){try{console.warn(e);return}catch{}}function on(e,t){t===void 0&&(t=[]);const n=document.createElement(e);return n.classList.add(...Array.isArray(t)?t:Ol(t)),n}function jl(e,t){const n=[];for(;e.previousElementSibling;){const r=e.previousElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}function zl(e,t){const n=[];for(;e.nextElementSibling;){const r=e.nextElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}function ce(e,t){return H().getComputedStyle(e,null).getPropertyValue(t)}function ur(e){let t=e,n;if(t){for(n=0;(t=t.previousSibling)!==null;)t.nodeType===1&&(n+=1);return n}}function Fl(e,t){const n=[];let r=e.parentElement;for(;r;)t?r.matches(t)&&n.push(r):n.push(r),r=r.parentElement;return n}function fr(e,t,n){const r=H();return e[t==="width"?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-left":"margin-bottom"))}function wh(e){return(Array.isArray(e)?e:[e]).filter(t=>!!t)}function yh(e){return t=>Math.abs(t)>0&&e.browser&&e.browser.need3dFix&&Math.abs(t)%90===0?t+.001:t}function xh(e,t){t===void 0&&(t=""),typeof trustedTypes<"u"?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:n=>n}).createHTML(t):e.innerHTML=t}let Nt;function Vl(){const e=H(),t=Ee();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}function xo(){return Nt||(Nt=Vl()),Nt}let $t;function Bl(e){let{userAgent:t}=e===void 0?{}:e;const n=xo(),r=H(),o=r.navigator.platform,i=t||r.navigator.userAgent,s={ios:!1,android:!1},a=r.screen.width,l=r.screen.height,c=i.match(/(Android);?[\s\/]+([\d.]+)?/);let d=i.match(/(iPad).*OS\s([\d_]+)/);const u=i.match(/(iPod)(.*OS\s([\d_]+))?/),p=!d&&i.match(/(iPhone\sOS|iOS)\s([\d_]+)/),h=o==="Win32";let v=o==="MacIntel";const m=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!d&&v&&n.touch&&m.indexOf(`${a}x${l}`)>=0&&(d=i.match(/(Version)\/([\d.]+)/),d||(d=[0,1,"13_0_0"]),v=!1),c&&!h&&(s.os="android",s.android=!0),(d||p||u)&&(s.os="ios",s.ios=!0),s}function So(e){return e===void 0&&(e={}),$t||($t=Bl(e)),$t}let jt;function Hl(){const e=H(),t=So();let n=!1;function r(){const a=e.navigator.userAgent.toLowerCase();return a.indexOf("safari")>=0&&a.indexOf("chrome")<0&&a.indexOf("android")<0}if(r()){const a=String(e.navigator.userAgent);if(a.includes("Version/")){const[l,c]=a.split("Version/")[1].split(" ")[0].split(".").map(d=>Number(d));n=l<16||l===16&&c<2}}const o=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),i=r(),s=i||o&&t.ios;return{isSafari:n||i,needPerspectiveFix:n,need3dFix:s,isWebView:o}}function bo(){return jt||(jt=Hl()),jt}function Wl(e){let{swiper:t,on:n,emit:r}=e;const o=H();let i=null,s=null;const a=()=>{!t||t.destroyed||!t.initialized||(r("beforeResize"),r("resize"))},l=()=>{!t||t.destroyed||!t.initialized||(i=new ResizeObserver(u=>{s=o.requestAnimationFrame(()=>{const{width:p,height:h}=t;let v=p,m=h;u.forEach(w=>{let{contentBoxSize:g,contentRect:x,target:S}=w;S&&S!==t.el||(v=x?x.width:(g[0]||g).inlineSize,m=x?x.height:(g[0]||g).blockSize)}),(v!==p||m!==h)&&a()})}),i.observe(t.el))},c=()=>{s&&o.cancelAnimationFrame(s),i&&i.unobserve&&t.el&&(i.unobserve(t.el),i=null)},d=()=>{!t||t.destroyed||!t.initialized||r("orientationchange")};n("init",()=>{if(t.params.resizeObserver&&typeof o.ResizeObserver<"u"){l();return}o.addEventListener("resize",a),o.addEventListener("orientationchange",d)}),n("destroy",()=>{c(),o.removeEventListener("resize",a),o.removeEventListener("orientationchange",d)})}function Gl(e){let{swiper:t,extendParams:n,on:r,emit:o}=e;const i=[],s=H(),a=function(d,u){u===void 0&&(u={});const p=s.MutationObserver||s.WebkitMutationObserver,h=new p(v=>{if(t.__preventObserver__)return;if(v.length===1){o("observerUpdate",v[0]);return}const m=function(){o("observerUpdate",v[0])};s.requestAnimationFrame?s.requestAnimationFrame(m):s.setTimeout(m,0)});h.observe(d,{attributes:typeof u.attributes>"u"?!0:u.attributes,childList:t.isElement||(typeof u.childList>"u"?!0:u).childList,characterData:typeof u.characterData>"u"?!0:u.characterData}),i.push(h)},l=()=>{if(t.params.observer){if(t.params.observeParents){const d=Fl(t.hostEl);for(let u=0;u<d.length;u+=1)a(d[u])}a(t.hostEl,{childList:t.params.observeSlideChildren}),a(t.wrapperEl,{attributes:!1})}},c=()=>{i.forEach(d=>{d.disconnect()}),i.splice(0,i.length)};n({observer:!1,observeParents:!1,observeSlideChildren:!1}),r("init",l),r("destroy",c)}var Ul={on(e,t,n){const r=this;if(!r.eventsListeners||r.destroyed||typeof t!="function")return r;const o=n?"unshift":"push";return e.split(" ").forEach(i=>{r.eventsListeners[i]||(r.eventsListeners[i]=[]),r.eventsListeners[i][o](t)}),r},once(e,t,n){const r=this;if(!r.eventsListeners||r.destroyed||typeof t!="function")return r;function o(){r.off(e,o),o.__emitterProxy&&delete o.__emitterProxy;for(var i=arguments.length,s=new Array(i),a=0;a<i;a++)s[a]=arguments[a];t.apply(r,s)}return o.__emitterProxy=t,r.on(e,o,n)},onAny(e,t){const n=this;if(!n.eventsListeners||n.destroyed||typeof e!="function")return n;const r=t?"unshift":"push";return n.eventsAnyListeners.indexOf(e)<0&&n.eventsAnyListeners[r](e),n},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed||!t.eventsAnyListeners)return t;const n=t.eventsAnyListeners.indexOf(e);return n>=0&&t.eventsAnyListeners.splice(n,1),t},off(e,t){const n=this;return!n.eventsListeners||n.destroyed||!n.eventsListeners||e.split(" ").forEach(r=>{typeof t>"u"?n.eventsListeners[r]=[]:n.eventsListeners[r]&&n.eventsListeners[r].forEach((o,i)=>{(o===t||o.__emitterProxy&&o.__emitterProxy===t)&&n.eventsListeners[r].splice(i,1)})}),n},emit(){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsListeners)return e;let t,n,r;for(var o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return typeof i[0]=="string"||Array.isArray(i[0])?(t=i[0],n=i.slice(1,i.length),r=e):(t=i[0].events,n=i[0].data,r=i[0].context||e),n.unshift(r),(Array.isArray(t)?t:t.split(" ")).forEach(l=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(c=>{c.apply(r,[l,...n])}),e.eventsListeners&&e.eventsListeners[l]&&e.eventsListeners[l].forEach(c=>{c.apply(r,n)})}),e}};function Yl(){const e=this;let t,n;const r=e.el;typeof e.params.width<"u"&&e.params.width!==null?t=e.params.width:t=r.clientWidth,typeof e.params.height<"u"&&e.params.height!==null?n=e.params.height:n=r.clientHeight,!(t===0&&e.isHorizontal()||n===0&&e.isVertical())&&(t=t-parseInt(ce(r,"padding-left")||0,10)-parseInt(ce(r,"padding-right")||0,10),n=n-parseInt(ce(r,"padding-top")||0,10)-parseInt(ce(r,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(n)&&(n=0),Object.assign(e,{width:t,height:n,size:e.isHorizontal()?t:n}))}function Xl(){const e=this;function t(E,A){return parseFloat(E.getPropertyValue(e.getDirectionLabel(A))||0)}const n=e.params,{wrapperEl:r,slidesEl:o,size:i,rtlTranslate:s,wrongRTL:a}=e,l=e.virtual&&n.virtual.enabled,c=l?e.virtual.slides.length:e.slides.length,d=ne(o,`.${e.params.slideClass}, swiper-slide`),u=l?e.virtual.slides.length:d.length;let p=[];const h=[],v=[];let m=n.slidesOffsetBefore;typeof m=="function"&&(m=n.slidesOffsetBefore.call(e));let w=n.slidesOffsetAfter;typeof w=="function"&&(w=n.slidesOffsetAfter.call(e));const g=e.snapGrid.length,x=e.slidesGrid.length;let S=n.spaceBetween,b=-m,C=0,T=0;if(typeof i>"u")return;typeof S=="string"&&S.indexOf("%")>=0?S=parseFloat(S.replace("%",""))/100*i:typeof S=="string"&&(S=parseFloat(S)),e.virtualSize=-S,d.forEach(E=>{s?E.style.marginLeft="":E.style.marginRight="",E.style.marginBottom="",E.style.marginTop=""}),n.centeredSlides&&n.cssMode&&(et(r,"--swiper-centered-offset-before",""),et(r,"--swiper-centered-offset-after",""));const P=n.grid&&n.grid.rows>1&&e.grid;P?e.grid.initSlides(d):e.grid&&e.grid.unsetSlides();let M;const R=n.slidesPerView==="auto"&&n.breakpoints&&Object.keys(n.breakpoints).filter(E=>typeof n.breakpoints[E].slidesPerView<"u").length>0;for(let E=0;E<u;E+=1){M=0;let A;if(d[E]&&(A=d[E]),P&&e.grid.updateSlide(E,A,d),!(d[E]&&ce(A,"display")==="none")){if(n.slidesPerView==="auto"){R&&(d[E].style[e.getDirectionLabel("width")]="");const O=getComputedStyle(A),L=A.style.transform,N=A.style.webkitTransform;if(L&&(A.style.transform="none"),N&&(A.style.webkitTransform="none"),n.roundLengths)M=e.isHorizontal()?fr(A,"width"):fr(A,"height");else{const z=t(O,"width"),k=t(O,"padding-left"),_=t(O,"padding-right"),I=t(O,"margin-left"),$=t(O,"margin-right"),W=O.getPropertyValue("box-sizing");if(W&&W==="border-box")M=z+I+$;else{const{clientWidth:Pe,offsetWidth:Dt}=A;M=z+k+_+I+$+(Dt-Pe)}}L&&(A.style.transform=L),N&&(A.style.webkitTransform=N),n.roundLengths&&(M=Math.floor(M))}else M=(i-(n.slidesPerView-1)*S)/n.slidesPerView,n.roundLengths&&(M=Math.floor(M)),d[E]&&(d[E].style[e.getDirectionLabel("width")]=`${M}px`);d[E]&&(d[E].swiperSlideSize=M),v.push(M),n.centeredSlides?(b=b+M/2+C/2+S,C===0&&E!==0&&(b=b-i/2-S),E===0&&(b=b-i/2-S),Math.abs(b)<1/1e3&&(b=0),n.roundLengths&&(b=Math.floor(b)),T%n.slidesPerGroup===0&&p.push(b),h.push(b)):(n.roundLengths&&(b=Math.floor(b)),(T-Math.min(e.params.slidesPerGroupSkip,T))%e.params.slidesPerGroup===0&&p.push(b),h.push(b),b=b+M+S),e.virtualSize+=M+S,C=M,T+=1}}if(e.virtualSize=Math.max(e.virtualSize,i)+w,s&&a&&(n.effect==="slide"||n.effect==="coverflow")&&(r.style.width=`${e.virtualSize+S}px`),n.setWrapperSize&&(r.style[e.getDirectionLabel("width")]=`${e.virtualSize+S}px`),P&&e.grid.updateWrapperSize(M,p),!n.centeredSlides){const E=[];for(let A=0;A<p.length;A+=1){let O=p[A];n.roundLengths&&(O=Math.floor(O)),p[A]<=e.virtualSize-i&&E.push(O)}p=E,Math.floor(e.virtualSize-i)-Math.floor(p[p.length-1])>1&&p.push(e.virtualSize-i)}if(l&&n.loop){const E=v[0]+S;if(n.slidesPerGroup>1){const A=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/n.slidesPerGroup),O=E*n.slidesPerGroup;for(let L=0;L<A;L+=1)p.push(p[p.length-1]+O)}for(let A=0;A<e.virtual.slidesBefore+e.virtual.slidesAfter;A+=1)n.slidesPerGroup===1&&p.push(p[p.length-1]+E),h.push(h[h.length-1]+E),e.virtualSize+=E}if(p.length===0&&(p=[0]),S!==0){const E=e.isHorizontal()&&s?"marginLeft":e.getDirectionLabel("marginRight");d.filter((A,O)=>!n.cssMode||n.loop?!0:O!==d.length-1).forEach(A=>{A.style[E]=`${S}px`})}if(n.centeredSlides&&n.centeredSlidesBounds){let E=0;v.forEach(O=>{E+=O+(S||0)}),E-=S;const A=E>i?E-i:0;p=p.map(O=>O<=0?-m:O>A?A+w:O)}if(n.centerInsufficientSlides){let E=0;v.forEach(O=>{E+=O+(S||0)}),E-=S;const A=(n.slidesOffsetBefore||0)+(n.slidesOffsetAfter||0);if(E+A<i){const O=(i-E-A)/2;p.forEach((L,N)=>{p[N]=L-O}),h.forEach((L,N)=>{h[N]=L+O})}}if(Object.assign(e,{slides:d,snapGrid:p,slidesGrid:h,slidesSizesGrid:v}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){et(r,"--swiper-centered-offset-before",`${-p[0]}px`),et(r,"--swiper-centered-offset-after",`${e.size/2-v[v.length-1]/2}px`);const E=-e.snapGrid[0],A=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map(O=>O+E),e.slidesGrid=e.slidesGrid.map(O=>O+A)}if(u!==c&&e.emit("slidesLengthChange"),p.length!==g&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),h.length!==x&&e.emit("slidesGridLengthChange"),n.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!l&&!n.cssMode&&(n.effect==="slide"||n.effect==="fade")){const E=`${n.containerModifierClass}backface-hidden`,A=e.el.classList.contains(E);u<=n.maxBackfaceHiddenSlides?A||e.el.classList.add(E):A&&e.el.classList.remove(E)}}function ql(e){const t=this,n=[],r=t.virtual&&t.params.virtual.enabled;let o=0,i;typeof e=="number"?t.setTransition(e):e===!0&&t.setTransition(t.params.speed);const s=a=>r?t.slides[t.getSlideIndexByData(a)]:t.slides[a];if(t.params.slidesPerView!=="auto"&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach(a=>{n.push(a)});else for(i=0;i<Math.ceil(t.params.slidesPerView);i+=1){const a=t.activeIndex+i;if(a>t.slides.length&&!r)break;n.push(s(a))}else n.push(s(t.activeIndex));for(i=0;i<n.length;i+=1)if(typeof n[i]<"u"){const a=n[i].offsetHeight;o=a>o?a:o}(o||o===0)&&(t.wrapperEl.style.height=`${o}px`)}function Kl(){const e=this,t=e.slides,n=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let r=0;r<t.length;r+=1)t[r].swiperSlideOffset=(e.isHorizontal()?t[r].offsetLeft:t[r].offsetTop)-n-e.cssOverflowAdjustment()}const pr=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)};function Zl(e){e===void 0&&(e=this&&this.translate||0);const t=this,n=t.params,{slides:r,rtlTranslate:o,snapGrid:i}=t;if(r.length===0)return;typeof r[0].swiperSlideOffset>"u"&&t.updateSlidesOffset();let s=-e;o&&(s=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let a=n.spaceBetween;typeof a=="string"&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*t.size:typeof a=="string"&&(a=parseFloat(a));for(let l=0;l<r.length;l+=1){const c=r[l];let d=c.swiperSlideOffset;n.cssMode&&n.centeredSlides&&(d-=r[0].swiperSlideOffset);const u=(s+(n.centeredSlides?t.minTranslate():0)-d)/(c.swiperSlideSize+a),p=(s-i[0]+(n.centeredSlides?t.minTranslate():0)-d)/(c.swiperSlideSize+a),h=-(s-d),v=h+t.slidesSizesGrid[l],m=h>=0&&h<=t.size-t.slidesSizesGrid[l],w=h>=0&&h<t.size-1||v>1&&v<=t.size||h<=0&&v>=t.size;w&&(t.visibleSlides.push(c),t.visibleSlidesIndexes.push(l)),pr(c,w,n.slideVisibleClass),pr(c,m,n.slideFullyVisibleClass),c.progress=o?-u:u,c.originalProgress=o?-p:p}}function Ql(e){const t=this;if(typeof e>"u"){const d=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*d||0}const n=t.params,r=t.maxTranslate()-t.minTranslate();let{progress:o,isBeginning:i,isEnd:s,progressLoop:a}=t;const l=i,c=s;if(r===0)o=0,i=!0,s=!0;else{o=(e-t.minTranslate())/r;const d=Math.abs(e-t.minTranslate())<1,u=Math.abs(e-t.maxTranslate())<1;i=d||o<=0,s=u||o>=1,d&&(o=0),u&&(o=1)}if(n.loop){const d=t.getSlideIndexByData(0),u=t.getSlideIndexByData(t.slides.length-1),p=t.slidesGrid[d],h=t.slidesGrid[u],v=t.slidesGrid[t.slidesGrid.length-1],m=Math.abs(e);m>=p?a=(m-p)/v:a=(m+v-h)/v,a>1&&(a-=1)}Object.assign(t,{progress:o,progressLoop:a,isBeginning:i,isEnd:s}),(n.watchSlidesProgress||n.centeredSlides&&n.autoHeight)&&t.updateSlidesProgress(e),i&&!l&&t.emit("reachBeginning toEdge"),s&&!c&&t.emit("reachEnd toEdge"),(l&&!i||c&&!s)&&t.emit("fromEdge"),t.emit("progress",o)}const zt=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)};function Jl(){const e=this,{slides:t,params:n,slidesEl:r,activeIndex:o}=e,i=e.virtual&&n.virtual.enabled,s=e.grid&&n.grid&&n.grid.rows>1,a=u=>ne(r,`.${n.slideClass}${u}, swiper-slide${u}`)[0];let l,c,d;if(i)if(n.loop){let u=o-e.virtual.slidesBefore;u<0&&(u=e.virtual.slides.length+u),u>=e.virtual.slides.length&&(u-=e.virtual.slides.length),l=a(`[data-swiper-slide-index="${u}"]`)}else l=a(`[data-swiper-slide-index="${o}"]`);else s?(l=t.find(u=>u.column===o),d=t.find(u=>u.column===o+1),c=t.find(u=>u.column===o-1)):l=t[o];l&&(s||(d=zl(l,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!d&&(d=t[0]),c=jl(l,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!c===0&&(c=t[t.length-1]))),t.forEach(u=>{zt(u,u===l,n.slideActiveClass),zt(u,u===d,n.slideNextClass),zt(u,u===c,n.slidePrevClass)}),e.emitSlidesClasses()}const at=(e,t)=>{if(!e||e.destroyed||!e.params)return;const n=()=>e.isElement?"swiper-slide":`.${e.params.slideClass}`,r=t.closest(n());if(r){let o=r.querySelector(`.${e.params.lazyPreloaderClass}`);!o&&e.isElement&&(r.shadowRoot?o=r.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{r.shadowRoot&&(o=r.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),o&&o.remove())})),o&&o.remove()}},Ft=(e,t)=>{if(!e.slides[t])return;const n=e.slides[t].querySelector('[loading="lazy"]');n&&n.removeAttribute("loading")},sn=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const n=e.slides.length;if(!n||!t||t<0)return;t=Math.min(t,n);const r=e.params.slidesPerView==="auto"?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),o=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const s=o,a=[s-t];a.push(...Array.from({length:t}).map((l,c)=>s+r+c)),e.slides.forEach((l,c)=>{a.includes(l.column)&&Ft(e,c)});return}const i=o+r-1;if(e.params.rewind||e.params.loop)for(let s=o-t;s<=i+t;s+=1){const a=(s%n+n)%n;(a<o||a>i)&&Ft(e,a)}else for(let s=Math.max(o-t,0);s<=Math.min(i+t,n-1);s+=1)s!==o&&(s>i||s<o)&&Ft(e,s)};function ec(e){const{slidesGrid:t,params:n}=e,r=e.rtlTranslate?e.translate:-e.translate;let o;for(let i=0;i<t.length;i+=1)typeof t[i+1]<"u"?r>=t[i]&&r<t[i+1]-(t[i+1]-t[i])/2?o=i:r>=t[i]&&r<t[i+1]&&(o=i+1):r>=t[i]&&(o=i);return n.normalizeSlideIndex&&(o<0||typeof o>"u")&&(o=0),o}function tc(e){const t=this,n=t.rtlTranslate?t.translate:-t.translate,{snapGrid:r,params:o,activeIndex:i,realIndex:s,snapIndex:a}=t;let l=e,c;const d=h=>{let v=h-t.virtual.slidesBefore;return v<0&&(v=t.virtual.slides.length+v),v>=t.virtual.slides.length&&(v-=t.virtual.slides.length),v};if(typeof l>"u"&&(l=ec(t)),r.indexOf(n)>=0)c=r.indexOf(n);else{const h=Math.min(o.slidesPerGroupSkip,l);c=h+Math.floor((l-h)/o.slidesPerGroup)}if(c>=r.length&&(c=r.length-1),l===i&&!t.params.loop){c!==a&&(t.snapIndex=c,t.emit("snapIndexChange"));return}if(l===i&&t.params.loop&&t.virtual&&t.params.virtual.enabled){t.realIndex=d(l);return}const u=t.grid&&o.grid&&o.grid.rows>1;let p;if(t.virtual&&o.virtual.enabled&&o.loop)p=d(l);else if(u){const h=t.slides.find(m=>m.column===l);let v=parseInt(h.getAttribute("data-swiper-slide-index"),10);Number.isNaN(v)&&(v=Math.max(t.slides.indexOf(h),0)),p=Math.floor(v/o.grid.rows)}else if(t.slides[l]){const h=t.slides[l].getAttribute("data-swiper-slide-index");h?p=parseInt(h,10):p=l}else p=l;Object.assign(t,{previousSnapIndex:a,snapIndex:c,previousRealIndex:s,realIndex:p,previousIndex:i,activeIndex:l}),t.initialized&&sn(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(s!==p&&t.emit("realIndexChange"),t.emit("slideChange"))}function nc(e,t){const n=this,r=n.params;let o=e.closest(`.${r.slideClass}, swiper-slide`);!o&&n.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(a=>{!o&&a.matches&&a.matches(`.${r.slideClass}, swiper-slide`)&&(o=a)});let i=!1,s;if(o){for(let a=0;a<n.slides.length;a+=1)if(n.slides[a]===o){i=!0,s=a;break}}if(o&&i)n.clickedSlide=o,n.virtual&&n.params.virtual.enabled?n.clickedIndex=parseInt(o.getAttribute("data-swiper-slide-index"),10):n.clickedIndex=s;else{n.clickedSlide=void 0,n.clickedIndex=void 0;return}r.slideToClickedSlide&&n.clickedIndex!==void 0&&n.clickedIndex!==n.activeIndex&&n.slideToClickedSlide()}var rc={updateSize:Yl,updateSlides:Xl,updateAutoHeight:ql,updateSlidesOffset:Kl,updateSlidesProgress:Zl,updateProgress:Ql,updateSlidesClasses:Jl,updateActiveIndex:tc,updateClickedSlide:nc};function oc(e){e===void 0&&(e=this.isHorizontal()?"x":"y");const t=this,{params:n,rtlTranslate:r,translate:o,wrapperEl:i}=t;if(n.virtualTranslate)return r?-o:o;if(n.cssMode)return o;let s=Ll(i,e);return s+=t.cssOverflowAdjustment(),r&&(s=-s),s||0}function ic(e,t){const n=this,{rtlTranslate:r,params:o,wrapperEl:i,progress:s}=n;let a=0,l=0;const c=0;n.isHorizontal()?a=r?-e:e:l=e,o.roundLengths&&(a=Math.floor(a),l=Math.floor(l)),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?a:l,o.cssMode?i[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-a:-l:o.virtualTranslate||(n.isHorizontal()?a-=n.cssOverflowAdjustment():l-=n.cssOverflowAdjustment(),i.style.transform=`translate3d(${a}px, ${l}px, ${c}px)`);let d;const u=n.maxTranslate()-n.minTranslate();u===0?d=0:d=(e-n.minTranslate())/u,d!==s&&n.updateProgress(e),n.emit("setTranslate",n.translate,t)}function sc(){return-this.snapGrid[0]}function ac(){return-this.snapGrid[this.snapGrid.length-1]}function lc(e,t,n,r,o){e===void 0&&(e=0),t===void 0&&(t=this.params.speed),n===void 0&&(n=!0),r===void 0&&(r=!0);const i=this,{params:s,wrapperEl:a}=i;if(i.animating&&s.preventInteractionOnTransition)return!1;const l=i.minTranslate(),c=i.maxTranslate();let d;if(r&&e>l?d=l:r&&e<c?d=c:d=e,i.updateProgress(d),s.cssMode){const u=i.isHorizontal();if(t===0)a[u?"scrollLeft":"scrollTop"]=-d;else{if(!i.support.smoothScroll)return yo({swiper:i,targetPosition:-d,side:u?"left":"top"}),!0;a.scrollTo({[u?"left":"top"]:-d,behavior:"smooth"})}return!0}return t===0?(i.setTransition(0),i.setTranslate(d),n&&(i.emit("beforeTransitionStart",t,o),i.emit("transitionEnd"))):(i.setTransition(t),i.setTranslate(d),n&&(i.emit("beforeTransitionStart",t,o),i.emit("transitionStart")),i.animating||(i.animating=!0,i.onTranslateToWrapperTransitionEnd||(i.onTranslateToWrapperTransitionEnd=function(p){!i||i.destroyed||p.target===this&&(i.wrapperEl.removeEventListener("transitionend",i.onTranslateToWrapperTransitionEnd),i.onTranslateToWrapperTransitionEnd=null,delete i.onTranslateToWrapperTransitionEnd,i.animating=!1,n&&i.emit("transitionEnd"))}),i.wrapperEl.addEventListener("transitionend",i.onTranslateToWrapperTransitionEnd))),!0}var cc={getTranslate:oc,setTranslate:ic,minTranslate:sc,maxTranslate:ac,translateTo:lc};function dc(e,t){const n=this;n.params.cssMode||(n.wrapperEl.style.transitionDuration=`${e}ms`,n.wrapperEl.style.transitionDelay=e===0?"0ms":""),n.emit("setTransition",e,t)}function Co(e){let{swiper:t,runCallbacks:n,direction:r,step:o}=e;const{activeIndex:i,previousIndex:s}=t;let a=r;a||(i>s?a="next":i<s?a="prev":a="reset"),t.emit(`transition${o}`),n&&a==="reset"?t.emit(`slideResetTransition${o}`):n&&i!==s&&(t.emit(`slideChangeTransition${o}`),a==="next"?t.emit(`slideNextTransition${o}`):t.emit(`slidePrevTransition${o}`))}function uc(e,t){e===void 0&&(e=!0);const n=this,{params:r}=n;r.cssMode||(r.autoHeight&&n.updateAutoHeight(),Co({swiper:n,runCallbacks:e,direction:t,step:"Start"}))}function fc(e,t){e===void 0&&(e=!0);const n=this,{params:r}=n;n.animating=!1,!r.cssMode&&(n.setTransition(0),Co({swiper:n,runCallbacks:e,direction:t,step:"End"}))}var pc={setTransition:dc,transitionStart:uc,transitionEnd:fc};function hc(e,t,n,r,o){e===void 0&&(e=0),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const i=this;let s=e;s<0&&(s=0);const{params:a,snapGrid:l,slidesGrid:c,previousIndex:d,activeIndex:u,rtlTranslate:p,wrapperEl:h,enabled:v}=i;if(!v&&!r&&!o||i.destroyed||i.animating&&a.preventInteractionOnTransition)return!1;typeof t>"u"&&(t=i.params.speed);const m=Math.min(i.params.slidesPerGroupSkip,s);let w=m+Math.floor((s-m)/i.params.slidesPerGroup);w>=l.length&&(w=l.length-1);const g=-l[w];if(a.normalizeSlideIndex)for(let P=0;P<c.length;P+=1){const M=-Math.floor(g*100),R=Math.floor(c[P]*100),E=Math.floor(c[P+1]*100);typeof c[P+1]<"u"?M>=R&&M<E-(E-R)/2?s=P:M>=R&&M<E&&(s=P+1):M>=R&&(s=P)}if(i.initialized&&s!==u&&(!i.allowSlideNext&&(p?g>i.translate&&g>i.minTranslate():g<i.translate&&g<i.minTranslate())||!i.allowSlidePrev&&g>i.translate&&g>i.maxTranslate()&&(u||0)!==s))return!1;s!==(d||0)&&n&&i.emit("beforeSlideChangeStart"),i.updateProgress(g);let x;s>u?x="next":s<u?x="prev":x="reset";const S=i.virtual&&i.params.virtual.enabled;if(!(S&&o)&&(p&&-g===i.translate||!p&&g===i.translate))return i.updateActiveIndex(s),a.autoHeight&&i.updateAutoHeight(),i.updateSlidesClasses(),a.effect!=="slide"&&i.setTranslate(g),x!=="reset"&&(i.transitionStart(n,x),i.transitionEnd(n,x)),!1;if(a.cssMode){const P=i.isHorizontal(),M=p?g:-g;if(t===0)S&&(i.wrapperEl.style.scrollSnapType="none",i._immediateVirtual=!0),S&&!i._cssModeVirtualInitialSet&&i.params.initialSlide>0?(i._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{h[P?"scrollLeft":"scrollTop"]=M})):h[P?"scrollLeft":"scrollTop"]=M,S&&requestAnimationFrame(()=>{i.wrapperEl.style.scrollSnapType="",i._immediateVirtual=!1});else{if(!i.support.smoothScroll)return yo({swiper:i,targetPosition:M,side:P?"left":"top"}),!0;h.scrollTo({[P?"left":"top"]:M,behavior:"smooth"})}return!0}const T=bo().isSafari;return S&&!o&&T&&i.isElement&&i.virtual.update(!1,!1,s),i.setTransition(t),i.setTranslate(g),i.updateActiveIndex(s),i.updateSlidesClasses(),i.emit("beforeTransitionStart",t,r),i.transitionStart(n,x),t===0?i.transitionEnd(n,x):i.animating||(i.animating=!0,i.onSlideToWrapperTransitionEnd||(i.onSlideToWrapperTransitionEnd=function(M){!i||i.destroyed||M.target===this&&(i.wrapperEl.removeEventListener("transitionend",i.onSlideToWrapperTransitionEnd),i.onSlideToWrapperTransitionEnd=null,delete i.onSlideToWrapperTransitionEnd,i.transitionEnd(n,x))}),i.wrapperEl.addEventListener("transitionend",i.onSlideToWrapperTransitionEnd)),!0}function mc(e,t,n,r){e===void 0&&(e=0),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const o=this;if(o.destroyed)return;typeof t>"u"&&(t=o.params.speed);const i=o.grid&&o.params.grid&&o.params.grid.rows>1;let s=e;if(o.params.loop)if(o.virtual&&o.params.virtual.enabled)s=s+o.virtual.slidesBefore;else{let a;if(i){const p=s*o.params.grid.rows;a=o.slides.find(h=>h.getAttribute("data-swiper-slide-index")*1===p).column}else a=o.getSlideIndexByData(s);const l=i?Math.ceil(o.slides.length/o.params.grid.rows):o.slides.length,{centeredSlides:c}=o.params;let d=o.params.slidesPerView;d==="auto"?d=o.slidesPerViewDynamic():(d=Math.ceil(parseFloat(o.params.slidesPerView,10)),c&&d%2===0&&(d=d+1));let u=l-a<d;if(c&&(u=u||a<Math.ceil(d/2)),r&&c&&o.params.slidesPerView!=="auto"&&!i&&(u=!1),u){const p=c?a<o.activeIndex?"prev":"next":a-o.activeIndex-1<o.params.slidesPerView?"next":"prev";o.loopFix({direction:p,slideTo:!0,activeSlideIndex:p==="next"?a+1:a-l+1,slideRealIndex:p==="next"?o.realIndex:void 0})}if(i){const p=s*o.params.grid.rows;s=o.slides.find(h=>h.getAttribute("data-swiper-slide-index")*1===p).column}else s=o.getSlideIndexByData(s)}return requestAnimationFrame(()=>{o.slideTo(s,t,n,r)}),o}function vc(e,t,n){t===void 0&&(t=!0);const r=this,{enabled:o,params:i,animating:s}=r;if(!o||r.destroyed)return r;typeof e>"u"&&(e=r.params.speed);let a=i.slidesPerGroup;i.slidesPerView==="auto"&&i.slidesPerGroup===1&&i.slidesPerGroupAuto&&(a=Math.max(r.slidesPerViewDynamic("current",!0),1));const l=r.activeIndex<i.slidesPerGroupSkip?1:a,c=r.virtual&&i.virtual.enabled;if(i.loop){if(s&&!c&&i.loopPreventsSliding)return!1;if(r.loopFix({direction:"next"}),r._clientLeft=r.wrapperEl.clientLeft,r.activeIndex===r.slides.length-1&&i.cssMode)return requestAnimationFrame(()=>{r.slideTo(r.activeIndex+l,e,t,n)}),!0}return i.rewind&&r.isEnd?r.slideTo(0,e,t,n):r.slideTo(r.activeIndex+l,e,t,n)}function gc(e,t,n){t===void 0&&(t=!0);const r=this,{params:o,snapGrid:i,slidesGrid:s,rtlTranslate:a,enabled:l,animating:c}=r;if(!l||r.destroyed)return r;typeof e>"u"&&(e=r.params.speed);const d=r.virtual&&o.virtual.enabled;if(o.loop){if(c&&!d&&o.loopPreventsSliding)return!1;r.loopFix({direction:"prev"}),r._clientLeft=r.wrapperEl.clientLeft}const u=a?r.translate:-r.translate;function p(x){return x<0?-Math.floor(Math.abs(x)):Math.floor(x)}const h=p(u),v=i.map(x=>p(x)),m=o.freeMode&&o.freeMode.enabled;let w=i[v.indexOf(h)-1];if(typeof w>"u"&&(o.cssMode||m)){let x;i.forEach((S,b)=>{h>=S&&(x=b)}),typeof x<"u"&&(w=m?i[x]:i[x>0?x-1:x])}let g=0;if(typeof w<"u"&&(g=s.indexOf(w),g<0&&(g=r.activeIndex-1),o.slidesPerView==="auto"&&o.slidesPerGroup===1&&o.slidesPerGroupAuto&&(g=g-r.slidesPerViewDynamic("previous",!0)+1,g=Math.max(g,0))),o.rewind&&r.isBeginning){const x=r.params.virtual&&r.params.virtual.enabled&&r.virtual?r.virtual.slides.length-1:r.slides.length-1;return r.slideTo(x,e,t,n)}else if(o.loop&&r.activeIndex===0&&o.cssMode)return requestAnimationFrame(()=>{r.slideTo(g,e,t,n)}),!0;return r.slideTo(g,e,t,n)}function wc(e,t,n){t===void 0&&(t=!0);const r=this;if(!r.destroyed)return typeof e>"u"&&(e=r.params.speed),r.slideTo(r.activeIndex,e,t,n)}function yc(e,t,n,r){t===void 0&&(t=!0),r===void 0&&(r=.5);const o=this;if(o.destroyed)return;typeof e>"u"&&(e=o.params.speed);let i=o.activeIndex;const s=Math.min(o.params.slidesPerGroupSkip,i),a=s+Math.floor((i-s)/o.params.slidesPerGroup),l=o.rtlTranslate?o.translate:-o.translate;if(l>=o.snapGrid[a]){const c=o.snapGrid[a],d=o.snapGrid[a+1];l-c>(d-c)*r&&(i+=o.params.slidesPerGroup)}else{const c=o.snapGrid[a-1],d=o.snapGrid[a];l-c<=(d-c)*r&&(i-=o.params.slidesPerGroup)}return i=Math.max(i,0),i=Math.min(i,o.slidesGrid.length-1),o.slideTo(i,e,t,n)}function xc(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:n}=e,r=t.slidesPerView==="auto"?e.slidesPerViewDynamic():t.slidesPerView;let o=e.clickedIndex,i;const s=e.isElement?"swiper-slide":`.${t.slideClass}`;if(t.loop){if(e.animating)return;i=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?o<e.loopedSlides-r/2||o>e.slides.length-e.loopedSlides+r/2?(e.loopFix(),o=e.getSlideIndex(ne(n,`${s}[data-swiper-slide-index="${i}"]`)[0]),rn(()=>{e.slideTo(o)})):e.slideTo(o):o>e.slides.length-r?(e.loopFix(),o=e.getSlideIndex(ne(n,`${s}[data-swiper-slide-index="${i}"]`)[0]),rn(()=>{e.slideTo(o)})):e.slideTo(o)}else e.slideTo(o)}var Sc={slideTo:hc,slideToLoop:mc,slideNext:vc,slidePrev:gc,slideReset:wc,slideToClosest:yc,slideToClickedSlide:xc};function bc(e,t){const n=this,{params:r,slidesEl:o}=n;if(!r.loop||n.virtual&&n.params.virtual.enabled)return;const i=()=>{ne(o,`.${r.slideClass}, swiper-slide`).forEach((p,h)=>{p.setAttribute("data-swiper-slide-index",h)})},s=n.grid&&r.grid&&r.grid.rows>1,a=r.slidesPerGroup*(s?r.grid.rows:1),l=n.slides.length%a!==0,c=s&&n.slides.length%r.grid.rows!==0,d=u=>{for(let p=0;p<u;p+=1){const h=n.isElement?on("swiper-slide",[r.slideBlankClass]):on("div",[r.slideClass,r.slideBlankClass]);n.slidesEl.append(h)}};if(l){if(r.loopAddBlankSlides){const u=a-n.slides.length%a;d(u),n.recalcSlides(),n.updateSlides()}else mt("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");i()}else if(c){if(r.loopAddBlankSlides){const u=r.grid.rows-n.slides.length%r.grid.rows;d(u),n.recalcSlides(),n.updateSlides()}else mt("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");i()}else i();n.loopFix({slideRealIndex:e,direction:r.centeredSlides?void 0:"next",initial:t})}function Cc(e){let{slideRealIndex:t,slideTo:n=!0,direction:r,setTranslate:o,activeSlideIndex:i,initial:s,byController:a,byMousewheel:l}=e===void 0?{}:e;const c=this;if(!c.params.loop)return;c.emit("beforeLoopFix");const{slides:d,allowSlidePrev:u,allowSlideNext:p,slidesEl:h,params:v}=c,{centeredSlides:m,initialSlide:w}=v;if(c.allowSlidePrev=!0,c.allowSlideNext=!0,c.virtual&&v.virtual.enabled){n&&(!v.centeredSlides&&c.snapIndex===0?c.slideTo(c.virtual.slides.length,0,!1,!0):v.centeredSlides&&c.snapIndex<v.slidesPerView?c.slideTo(c.virtual.slides.length+c.snapIndex,0,!1,!0):c.snapIndex===c.snapGrid.length-1&&c.slideTo(c.virtual.slidesBefore,0,!1,!0)),c.allowSlidePrev=u,c.allowSlideNext=p,c.emit("loopFix");return}let g=v.slidesPerView;g==="auto"?g=c.slidesPerViewDynamic():(g=Math.ceil(parseFloat(v.slidesPerView,10)),m&&g%2===0&&(g=g+1));const x=v.slidesPerGroupAuto?g:v.slidesPerGroup;let S=x;S%x!==0&&(S+=x-S%x),S+=v.loopAdditionalSlides,c.loopedSlides=S;const b=c.grid&&v.grid&&v.grid.rows>1;d.length<g+S||c.params.effect==="cards"&&d.length<g+S*2?mt("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):b&&v.grid.fill==="row"&&mt("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const C=[],T=[],P=b?Math.ceil(d.length/v.grid.rows):d.length,M=s&&P-w<g&&!m;let R=M?w:c.activeIndex;typeof i>"u"?i=c.getSlideIndex(d.find(k=>k.classList.contains(v.slideActiveClass))):R=i;const E=r==="next"||!r,A=r==="prev"||!r;let O=0,L=0;const z=(b?d[i].column:i)+(m&&typeof o>"u"?-g/2+.5:0);if(z<S){O=Math.max(S-z,x);for(let k=0;k<S-z;k+=1){const _=k-Math.floor(k/P)*P;if(b){const I=P-_-1;for(let $=d.length-1;$>=0;$-=1)d[$].column===I&&C.push($)}else C.push(P-_-1)}}else if(z+g>P-S){L=Math.max(z-(P-S*2),x),M&&(L=Math.max(L,g-P+w+1));for(let k=0;k<L;k+=1){const _=k-Math.floor(k/P)*P;b?d.forEach((I,$)=>{I.column===_&&T.push($)}):T.push(_)}}if(c.__preventObserver__=!0,requestAnimationFrame(()=>{c.__preventObserver__=!1}),c.params.effect==="cards"&&d.length<g+S*2&&(T.includes(i)&&T.splice(T.indexOf(i),1),C.includes(i)&&C.splice(C.indexOf(i),1)),A&&C.forEach(k=>{d[k].swiperLoopMoveDOM=!0,h.prepend(d[k]),d[k].swiperLoopMoveDOM=!1}),E&&T.forEach(k=>{d[k].swiperLoopMoveDOM=!0,h.append(d[k]),d[k].swiperLoopMoveDOM=!1}),c.recalcSlides(),v.slidesPerView==="auto"?c.updateSlides():b&&(C.length>0&&A||T.length>0&&E)&&c.slides.forEach((k,_)=>{c.grid.updateSlide(_,k,c.slides)}),v.watchSlidesProgress&&c.updateSlidesOffset(),n){if(C.length>0&&A){if(typeof t>"u"){const k=c.slidesGrid[R],I=c.slidesGrid[R+O]-k;l?c.setTranslate(c.translate-I):(c.slideTo(R+Math.ceil(O),0,!1,!0),o&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-I,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-I))}else if(o){const k=b?C.length/v.grid.rows:C.length;c.slideTo(c.activeIndex+k,0,!1,!0),c.touchEventsData.currentTranslate=c.translate}}else if(T.length>0&&E)if(typeof t>"u"){const k=c.slidesGrid[R],I=c.slidesGrid[R-L]-k;l?c.setTranslate(c.translate-I):(c.slideTo(R-L,0,!1,!0),o&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-I,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-I))}else{const k=b?T.length/v.grid.rows:T.length;c.slideTo(c.activeIndex-k,0,!1,!0)}}if(c.allowSlidePrev=u,c.allowSlideNext=p,c.controller&&c.controller.control&&!a){const k={slideRealIndex:t,direction:r,setTranslate:o,activeSlideIndex:i,byController:!0};Array.isArray(c.controller.control)?c.controller.control.forEach(_=>{!_.destroyed&&_.params.loop&&_.loopFix({...k,slideTo:_.params.slidesPerView===v.slidesPerView?n:!1})}):c.controller.control instanceof c.constructor&&c.controller.control.params.loop&&c.controller.control.loopFix({...k,slideTo:c.controller.control.params.slidesPerView===v.slidesPerView?n:!1})}c.emit("loopFix")}function Ec(){const e=this,{params:t,slidesEl:n}=e;if(!t.loop||!n||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const r=[];e.slides.forEach(o=>{const i=typeof o.swiperSlideIndex>"u"?o.getAttribute("data-swiper-slide-index")*1:o.swiperSlideIndex;r[i]=o}),e.slides.forEach(o=>{o.removeAttribute("data-swiper-slide-index")}),r.forEach(o=>{n.append(o)}),e.recalcSlides(),e.slideTo(e.realIndex,0)}var Tc={loopCreate:bc,loopFix:Cc,loopDestroy:Ec};function Pc(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const n=t.params.touchEventsTarget==="container"?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),n.style.cursor="move",n.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})}function Ac(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e[e.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}var Mc={setGrabCursor:Pc,unsetGrabCursor:Ac};function Rc(e,t){t===void 0&&(t=this);function n(r){if(!r||r===Ee()||r===H())return null;r.assignedSlot&&(r=r.assignedSlot);const o=r.closest(e);return!o&&!r.getRootNode?null:o||n(r.getRootNode().host)}return n(t)}function hr(e,t,n){const r=H(),{params:o}=e,i=o.edgeSwipeDetection,s=o.edgeSwipeThreshold;return i&&(n<=s||n>=r.innerWidth-s)?i==="prevent"?(t.preventDefault(),!0):!1:!0}function kc(e){const t=this,n=Ee();let r=e;r.originalEvent&&(r=r.originalEvent);const o=t.touchEventsData;if(r.type==="pointerdown"){if(o.pointerId!==null&&o.pointerId!==r.pointerId)return;o.pointerId=r.pointerId}else r.type==="touchstart"&&r.targetTouches.length===1&&(o.touchId=r.targetTouches[0].identifier);if(r.type==="touchstart"){hr(t,r,r.targetTouches[0].pageX);return}const{params:i,touches:s,enabled:a}=t;if(!a||!i.simulateTouch&&r.pointerType==="mouse"||t.animating&&i.preventInteractionOnTransition)return;!t.animating&&i.cssMode&&i.loop&&t.loopFix();let l=r.target;if(i.touchEventsTarget==="wrapper"&&!$l(l,t.wrapperEl)||"which"in r&&r.which===3||"button"in r&&r.button>0||o.isTouched&&o.isMoved)return;const c=!!i.noSwipingClass&&i.noSwipingClass!=="",d=r.composedPath?r.composedPath():r.path;c&&r.target&&r.target.shadowRoot&&d&&(l=d[0]);const u=i.noSwipingSelector?i.noSwipingSelector:`.${i.noSwipingClass}`,p=!!(r.target&&r.target.shadowRoot);if(i.noSwiping&&(p?Rc(u,l):l.closest(u))){t.allowClick=!0;return}if(i.swipeHandler&&!l.closest(i.swipeHandler))return;s.currentX=r.pageX,s.currentY=r.pageY;const h=s.currentX,v=s.currentY;if(!hr(t,r,h))return;Object.assign(o,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),s.startX=h,s.startY=v,o.touchStartTime=ht(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,i.threshold>0&&(o.allowThresholdMove=!1);let m=!0;l.matches(o.focusableElements)&&(m=!1,l.nodeName==="SELECT"&&(o.isTouched=!1)),n.activeElement&&n.activeElement.matches(o.focusableElements)&&n.activeElement!==l&&(r.pointerType==="mouse"||r.pointerType!=="mouse"&&!l.matches(o.focusableElements))&&n.activeElement.blur();const w=m&&t.allowTouchMove&&i.touchStartPreventDefault;(i.touchStartForcePreventDefault||w)&&!l.isContentEditable&&r.preventDefault(),i.freeMode&&i.freeMode.enabled&&t.freeMode&&t.animating&&!i.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",r)}function Oc(e){const t=Ee(),n=this,r=n.touchEventsData,{params:o,touches:i,rtlTranslate:s,enabled:a}=n;if(!a||!o.simulateTouch&&e.pointerType==="mouse")return;let l=e;if(l.originalEvent&&(l=l.originalEvent),l.type==="pointermove"&&(r.touchId!==null||l.pointerId!==r.pointerId))return;let c;if(l.type==="touchmove"){if(c=[...l.changedTouches].find(T=>T.identifier===r.touchId),!c||c.identifier!==r.touchId)return}else c=l;if(!r.isTouched){r.startMoving&&r.isScrolling&&n.emit("touchMoveOpposite",l);return}const d=c.pageX,u=c.pageY;if(l.preventedByNestedSwiper){i.startX=d,i.startY=u;return}if(!n.allowTouchMove){l.target.matches(r.focusableElements)||(n.allowClick=!1),r.isTouched&&(Object.assign(i,{startX:d,startY:u,currentX:d,currentY:u}),r.touchStartTime=ht());return}if(o.touchReleaseOnEdges&&!o.loop)if(n.isVertical()){if(u<i.startY&&n.translate<=n.maxTranslate()||u>i.startY&&n.translate>=n.minTranslate()){r.isTouched=!1,r.isMoved=!1;return}}else{if(s&&(d>i.startX&&-n.translate<=n.maxTranslate()||d<i.startX&&-n.translate>=n.minTranslate()))return;if(!s&&(d<i.startX&&n.translate<=n.maxTranslate()||d>i.startX&&n.translate>=n.minTranslate()))return}if(t.activeElement&&t.activeElement.matches(r.focusableElements)&&t.activeElement!==l.target&&l.pointerType!=="mouse"&&t.activeElement.blur(),t.activeElement&&l.target===t.activeElement&&l.target.matches(r.focusableElements)){r.isMoved=!0,n.allowClick=!1;return}r.allowTouchCallbacks&&n.emit("touchMove",l),i.previousX=i.currentX,i.previousY=i.currentY,i.currentX=d,i.currentY=u;const p=i.currentX-i.startX,h=i.currentY-i.startY;if(n.params.threshold&&Math.sqrt(p**2+h**2)<n.params.threshold)return;if(typeof r.isScrolling>"u"){let T;n.isHorizontal()&&i.currentY===i.startY||n.isVertical()&&i.currentX===i.startX?r.isScrolling=!1:p*p+h*h>=25&&(T=Math.atan2(Math.abs(h),Math.abs(p))*180/Math.PI,r.isScrolling=n.isHorizontal()?T>o.touchAngle:90-T>o.touchAngle)}if(r.isScrolling&&n.emit("touchMoveOpposite",l),typeof r.startMoving>"u"&&(i.currentX!==i.startX||i.currentY!==i.startY)&&(r.startMoving=!0),r.isScrolling||l.type==="touchmove"&&r.preventTouchMoveFromPointerMove){r.isTouched=!1;return}if(!r.startMoving)return;n.allowClick=!1,!o.cssMode&&l.cancelable&&l.preventDefault(),o.touchMoveStopPropagation&&!o.nested&&l.stopPropagation();let v=n.isHorizontal()?p:h,m=n.isHorizontal()?i.currentX-i.previousX:i.currentY-i.previousY;o.oneWayMovement&&(v=Math.abs(v)*(s?1:-1),m=Math.abs(m)*(s?1:-1)),i.diff=v,v*=o.touchRatio,s&&(v=-v,m=-m);const w=n.touchesDirection;n.swipeDirection=v>0?"prev":"next",n.touchesDirection=m>0?"prev":"next";const g=n.params.loop&&!o.cssMode,x=n.touchesDirection==="next"&&n.allowSlideNext||n.touchesDirection==="prev"&&n.allowSlidePrev;if(!r.isMoved){if(g&&x&&n.loopFix({direction:n.swipeDirection}),r.startTranslate=n.getTranslate(),n.setTransition(0),n.animating){const T=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});n.wrapperEl.dispatchEvent(T)}r.allowMomentumBounce=!1,o.grabCursor&&(n.allowSlideNext===!0||n.allowSlidePrev===!0)&&n.setGrabCursor(!0),n.emit("sliderFirstMove",l)}let S;if(new Date().getTime(),o._loopSwapReset!==!1&&r.isMoved&&r.allowThresholdMove&&w!==n.touchesDirection&&g&&x&&Math.abs(v)>=1){Object.assign(i,{startX:d,startY:u,currentX:d,currentY:u,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,r.startTranslate=r.currentTranslate;return}n.emit("sliderMove",l),r.isMoved=!0,r.currentTranslate=v+r.startTranslate;let b=!0,C=o.resistanceRatio;if(o.touchReleaseOnEdges&&(C=0),v>0?(g&&x&&!S&&r.allowThresholdMove&&r.currentTranslate>(o.centeredSlides?n.minTranslate()-n.slidesSizesGrid[n.activeIndex+1]-(o.slidesPerView!=="auto"&&n.slides.length-o.slidesPerView>=2?n.slidesSizesGrid[n.activeIndex+1]+n.params.spaceBetween:0)-n.params.spaceBetween:n.minTranslate())&&n.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>n.minTranslate()&&(b=!1,o.resistance&&(r.currentTranslate=n.minTranslate()-1+(-n.minTranslate()+r.startTranslate+v)**C))):v<0&&(g&&x&&!S&&r.allowThresholdMove&&r.currentTranslate<(o.centeredSlides?n.maxTranslate()+n.slidesSizesGrid[n.slidesSizesGrid.length-1]+n.params.spaceBetween+(o.slidesPerView!=="auto"&&n.slides.length-o.slidesPerView>=2?n.slidesSizesGrid[n.slidesSizesGrid.length-1]+n.params.spaceBetween:0):n.maxTranslate())&&n.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:n.slides.length-(o.slidesPerView==="auto"?n.slidesPerViewDynamic():Math.ceil(parseFloat(o.slidesPerView,10)))}),r.currentTranslate<n.maxTranslate()&&(b=!1,o.resistance&&(r.currentTranslate=n.maxTranslate()+1-(n.maxTranslate()-r.startTranslate-v)**C))),b&&(l.preventedByNestedSwiper=!0),!n.allowSlideNext&&n.swipeDirection==="next"&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!n.allowSlidePrev&&n.swipeDirection==="prev"&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),!n.allowSlidePrev&&!n.allowSlideNext&&(r.currentTranslate=r.startTranslate),o.threshold>0)if(Math.abs(v)>o.threshold||r.allowThresholdMove){if(!r.allowThresholdMove){r.allowThresholdMove=!0,i.startX=i.currentX,i.startY=i.currentY,r.currentTranslate=r.startTranslate,i.diff=n.isHorizontal()?i.currentX-i.startX:i.currentY-i.startY;return}}else{r.currentTranslate=r.startTranslate;return}!o.followFinger||o.cssMode||((o.freeMode&&o.freeMode.enabled&&n.freeMode||o.watchSlidesProgress)&&(n.updateActiveIndex(),n.updateSlidesClasses()),o.freeMode&&o.freeMode.enabled&&n.freeMode&&n.freeMode.onTouchMove(),n.updateProgress(r.currentTranslate),n.setTranslate(r.currentTranslate))}function Ic(e){const t=this,n=t.touchEventsData;let r=e;r.originalEvent&&(r=r.originalEvent);let o;if(r.type==="touchend"||r.type==="touchcancel"){if(o=[...r.changedTouches].find(C=>C.identifier===n.touchId),!o||o.identifier!==n.touchId)return}else{if(n.touchId!==null||r.pointerId!==n.pointerId)return;o=r}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(r.type)&&!(["pointercancel","contextmenu"].includes(r.type)&&(t.browser.isSafari||t.browser.isWebView)))return;n.pointerId=null,n.touchId=null;const{params:s,touches:a,rtlTranslate:l,slidesGrid:c,enabled:d}=t;if(!d||!s.simulateTouch&&r.pointerType==="mouse")return;if(n.allowTouchCallbacks&&t.emit("touchEnd",r),n.allowTouchCallbacks=!1,!n.isTouched){n.isMoved&&s.grabCursor&&t.setGrabCursor(!1),n.isMoved=!1,n.startMoving=!1;return}s.grabCursor&&n.isMoved&&n.isTouched&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!1);const u=ht(),p=u-n.touchStartTime;if(t.allowClick){const C=r.path||r.composedPath&&r.composedPath();t.updateClickedSlide(C&&C[0]||r.target,C),t.emit("tap click",r),p<300&&u-n.lastClickTime<300&&t.emit("doubleTap doubleClick",r)}if(n.lastClickTime=ht(),rn(()=>{t.destroyed||(t.allowClick=!0)}),!n.isTouched||!n.isMoved||!t.swipeDirection||a.diff===0&&!n.loopSwapReset||n.currentTranslate===n.startTranslate&&!n.loopSwapReset){n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;return}n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;let h;if(s.followFinger?h=l?t.translate:-t.translate:h=-n.currentTranslate,s.cssMode)return;if(s.freeMode&&s.freeMode.enabled){t.freeMode.onTouchEnd({currentPos:h});return}const v=h>=-t.maxTranslate()&&!t.params.loop;let m=0,w=t.slidesSizesGrid[0];for(let C=0;C<c.length;C+=C<s.slidesPerGroupSkip?1:s.slidesPerGroup){const T=C<s.slidesPerGroupSkip-1?1:s.slidesPerGroup;typeof c[C+T]<"u"?(v||h>=c[C]&&h<c[C+T])&&(m=C,w=c[C+T]-c[C]):(v||h>=c[C])&&(m=C,w=c[c.length-1]-c[c.length-2])}let g=null,x=null;s.rewind&&(t.isBeginning?x=s.virtual&&s.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(g=0));const S=(h-c[m])/w,b=m<s.slidesPerGroupSkip-1?1:s.slidesPerGroup;if(p>s.longSwipesMs){if(!s.longSwipes){t.slideTo(t.activeIndex);return}t.swipeDirection==="next"&&(S>=s.longSwipesRatio?t.slideTo(s.rewind&&t.isEnd?g:m+b):t.slideTo(m)),t.swipeDirection==="prev"&&(S>1-s.longSwipesRatio?t.slideTo(m+b):x!==null&&S<0&&Math.abs(S)>s.longSwipesRatio?t.slideTo(x):t.slideTo(m))}else{if(!s.shortSwipes){t.slideTo(t.activeIndex);return}t.navigation&&(r.target===t.navigation.nextEl||r.target===t.navigation.prevEl)?r.target===t.navigation.nextEl?t.slideTo(m+b):t.slideTo(m):(t.swipeDirection==="next"&&t.slideTo(g!==null?g:m+b),t.swipeDirection==="prev"&&t.slideTo(x!==null?x:m))}}function mr(){const e=this,{params:t,el:n}=e;if(n&&n.offsetWidth===0)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:r,allowSlidePrev:o,snapGrid:i}=e,s=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const a=s&&t.loop;(t.slidesPerView==="auto"||t.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides&&!a?e.slideTo(e.slides.length-1,0,!1,!0):e.params.loop&&!s?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=o,e.allowSlideNext=r,e.params.watchOverflow&&i!==e.snapGrid&&e.checkOverflow()}function Dc(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function Lc(){const e=this,{wrapperEl:t,rtlTranslate:n,enabled:r}=e;if(!r)return;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,e.translate===0&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();let o;const i=e.maxTranslate()-e.minTranslate();i===0?o=0:o=(e.translate-e.minTranslate())/i,o!==e.progress&&e.updateProgress(n?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function _c(e){const t=this;at(t,e.target),!(t.params.cssMode||t.params.slidesPerView!=="auto"&&!t.params.autoHeight)&&t.update()}function Nc(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const Eo=(e,t)=>{const n=Ee(),{params:r,el:o,wrapperEl:i,device:s}=e,a=!!r.nested,l=t==="on"?"addEventListener":"removeEventListener",c=t;!o||typeof o=="string"||(n[l]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:a}),o[l]("touchstart",e.onTouchStart,{passive:!1}),o[l]("pointerdown",e.onTouchStart,{passive:!1}),n[l]("touchmove",e.onTouchMove,{passive:!1,capture:a}),n[l]("pointermove",e.onTouchMove,{passive:!1,capture:a}),n[l]("touchend",e.onTouchEnd,{passive:!0}),n[l]("pointerup",e.onTouchEnd,{passive:!0}),n[l]("pointercancel",e.onTouchEnd,{passive:!0}),n[l]("touchcancel",e.onTouchEnd,{passive:!0}),n[l]("pointerout",e.onTouchEnd,{passive:!0}),n[l]("pointerleave",e.onTouchEnd,{passive:!0}),n[l]("contextmenu",e.onTouchEnd,{passive:!0}),(r.preventClicks||r.preventClicksPropagation)&&o[l]("click",e.onClick,!0),r.cssMode&&i[l]("scroll",e.onScroll),r.updateOnWindowResize?e[c](s.ios||s.android?"resize orientationchange observerUpdate":"resize observerUpdate",mr,!0):e[c]("observerUpdate",mr,!0),o[l]("load",e.onLoad,{capture:!0}))};function $c(){const e=this,{params:t}=e;e.onTouchStart=kc.bind(e),e.onTouchMove=Oc.bind(e),e.onTouchEnd=Ic.bind(e),e.onDocumentTouchStart=Nc.bind(e),t.cssMode&&(e.onScroll=Lc.bind(e)),e.onClick=Dc.bind(e),e.onLoad=_c.bind(e),Eo(e,"on")}function jc(){Eo(this,"off")}var zc={attachEvents:$c,detachEvents:jc};const vr=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;function Fc(){const e=this,{realIndex:t,initialized:n,params:r,el:o}=e,i=r.breakpoints;if(!i||i&&Object.keys(i).length===0)return;const s=Ee(),a=r.breakpointsBase==="window"||!r.breakpointsBase?r.breakpointsBase:"container",l=["window","container"].includes(r.breakpointsBase)||!r.breakpointsBase?e.el:s.querySelector(r.breakpointsBase),c=e.getBreakpoint(i,a,l);if(!c||e.currentBreakpoint===c)return;const u=(c in i?i[c]:void 0)||e.originalParams,p=vr(e,r),h=vr(e,u),v=e.params.grabCursor,m=u.grabCursor,w=r.enabled;p&&!h?(o.classList.remove(`${r.containerModifierClass}grid`,`${r.containerModifierClass}grid-column`),e.emitContainerClasses()):!p&&h&&(o.classList.add(`${r.containerModifierClass}grid`),(u.grid.fill&&u.grid.fill==="column"||!u.grid.fill&&r.grid.fill==="column")&&o.classList.add(`${r.containerModifierClass}grid-column`),e.emitContainerClasses()),v&&!m?e.unsetGrabCursor():!v&&m&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(T=>{if(typeof u[T]>"u")return;const P=r[T]&&r[T].enabled,M=u[T]&&u[T].enabled;P&&!M&&e[T].disable(),!P&&M&&e[T].enable()});const g=u.direction&&u.direction!==r.direction,x=r.loop&&(u.slidesPerView!==r.slidesPerView||g),S=r.loop;g&&n&&e.changeDirection(),G(e.params,u);const b=e.params.enabled,C=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),w&&!b?e.disable():!w&&b&&e.enable(),e.currentBreakpoint=c,e.emit("_beforeBreakpoint",u),n&&(x?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!S&&C?(e.loopCreate(t),e.updateSlides()):S&&!C&&e.loopDestroy()),e.emit("breakpoint",u)}function Vc(e,t,n){if(t===void 0&&(t="window"),!e||t==="container"&&!n)return;let r=!1;const o=H(),i=t==="window"?o.innerHeight:n.clientHeight,s=Object.keys(e).map(a=>{if(typeof a=="string"&&a.indexOf("@")===0){const l=parseFloat(a.substr(1));return{value:i*l,point:a}}return{value:a,point:a}});s.sort((a,l)=>parseInt(a.value,10)-parseInt(l.value,10));for(let a=0;a<s.length;a+=1){const{point:l,value:c}=s[a];t==="window"?o.matchMedia(`(min-width: ${c}px)`).matches&&(r=l):c<=n.clientWidth&&(r=l)}return r||"max"}var Bc={setBreakpoint:Fc,getBreakpoint:Vc};function Hc(e,t){const n=[];return e.forEach(r=>{typeof r=="object"?Object.keys(r).forEach(o=>{r[o]&&n.push(t+o)}):typeof r=="string"&&n.push(t+r)}),n}function Wc(){const e=this,{classNames:t,params:n,rtl:r,el:o,device:i}=e,s=Hc(["initialized",n.direction,{"free-mode":e.params.freeMode&&n.freeMode.enabled},{autoheight:n.autoHeight},{rtl:r},{grid:n.grid&&n.grid.rows>1},{"grid-column":n.grid&&n.grid.rows>1&&n.grid.fill==="column"},{android:i.android},{ios:i.ios},{"css-mode":n.cssMode},{centered:n.cssMode&&n.centeredSlides},{"watch-progress":n.watchSlidesProgress}],n.containerModifierClass);t.push(...s),o.classList.add(...t),e.emitContainerClasses()}function Gc(){const e=this,{el:t,classNames:n}=e;!t||typeof t=="string"||(t.classList.remove(...n),e.emitContainerClasses())}var Uc={addClasses:Wc,removeClasses:Gc};function Yc(){const e=this,{isLocked:t,params:n}=e,{slidesOffsetBefore:r}=n;if(r){const o=e.slides.length-1,i=e.slidesGrid[o]+e.slidesSizesGrid[o]+r*2;e.isLocked=e.size>i}else e.isLocked=e.snapGrid.length===1;n.allowSlideNext===!0&&(e.allowSlideNext=!e.isLocked),n.allowSlidePrev===!0&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}var Xc={checkOverflow:Yc},gr={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function qc(e,t){return function(r){r===void 0&&(r={});const o=Object.keys(r)[0],i=r[o];if(typeof i!="object"||i===null){G(t,r);return}if(e[o]===!0&&(e[o]={enabled:!0}),o==="navigation"&&e[o]&&e[o].enabled&&!e[o].prevEl&&!e[o].nextEl&&(e[o].auto=!0),["pagination","scrollbar"].indexOf(o)>=0&&e[o]&&e[o].enabled&&!e[o].el&&(e[o].auto=!0),!(o in e&&"enabled"in i)){G(t,r);return}typeof e[o]=="object"&&!("enabled"in e[o])&&(e[o].enabled=!0),e[o]||(e[o]={enabled:!1}),G(t,r)}}const Vt={eventsEmitter:Ul,update:rc,translate:cc,transition:pc,slide:Sc,loop:Tc,grabCursor:Mc,events:zc,breakpoints:Bc,checkOverflow:Xc,classes:Uc},Bt={};class q{constructor(){let t,n;for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];o.length===1&&o[0].constructor&&Object.prototype.toString.call(o[0]).slice(8,-1)==="Object"?n=o[0]:[t,n]=o,n||(n={}),n=G({},n),t&&!n.el&&(n.el=t);const s=Ee();if(n.el&&typeof n.el=="string"&&s.querySelectorAll(n.el).length>1){const d=[];return s.querySelectorAll(n.el).forEach(u=>{const p=G({},n,{el:u});d.push(new q(p))}),d}const a=this;a.__swiper__=!0,a.support=xo(),a.device=So({userAgent:n.userAgent}),a.browser=bo(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],n.modules&&Array.isArray(n.modules)&&a.modules.push(...n.modules);const l={};a.modules.forEach(d=>{d({params:n,swiper:a,extendParams:qc(n,l),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});const c=G({},gr,l);return a.params=G({},c,Bt,n),a.originalParams=G({},a.params),a.passedParams=G({},n),a.params&&a.params.on&&Object.keys(a.params.on).forEach(d=>{a.on(d,a.params.on[d])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:t,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return a.params.direction==="horizontal"},isVertical(){return a.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(t){return this.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}getSlideIndex(t){const{slidesEl:n,params:r}=this,o=ne(n,`.${r.slideClass}, swiper-slide`),i=ur(o[0]);return ur(t)-i}getSlideIndexByData(t){return this.getSlideIndex(this.slides.find(n=>n.getAttribute("data-swiper-slide-index")*1===t))}recalcSlides(){const t=this,{slidesEl:n,params:r}=t;t.slides=ne(n,`.${r.slideClass}, swiper-slide`)}enable(){const t=this;t.enabled||(t.enabled=!0,t.params.grabCursor&&t.setGrabCursor(),t.emit("enable"))}disable(){const t=this;t.enabled&&(t.enabled=!1,t.params.grabCursor&&t.unsetGrabCursor(),t.emit("disable"))}setProgress(t,n){const r=this;t=Math.min(Math.max(t,0),1);const o=r.minTranslate(),s=(r.maxTranslate()-o)*t+o;r.translateTo(s,typeof n>"u"?0:n),r.updateActiveIndex(),r.updateSlidesClasses()}emitContainerClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=t.el.className.split(" ").filter(r=>r.indexOf("swiper")===0||r.indexOf(t.params.containerModifierClass)===0);t.emit("_containerClasses",n.join(" "))}getSlideClasses(t){const n=this;return n.destroyed?"":t.className.split(" ").filter(r=>r.indexOf("swiper-slide")===0||r.indexOf(n.params.slideClass)===0).join(" ")}emitSlidesClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=[];t.slides.forEach(r=>{const o=t.getSlideClasses(r);n.push({slideEl:r,classNames:o}),t.emit("_slideClass",r,o)}),t.emit("_slideClasses",n)}slidesPerViewDynamic(t,n){t===void 0&&(t="current"),n===void 0&&(n=!1);const r=this,{params:o,slides:i,slidesGrid:s,slidesSizesGrid:a,size:l,activeIndex:c}=r;let d=1;if(typeof o.slidesPerView=="number")return o.slidesPerView;if(o.centeredSlides){let u=i[c]?Math.ceil(i[c].swiperSlideSize):0,p;for(let h=c+1;h<i.length;h+=1)i[h]&&!p&&(u+=Math.ceil(i[h].swiperSlideSize),d+=1,u>l&&(p=!0));for(let h=c-1;h>=0;h-=1)i[h]&&!p&&(u+=i[h].swiperSlideSize,d+=1,u>l&&(p=!0))}else if(t==="current")for(let u=c+1;u<i.length;u+=1)(n?s[u]+a[u]-s[c]<l:s[u]-s[c]<l)&&(d+=1);else for(let u=c-1;u>=0;u-=1)s[c]-s[u]<l&&(d+=1);return d}update(){const t=this;if(!t||t.destroyed)return;const{snapGrid:n,params:r}=t;r.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(s=>{s.complete&&at(t,s)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses();function o(){const s=t.rtlTranslate?t.translate*-1:t.translate,a=Math.min(Math.max(s,t.maxTranslate()),t.minTranslate());t.setTranslate(a),t.updateActiveIndex(),t.updateSlidesClasses()}let i;if(r.freeMode&&r.freeMode.enabled&&!r.cssMode)o(),r.autoHeight&&t.updateAutoHeight();else{if((r.slidesPerView==="auto"||r.slidesPerView>1)&&t.isEnd&&!r.centeredSlides){const s=t.virtual&&r.virtual.enabled?t.virtual.slides:t.slides;i=t.slideTo(s.length-1,0,!1,!0)}else i=t.slideTo(t.activeIndex,0,!1,!0);i||o()}r.watchOverflow&&n!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(t,n){n===void 0&&(n=!0);const r=this,o=r.params.direction;return t||(t=o==="horizontal"?"vertical":"horizontal"),t===o||t!=="horizontal"&&t!=="vertical"||(r.el.classList.remove(`${r.params.containerModifierClass}${o}`),r.el.classList.add(`${r.params.containerModifierClass}${t}`),r.emitContainerClasses(),r.params.direction=t,r.slides.forEach(i=>{t==="vertical"?i.style.width="":i.style.height=""}),r.emit("changeDirection"),n&&r.update()),r}changeLanguageDirection(t){const n=this;n.rtl&&t==="rtl"||!n.rtl&&t==="ltr"||(n.rtl=t==="rtl",n.rtlTranslate=n.params.direction==="horizontal"&&n.rtl,n.rtl?(n.el.classList.add(`${n.params.containerModifierClass}rtl`),n.el.dir="rtl"):(n.el.classList.remove(`${n.params.containerModifierClass}rtl`),n.el.dir="ltr"),n.update())}mount(t){const n=this;if(n.mounted)return!0;let r=t||n.params.el;if(typeof r=="string"&&(r=document.querySelector(r)),!r)return!1;r.swiper=n,r.parentNode&&r.parentNode.host&&r.parentNode.host.nodeName===n.params.swiperElementNodeName.toUpperCase()&&(n.isElement=!0);const o=()=>`.${(n.params.wrapperClass||"").trim().split(" ").join(".")}`;let s=r&&r.shadowRoot&&r.shadowRoot.querySelector?r.shadowRoot.querySelector(o()):ne(r,o())[0];return!s&&n.params.createElements&&(s=on("div",n.params.wrapperClass),r.append(s),ne(r,`.${n.params.slideClass}`).forEach(a=>{s.append(a)})),Object.assign(n,{el:r,wrapperEl:s,slidesEl:n.isElement&&!r.parentNode.host.slideSlots?r.parentNode.host:s,hostEl:n.isElement?r.parentNode.host:r,mounted:!0,rtl:r.dir.toLowerCase()==="rtl"||ce(r,"direction")==="rtl",rtlTranslate:n.params.direction==="horizontal"&&(r.dir.toLowerCase()==="rtl"||ce(r,"direction")==="rtl"),wrongRTL:ce(s,"display")==="-webkit-box"}),!0}init(t){const n=this;if(n.initialized||n.mount(t)===!1)return n;n.emit("beforeInit"),n.params.breakpoints&&n.setBreakpoint(),n.addClasses(),n.updateSize(),n.updateSlides(),n.params.watchOverflow&&n.checkOverflow(),n.params.grabCursor&&n.enabled&&n.setGrabCursor(),n.params.loop&&n.virtual&&n.params.virtual.enabled?n.slideTo(n.params.initialSlide+n.virtual.slidesBefore,0,n.params.runCallbacksOnInit,!1,!0):n.slideTo(n.params.initialSlide,0,n.params.runCallbacksOnInit,!1,!0),n.params.loop&&n.loopCreate(void 0,!0),n.attachEvents();const o=[...n.el.querySelectorAll('[loading="lazy"]')];return n.isElement&&o.push(...n.hostEl.querySelectorAll('[loading="lazy"]')),o.forEach(i=>{i.complete?at(n,i):i.addEventListener("load",s=>{at(n,s.target)})}),sn(n),n.initialized=!0,sn(n),n.emit("init"),n.emit("afterInit"),n}destroy(t,n){t===void 0&&(t=!0),n===void 0&&(n=!0);const r=this,{params:o,el:i,wrapperEl:s,slides:a}=r;return typeof r.params>"u"||r.destroyed||(r.emit("beforeDestroy"),r.initialized=!1,r.detachEvents(),o.loop&&r.loopDestroy(),n&&(r.removeClasses(),i&&typeof i!="string"&&i.removeAttribute("style"),s&&s.removeAttribute("style"),a&&a.length&&a.forEach(l=>{l.classList.remove(o.slideVisibleClass,o.slideFullyVisibleClass,o.slideActiveClass,o.slideNextClass,o.slidePrevClass),l.removeAttribute("style"),l.removeAttribute("data-swiper-slide-index")})),r.emit("destroy"),Object.keys(r.eventsListeners).forEach(l=>{r.off(l)}),t!==!1&&(r.el&&typeof r.el!="string"&&(r.el.swiper=null),Il(r)),r.destroyed=!0),null}static extendDefaults(t){G(Bt,t)}static get extendedDefaults(){return Bt}static get defaults(){return gr}static installModule(t){q.prototype.__modules__||(q.prototype.__modules__=[]);const n=q.prototype.__modules__;typeof t=="function"&&n.indexOf(t)<0&&n.push(t)}static use(t){return Array.isArray(t)?(t.forEach(n=>q.installModule(n)),q):(q.installModule(t),q)}}Object.keys(Vt).forEach(e=>{Object.keys(Vt[e]).forEach(t=>{q.prototype[t]=Vt[e][t]})});q.use([Wl,Gl]);var Ht="focusScope.autoFocusOnMount",Wt="focusScope.autoFocusOnUnmount",wr={bubbles:!1,cancelable:!0},Kc="FocusScope",To=f.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[a,l]=f.useState(null),c=ue(o),d=ue(i),u=f.useRef(null),p=B(t,m=>l(m)),h=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(r){let m=function(S){if(h.paused||!a)return;const b=S.target;a.contains(b)?u.current=b:ae(u.current,{select:!0})},w=function(S){if(h.paused||!a)return;const b=S.relatedTarget;b!==null&&(a.contains(b)||ae(u.current,{select:!0}))},g=function(S){if(document.activeElement===document.body)for(const C of S)C.removedNodes.length>0&&ae(a)};document.addEventListener("focusin",m),document.addEventListener("focusout",w);const x=new MutationObserver(g);return a&&x.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",w),x.disconnect()}}},[r,a,h.paused]),f.useEffect(()=>{if(a){xr.add(h);const m=document.activeElement;if(!a.contains(m)){const g=new CustomEvent(Ht,wr);a.addEventListener(Ht,c),a.dispatchEvent(g),g.defaultPrevented||(Zc(nd(Po(a)),{select:!0}),document.activeElement===m&&ae(a))}return()=>{a.removeEventListener(Ht,c),setTimeout(()=>{const g=new CustomEvent(Wt,wr);a.addEventListener(Wt,d),a.dispatchEvent(g),g.defaultPrevented||ae(m??document.body,{select:!0}),a.removeEventListener(Wt,d),xr.remove(h)},0)}}},[a,c,d,h]);const v=f.useCallback(m=>{if(!n&&!r||h.paused)return;const w=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,g=document.activeElement;if(w&&g){const x=m.currentTarget,[S,b]=Qc(x);S&&b?!m.shiftKey&&g===b?(m.preventDefault(),n&&ae(S,{select:!0})):m.shiftKey&&g===S&&(m.preventDefault(),n&&ae(b,{select:!0})):g===x&&m.preventDefault()}},[n,r,h.paused]);return y.jsx(F.div,{tabIndex:-1,...s,ref:p,onKeyDown:v})});To.displayName=Kc;function Zc(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(ae(r,{select:t}),document.activeElement!==n)return}function Qc(e){const t=Po(e),n=yr(t,e),r=yr(t.reverse(),e);return[n,r]}function Po(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function yr(e,t){for(const n of e)if(!Jc(n,{upTo:t}))return n}function Jc(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function ed(e){return e instanceof HTMLInputElement&&"select"in e}function ae(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&ed(e)&&t&&e.select()}}var xr=td();function td(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Sr(e,t),e.unshift(t)},remove(t){var n;e=Sr(e,t),(n=e[0])==null||n.resume()}}}function Sr(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function nd(e){return e.filter(t=>t.tagName!=="A")}var Gt=0;function rd(){f.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??br()),document.body.insertAdjacentElement("beforeend",e[1]??br()),Gt++,()=>{Gt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Gt--}},[])}function br(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var lt="right-scroll-bar-position",ct="width-before-scroll-bar",od="with-scroll-bars-hidden",id="--removed-body-scroll-bar-size";function Ut(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function sd(e,t){var n=f.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var ad=typeof window<"u"?f.useLayoutEffect:f.useEffect,Cr=new WeakMap;function ld(e,t){var n=sd(null,function(r){return e.forEach(function(o){return Ut(o,r)})});return ad(function(){var r=Cr.get(n);if(r){var o=new Set(r),i=new Set(e),s=n.current;o.forEach(function(a){i.has(a)||Ut(a,null)}),i.forEach(function(a){o.has(a)||Ut(a,s)})}Cr.set(n,e)},[e]),n}function cd(e){return e}function dd(e,t){t===void 0&&(t=cd);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var s=t(i,r);return n.push(s),function(){n=n.filter(function(a){return a!==s})}},assignSyncMedium:function(i){for(r=!0;n.length;){var s=n;n=[],s.forEach(i)}n={push:function(a){return i(a)},filter:function(){return n}}},assignMedium:function(i){r=!0;var s=[];if(n.length){var a=n;n=[],a.forEach(i),s=n}var l=function(){var d=s;s=[],d.forEach(i)},c=function(){return Promise.resolve().then(l)};c(),n={push:function(d){s.push(d),c()},filter:function(d){return s=s.filter(d),n}}}};return o}function ud(e){e===void 0&&(e={});var t=dd(null);return t.options=le({async:!0,ssr:!1},e),t}var Ao=function(e){var t=e.sideCar,n=Nr(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return f.createElement(r,le({},n))};Ao.isSideCarExport=!0;function fd(e,t){return e.useMedium(t),Ao}var Mo=ud(),Yt=function(){},At=f.forwardRef(function(e,t){var n=f.useRef(null),r=f.useState({onScrollCapture:Yt,onWheelCapture:Yt,onTouchMoveCapture:Yt}),o=r[0],i=r[1],s=e.forwardProps,a=e.children,l=e.className,c=e.removeScrollBar,d=e.enabled,u=e.shards,p=e.sideCar,h=e.noIsolation,v=e.inert,m=e.allowPinchZoom,w=e.as,g=w===void 0?"div":w,x=e.gapMode,S=Nr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),b=p,C=ld([n,t]),T=le(le({},S),o);return f.createElement(f.Fragment,null,d&&f.createElement(b,{sideCar:Mo,removeScrollBar:c,shards:u,noIsolation:h,inert:v,setCallbacks:i,allowPinchZoom:!!m,lockRef:n,gapMode:x}),s?f.cloneElement(f.Children.only(a),le(le({},T),{ref:C})):f.createElement(g,le({},T,{className:l,ref:C}),a))});At.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};At.classNames={fullWidth:ct,zeroRight:lt};var pd=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function hd(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=pd();return t&&e.setAttribute("nonce",t),e}function md(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function vd(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var gd=function(){var e=0,t=null;return{add:function(n){e==0&&(t=hd())&&(md(t,n),vd(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},wd=function(){var e=gd();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Ro=function(){var e=wd(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},yd={left:0,top:0,right:0,gap:0},Xt=function(e){return parseInt(e||"",10)||0},xd=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Xt(n),Xt(r),Xt(o)]},Sd=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return yd;var t=xd(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},bd=Ro(),Ie="data-scroll-locked",Cd=function(e,t,n,r){var o=e.left,i=e.top,s=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(od,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(Ie,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(lt,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(ct,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(lt," .").concat(lt,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(ct," .").concat(ct,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Ie,`] {
    `).concat(id,": ").concat(a,`px;
  }
`)},Er=function(){var e=parseInt(document.body.getAttribute(Ie)||"0",10);return isFinite(e)?e:0},Ed=function(){f.useEffect(function(){return document.body.setAttribute(Ie,(Er()+1).toString()),function(){var e=Er()-1;e<=0?document.body.removeAttribute(Ie):document.body.setAttribute(Ie,e.toString())}},[])},Td=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Ed();var i=f.useMemo(function(){return Sd(o)},[o]);return f.createElement(bd,{styles:Cd(i,!t,o,n?"":"!important")})},an=!1;if(typeof window<"u")try{var tt=Object.defineProperty({},"passive",{get:function(){return an=!0,!0}});window.addEventListener("test",tt,tt),window.removeEventListener("test",tt,tt)}catch{an=!1}var Ae=an?{passive:!1}:!1,Pd=function(e){return e.tagName==="TEXTAREA"},ko=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Pd(e)&&n[t]==="visible")},Ad=function(e){return ko(e,"overflowY")},Md=function(e){return ko(e,"overflowX")},Tr=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Oo(e,r);if(o){var i=Io(e,r),s=i[1],a=i[2];if(s>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Rd=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},kd=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Oo=function(e,t){return e==="v"?Ad(t):Md(t)},Io=function(e,t){return e==="v"?Rd(t):kd(t)},Od=function(e,t){return e==="h"&&t==="rtl"?-1:1},Id=function(e,t,n,r,o){var i=Od(e,window.getComputedStyle(t).direction),s=i*r,a=n.target,l=t.contains(a),c=!1,d=s>0,u=0,p=0;do{var h=Io(e,a),v=h[0],m=h[1],w=h[2],g=m-w-i*v;(v||g)&&Oo(e,a)&&(u+=g,p+=v),a instanceof ShadowRoot?a=a.host:a=a.parentNode}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(d&&(Math.abs(u)<1||!o)||!d&&(Math.abs(p)<1||!o))&&(c=!0),c},nt=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Pr=function(e){return[e.deltaX,e.deltaY]},Ar=function(e){return e&&"current"in e?e.current:e},Dd=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Ld=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},_d=0,Me=[];function Nd(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),o=f.useState(_d++)[0],i=f.useState(Ro)[0],s=f.useRef(e);f.useEffect(function(){s.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=Ts([e.lockRef.current],(e.shards||[]).map(Ar),!0).filter(Boolean);return m.forEach(function(w){return w.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(w){return w.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=f.useCallback(function(m,w){if("touches"in m&&m.touches.length===2||m.type==="wheel"&&m.ctrlKey)return!s.current.allowPinchZoom;var g=nt(m),x=n.current,S="deltaX"in m?m.deltaX:x[0]-g[0],b="deltaY"in m?m.deltaY:x[1]-g[1],C,T=m.target,P=Math.abs(S)>Math.abs(b)?"h":"v";if("touches"in m&&P==="h"&&T.type==="range")return!1;var M=Tr(P,T);if(!M)return!0;if(M?C=P:(C=P==="v"?"h":"v",M=Tr(P,T)),!M)return!1;if(!r.current&&"changedTouches"in m&&(S||b)&&(r.current=C),!C)return!0;var R=r.current||C;return Id(R,w,m,R==="h"?S:b,!0)},[]),l=f.useCallback(function(m){var w=m;if(!(!Me.length||Me[Me.length-1]!==i)){var g="deltaY"in w?Pr(w):nt(w),x=t.current.filter(function(C){return C.name===w.type&&(C.target===w.target||w.target===C.shadowParent)&&Dd(C.delta,g)})[0];if(x&&x.should){w.cancelable&&w.preventDefault();return}if(!x){var S=(s.current.shards||[]).map(Ar).filter(Boolean).filter(function(C){return C.contains(w.target)}),b=S.length>0?a(w,S[0]):!s.current.noIsolation;b&&w.cancelable&&w.preventDefault()}}},[]),c=f.useCallback(function(m,w,g,x){var S={name:m,delta:w,target:g,should:x,shadowParent:$d(g)};t.current.push(S),setTimeout(function(){t.current=t.current.filter(function(b){return b!==S})},1)},[]),d=f.useCallback(function(m){n.current=nt(m),r.current=void 0},[]),u=f.useCallback(function(m){c(m.type,Pr(m),m.target,a(m,e.lockRef.current))},[]),p=f.useCallback(function(m){c(m.type,nt(m),m.target,a(m,e.lockRef.current))},[]);f.useEffect(function(){return Me.push(i),e.setCallbacks({onScrollCapture:u,onWheelCapture:u,onTouchMoveCapture:p}),document.addEventListener("wheel",l,Ae),document.addEventListener("touchmove",l,Ae),document.addEventListener("touchstart",d,Ae),function(){Me=Me.filter(function(m){return m!==i}),document.removeEventListener("wheel",l,Ae),document.removeEventListener("touchmove",l,Ae),document.removeEventListener("touchstart",d,Ae)}},[]);var h=e.removeScrollBar,v=e.inert;return f.createElement(f.Fragment,null,v?f.createElement(i,{styles:Ld(o)}):null,h?f.createElement(Td,{gapMode:e.gapMode}):null)}function $d(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const jd=fd(Mo,Nd);var Do=f.forwardRef(function(e,t){return f.createElement(At,le({},e,{ref:t,sideCar:jd}))});Do.classNames=At.classNames;var zd=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Re=new WeakMap,rt=new WeakMap,ot={},qt=0,Lo=function(e){return e&&(e.host||Lo(e.parentNode))},Fd=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Lo(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Vd=function(e,t,n,r){var o=Fd(t,Array.isArray(e)?e:[e]);ot[n]||(ot[n]=new WeakMap);var i=ot[n],s=[],a=new Set,l=new Set(o),c=function(u){!u||a.has(u)||(a.add(u),c(u.parentNode))};o.forEach(c);var d=function(u){!u||l.has(u)||Array.prototype.forEach.call(u.children,function(p){if(a.has(p))d(p);else try{var h=p.getAttribute(r),v=h!==null&&h!=="false",m=(Re.get(p)||0)+1,w=(i.get(p)||0)+1;Re.set(p,m),i.set(p,w),s.push(p),m===1&&v&&rt.set(p,!0),w===1&&p.setAttribute(n,"true"),v||p.setAttribute(r,"true")}catch(g){console.error("aria-hidden: cannot operate on ",p,g)}})};return d(t),a.clear(),qt++,function(){s.forEach(function(u){var p=Re.get(u)-1,h=i.get(u)-1;Re.set(u,p),i.set(u,h),p||(rt.has(u)||u.removeAttribute(r),rt.delete(u)),h||u.removeAttribute(n)}),qt--,qt||(Re=new WeakMap,Re=new WeakMap,rt=new WeakMap,ot={})}},Bd=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=zd(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),Vd(r,o,n,"aria-hidden")):function(){return null}},Pn="Dialog",[_o,No]=be(Pn),[Hd,Q]=_o(Pn),$o=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:s=!0}=e,a=f.useRef(null),l=f.useRef(null),[c=!1,d]=ze({prop:r,defaultProp:o,onChange:i});return y.jsx(Hd,{scope:t,triggerRef:a,contentRef:l,contentId:Be(),titleId:Be(),descriptionId:Be(),open:c,onOpenChange:d,onOpenToggle:f.useCallback(()=>d(u=>!u),[d]),modal:s,children:n})};$o.displayName=Pn;var jo="DialogTrigger",zo=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(jo,n),i=B(t,o.triggerRef);return y.jsx(F.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Rn(o.open),...r,ref:i,onClick:V(e.onClick,o.onOpenToggle)})});zo.displayName=jo;var An="DialogPortal",[Wd,Fo]=_o(An,{forceMount:void 0}),Vo=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=Q(An,t);return y.jsx(Wd,{scope:t,forceMount:n,children:f.Children.map(r,s=>y.jsx(Ce,{present:n||i.open,children:y.jsx(lo,{asChild:!0,container:o,children:s})}))})};Vo.displayName=An;var vt="DialogOverlay",Bo=f.forwardRef((e,t)=>{const n=Fo(vt,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=Q(vt,e.__scopeDialog);return i.modal?y.jsx(Ce,{present:r||i.open,children:y.jsx(Gd,{...o,ref:t})}):null});Bo.displayName=vt;var Gd=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(vt,n);return y.jsx(Do,{as:He,allowPinchZoom:!0,shards:[o.contentRef],children:y.jsx(F.div,{"data-state":Rn(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),Se="DialogContent",Ho=f.forwardRef((e,t)=>{const n=Fo(Se,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=Q(Se,e.__scopeDialog);return y.jsx(Ce,{present:r||i.open,children:i.modal?y.jsx(Ud,{...o,ref:t}):y.jsx(Yd,{...o,ref:t})})});Ho.displayName=Se;var Ud=f.forwardRef((e,t)=>{const n=Q(Se,e.__scopeDialog),r=f.useRef(null),o=B(t,n.contentRef,r);return f.useEffect(()=>{const i=r.current;if(i)return Bd(i)},[]),y.jsx(Wo,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:V(e.onCloseAutoFocus,i=>{var s;i.preventDefault(),(s=n.triggerRef.current)==null||s.focus()}),onPointerDownOutside:V(e.onPointerDownOutside,i=>{const s=i.detail.originalEvent,a=s.button===0&&s.ctrlKey===!0;(s.button===2||a)&&i.preventDefault()}),onFocusOutside:V(e.onFocusOutside,i=>i.preventDefault())})}),Yd=f.forwardRef((e,t)=>{const n=Q(Se,e.__scopeDialog),r=f.useRef(!1),o=f.useRef(!1);return y.jsx(Wo,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var s,a;(s=e.onCloseAutoFocus)==null||s.call(e,i),i.defaultPrevented||(r.current||(a=n.triggerRef.current)==null||a.focus(),i.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:i=>{var l,c;(l=e.onInteractOutside)==null||l.call(e,i),i.defaultPrevented||(r.current=!0,i.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const s=i.target;((c=n.triggerRef.current)==null?void 0:c.contains(s))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&o.current&&i.preventDefault()}})}),Wo=f.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...s}=e,a=Q(Se,n),l=f.useRef(null),c=B(t,l);return rd(),y.jsxs(y.Fragment,{children:[y.jsx(To,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:y.jsx(mn,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":Rn(a.open),...s,ref:c,onDismiss:()=>a.onOpenChange(!1)})}),y.jsxs(y.Fragment,{children:[y.jsx(qd,{titleId:a.titleId}),y.jsx(Zd,{contentRef:l,descriptionId:a.descriptionId})]})]})}),Mn="DialogTitle",Go=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(Mn,n);return y.jsx(F.h2,{id:o.titleId,...r,ref:t})});Go.displayName=Mn;var Uo="DialogDescription",Yo=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(Uo,n);return y.jsx(F.p,{id:o.descriptionId,...r,ref:t})});Yo.displayName=Uo;var Xo="DialogClose",qo=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(Xo,n);return y.jsx(F.button,{type:"button",...r,ref:t,onClick:V(e.onClick,()=>o.onOpenChange(!1))})});qo.displayName=Xo;function Rn(e){return e?"open":"closed"}var Ko="DialogTitleWarning",[Xd,Zo]=Ns(Ko,{contentName:Se,titleName:Mn,docsSlug:"dialog"}),qd=({titleId:e})=>{const t=Zo(Ko),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return f.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Kd="DialogDescriptionWarning",Zd=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Zo(Kd).contentName}}.`;return f.useEffect(()=>{var i;const o=(i=e.current)==null?void 0:i.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Qd=$o,Jd=zo,eu=Vo,tu=Bo,nu=Ho,ru=Go,ou=Yo,Qo=qo;function iu(e,t=[]){let n=[];function r(i,s){const a=f.createContext(s),l=n.length;n=[...n,s];const c=u=>{var g;const{scope:p,children:h,...v}=u,m=((g=p==null?void 0:p[e])==null?void 0:g[l])||a,w=f.useMemo(()=>v,Object.values(v));return y.jsx(m.Provider,{value:w,children:h})};c.displayName=i+"Provider";function d(u,p){var m;const h=((m=p==null?void 0:p[e])==null?void 0:m[l])||a,v=f.useContext(h);if(v)return v;if(s!==void 0)return s;throw new Error(`\`${u}\` must be used within \`${i}\``)}return[c,d]}const o=()=>{const i=n.map(s=>f.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return f.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,su(o,...t)]}function su(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:c})=>{const u=l(i)[`__scope${c}`];return{...a,...u}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function Mr(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function au(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Mr(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Mr(e[o],null)}}}}function lu(e){const t=cu(e),n=f.forwardRef((r,o)=>{const{children:i,...s}=r,a=f.Children.toArray(i),l=a.find(uu);if(l){const c=l.props.children,d=a.map(u=>u===l?f.Children.count(c)>1?f.Children.only(null):f.isValidElement(c)?c.props.children:null:u);return y.jsx(t,{...s,ref:o,children:f.isValidElement(c)?f.cloneElement(c,void 0,d):null})}return y.jsx(t,{...s,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}function cu(e){const t=f.forwardRef((n,r)=>{const{children:o,...i}=n;if(f.isValidElement(o)){const s=pu(o),a=fu(i,o.props);return o.type!==f.Fragment&&(a.ref=r?au(r,s):s),f.cloneElement(o,a)}return f.Children.count(o)>1?f.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var du=Symbol("radix.slottable");function uu(e){return f.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===du}function fu(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{const l=i(...a);return o(...a),l}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function pu(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var hu=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Jo=hu.reduce((e,t)=>{const n=lu(`Primitive.${t}`),r=f.forwardRef((o,i)=>{const{asChild:s,...a}=o,l=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),y.jsx(l,{...a,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),kn="Progress",On=100,[mu,Sh]=iu(kn),[vu,gu]=mu(kn),ei=f.forwardRef((e,t)=>{const{__scopeProgress:n,value:r=null,max:o,getValueLabel:i=wu,...s}=e;(o||o===0)&&!Rr(o)&&console.error(yu(`${o}`,"Progress"));const a=Rr(o)?o:On;r!==null&&!kr(r,a)&&console.error(xu(`${r}`,"Progress"));const l=kr(r,a)?r:null,c=gt(l)?i(l,a):void 0;return y.jsx(vu,{scope:n,value:l,max:a,children:y.jsx(Jo.div,{"aria-valuemax":a,"aria-valuemin":0,"aria-valuenow":gt(l)?l:void 0,"aria-valuetext":c,role:"progressbar","data-state":ri(l,a),"data-value":l??void 0,"data-max":a,...s,ref:t})})});ei.displayName=kn;var ti="ProgressIndicator",ni=f.forwardRef((e,t)=>{const{__scopeProgress:n,...r}=e,o=gu(ti,n);return y.jsx(Jo.div,{"data-state":ri(o.value,o.max),"data-value":o.value??void 0,"data-max":o.max,...r,ref:t})});ni.displayName=ti;function wu(e,t){return`${Math.round(e/t*100)}%`}function ri(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function gt(e){return typeof e=="number"}function Rr(e){return gt(e)&&!isNaN(e)&&e>0}function kr(e,t){return gt(e)&&!isNaN(e)&&e<=t&&e>=0}function yu(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${On}\`.`}function xu(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${On} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var bh=ei,Ch=ni;function Or(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function oi(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Or(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Or(e[o],null)}}}}function Te(...e){return f.useCallback(oi(...e),e)}function Su(e){const t=bu(e),n=f.forwardRef((r,o)=>{const{children:i,...s}=r,a=f.Children.toArray(i),l=a.find(Eu);if(l){const c=l.props.children,d=a.map(u=>u===l?f.Children.count(c)>1?f.Children.only(null):f.isValidElement(c)?c.props.children:null:u);return y.jsx(t,{...s,ref:o,children:f.isValidElement(c)?f.cloneElement(c,void 0,d):null})}return y.jsx(t,{...s,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}function bu(e){const t=f.forwardRef((n,r)=>{const{children:o,...i}=n;if(f.isValidElement(o)){const s=Pu(o),a=Tu(i,o.props);return o.type!==f.Fragment&&(a.ref=r?oi(r,s):s),f.cloneElement(o,a)}return f.Children.count(o)>1?f.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Cu=Symbol("radix.slottable");function Eu(e){return f.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Cu}function Tu(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{const l=i(...a);return o(...a),l}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function Pu(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Au=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Ye=Au.reduce((e,t)=>{const n=Su(`Primitive.${t}`),r=f.forwardRef((o,i)=>{const{asChild:s,...a}=o,l=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),y.jsx(l,{...a,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),ln=globalThis!=null&&globalThis.document?f.useLayoutEffect:()=>{};function Mu(e,t){return f.useReducer((n,r)=>t[n][r]??n,e)}var Xe=e=>{const{present:t,children:n}=e,r=Ru(t),o=typeof n=="function"?n({present:r.isPresent}):f.Children.only(n),i=Te(r.ref,ku(o));return typeof n=="function"||r.isPresent?f.cloneElement(o,{ref:i}):null};Xe.displayName="Presence";function Ru(e){const[t,n]=f.useState(),r=f.useRef(null),o=f.useRef(e),i=f.useRef("none"),s=e?"mounted":"unmounted",[a,l]=Mu(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return f.useEffect(()=>{const c=it(r.current);i.current=a==="mounted"?c:"none"},[a]),ln(()=>{const c=r.current,d=o.current;if(d!==e){const p=i.current,h=it(c);e?l("MOUNT"):h==="none"||(c==null?void 0:c.display)==="none"?l("UNMOUNT"):l(d&&p!==h?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),ln(()=>{if(t){let c;const d=t.ownerDocument.defaultView??window,u=h=>{const m=it(r.current).includes(h.animationName);if(h.target===t&&m&&(l("ANIMATION_END"),!o.current)){const w=t.style.animationFillMode;t.style.animationFillMode="forwards",c=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=w)})}},p=h=>{h.target===t&&(i.current=it(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",u),t.addEventListener("animationend",u),()=>{d.clearTimeout(c),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",u),t.removeEventListener("animationend",u)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:f.useCallback(c=>{r.current=c?getComputedStyle(c):null,n(c)},[])}}function it(e){return(e==null?void 0:e.animationName)||"none"}function ku(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Ou(e,t=[]){let n=[];function r(i,s){const a=f.createContext(s),l=n.length;n=[...n,s];const c=u=>{var g;const{scope:p,children:h,...v}=u,m=((g=p==null?void 0:p[e])==null?void 0:g[l])||a,w=f.useMemo(()=>v,Object.values(v));return y.jsx(m.Provider,{value:w,children:h})};c.displayName=i+"Provider";function d(u,p){var m;const h=((m=p==null?void 0:p[e])==null?void 0:m[l])||a,v=f.useContext(h);if(v)return v;if(s!==void 0)return s;throw new Error(`\`${u}\` must be used within \`${i}\``)}return[c,d]}const o=()=>{const i=n.map(s=>f.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return f.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,Iu(o,...t)]}function Iu(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:c})=>{const u=l(i)[`__scope${c}`];return{...a,...u}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function we(e){const t=f.useRef(e);return f.useEffect(()=>{t.current=e}),f.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}var Du=f.createContext(void 0);function Lu(e){const t=f.useContext(Du);return e||t||"ltr"}function In(e,[t,n]){return Math.min(n,Math.max(t,e))}function ye(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function _u(e,t){return f.useReducer((n,r)=>t[n][r]??n,e)}var Dn="ScrollArea",[ii,Eh]=Ou(Dn),[Nu,X]=ii(Dn),si=f.forwardRef((e,t)=>{const{__scopeScrollArea:n,type:r="hover",dir:o,scrollHideDelay:i=600,...s}=e,[a,l]=f.useState(null),[c,d]=f.useState(null),[u,p]=f.useState(null),[h,v]=f.useState(null),[m,w]=f.useState(null),[g,x]=f.useState(0),[S,b]=f.useState(0),[C,T]=f.useState(!1),[P,M]=f.useState(!1),R=Te(t,A=>l(A)),E=Lu(o);return y.jsx(Nu,{scope:n,type:r,dir:E,scrollHideDelay:i,scrollArea:a,viewport:c,onViewportChange:d,content:u,onContentChange:p,scrollbarX:h,onScrollbarXChange:v,scrollbarXEnabled:C,onScrollbarXEnabledChange:T,scrollbarY:m,onScrollbarYChange:w,scrollbarYEnabled:P,onScrollbarYEnabledChange:M,onCornerWidthChange:x,onCornerHeightChange:b,children:y.jsx(Ye.div,{dir:E,...s,ref:R,style:{position:"relative","--radix-scroll-area-corner-width":g+"px","--radix-scroll-area-corner-height":S+"px",...e.style}})})});si.displayName=Dn;var ai="ScrollAreaViewport",li=f.forwardRef((e,t)=>{const{__scopeScrollArea:n,children:r,nonce:o,...i}=e,s=X(ai,n),a=f.useRef(null),l=Te(t,a,s.onViewportChange);return y.jsxs(y.Fragment,{children:[y.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),y.jsx(Ye.div,{"data-radix-scroll-area-viewport":"",...i,ref:l,style:{overflowX:s.scrollbarXEnabled?"scroll":"hidden",overflowY:s.scrollbarYEnabled?"scroll":"hidden",...e.style},children:y.jsx("div",{ref:s.onContentChange,style:{minWidth:"100%",display:"table"},children:r})})]})});li.displayName=ai;var te="ScrollAreaScrollbar",$u=f.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=X(te,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:s}=o,a=e.orientation==="horizontal";return f.useEffect(()=>(a?i(!0):s(!0),()=>{a?i(!1):s(!1)}),[a,i,s]),o.type==="hover"?y.jsx(ju,{...r,ref:t,forceMount:n}):o.type==="scroll"?y.jsx(zu,{...r,ref:t,forceMount:n}):o.type==="auto"?y.jsx(ci,{...r,ref:t,forceMount:n}):o.type==="always"?y.jsx(Ln,{...r,ref:t}):null});$u.displayName=te;var ju=f.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=X(te,e.__scopeScrollArea),[i,s]=f.useState(!1);return f.useEffect(()=>{const a=o.scrollArea;let l=0;if(a){const c=()=>{window.clearTimeout(l),s(!0)},d=()=>{l=window.setTimeout(()=>s(!1),o.scrollHideDelay)};return a.addEventListener("pointerenter",c),a.addEventListener("pointerleave",d),()=>{window.clearTimeout(l),a.removeEventListener("pointerenter",c),a.removeEventListener("pointerleave",d)}}},[o.scrollArea,o.scrollHideDelay]),y.jsx(Xe,{present:n||i,children:y.jsx(ci,{"data-state":i?"visible":"hidden",...r,ref:t})})}),zu=f.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=X(te,e.__scopeScrollArea),i=e.orientation==="horizontal",s=Rt(()=>l("SCROLL_END"),100),[a,l]=_u("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return f.useEffect(()=>{if(a==="idle"){const c=window.setTimeout(()=>l("HIDE"),o.scrollHideDelay);return()=>window.clearTimeout(c)}},[a,o.scrollHideDelay,l]),f.useEffect(()=>{const c=o.viewport,d=i?"scrollLeft":"scrollTop";if(c){let u=c[d];const p=()=>{const h=c[d];u!==h&&(l("SCROLL"),s()),u=h};return c.addEventListener("scroll",p),()=>c.removeEventListener("scroll",p)}},[o.viewport,i,l,s]),y.jsx(Xe,{present:n||a!=="hidden",children:y.jsx(Ln,{"data-state":a==="hidden"?"hidden":"visible",...r,ref:t,onPointerEnter:ye(e.onPointerEnter,()=>l("POINTER_ENTER")),onPointerLeave:ye(e.onPointerLeave,()=>l("POINTER_LEAVE"))})})}),ci=f.forwardRef((e,t)=>{const n=X(te,e.__scopeScrollArea),{forceMount:r,...o}=e,[i,s]=f.useState(!1),a=e.orientation==="horizontal",l=Rt(()=>{if(n.viewport){const c=n.viewport.offsetWidth<n.viewport.scrollWidth,d=n.viewport.offsetHeight<n.viewport.scrollHeight;s(a?c:d)}},10);return Ne(n.viewport,l),Ne(n.content,l),y.jsx(Xe,{present:r||i,children:y.jsx(Ln,{"data-state":i?"visible":"hidden",...o,ref:t})})}),Ln=f.forwardRef((e,t)=>{const{orientation:n="vertical",...r}=e,o=X(te,e.__scopeScrollArea),i=f.useRef(null),s=f.useRef(0),[a,l]=f.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=pi(a.viewport,a.content),d={...r,sizes:a,onSizesChange:l,hasThumb:c>0&&c<1,onThumbChange:p=>i.current=p,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:p=>s.current=p};function u(p,h){return Uu(p,s.current,a,h)}return n==="horizontal"?y.jsx(Fu,{...d,ref:t,onThumbPositionChange:()=>{if(o.viewport&&i.current){const p=o.viewport.scrollLeft,h=Ir(p,a,o.dir);i.current.style.transform=`translate3d(${h}px, 0, 0)`}},onWheelScroll:p=>{o.viewport&&(o.viewport.scrollLeft=p)},onDragScroll:p=>{o.viewport&&(o.viewport.scrollLeft=u(p,o.dir))}}):n==="vertical"?y.jsx(Vu,{...d,ref:t,onThumbPositionChange:()=>{if(o.viewport&&i.current){const p=o.viewport.scrollTop,h=Ir(p,a);i.current.style.transform=`translate3d(0, ${h}px, 0)`}},onWheelScroll:p=>{o.viewport&&(o.viewport.scrollTop=p)},onDragScroll:p=>{o.viewport&&(o.viewport.scrollTop=u(p))}}):null}),Fu=f.forwardRef((e,t)=>{const{sizes:n,onSizesChange:r,...o}=e,i=X(te,e.__scopeScrollArea),[s,a]=f.useState(),l=f.useRef(null),c=Te(t,l,i.onScrollbarXChange);return f.useEffect(()=>{l.current&&a(getComputedStyle(l.current))},[l]),y.jsx(ui,{"data-orientation":"horizontal",...o,ref:c,sizes:n,style:{bottom:0,left:i.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:i.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":Mt(n)+"px",...e.style},onThumbPointerDown:d=>e.onThumbPointerDown(d.x),onDragScroll:d=>e.onDragScroll(d.x),onWheelScroll:(d,u)=>{if(i.viewport){const p=i.viewport.scrollLeft+d.deltaX;e.onWheelScroll(p),mi(p,u)&&d.preventDefault()}},onResize:()=>{l.current&&i.viewport&&s&&r({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:l.current.clientWidth,paddingStart:yt(s.paddingLeft),paddingEnd:yt(s.paddingRight)}})}})}),Vu=f.forwardRef((e,t)=>{const{sizes:n,onSizesChange:r,...o}=e,i=X(te,e.__scopeScrollArea),[s,a]=f.useState(),l=f.useRef(null),c=Te(t,l,i.onScrollbarYChange);return f.useEffect(()=>{l.current&&a(getComputedStyle(l.current))},[l]),y.jsx(ui,{"data-orientation":"vertical",...o,ref:c,sizes:n,style:{top:0,right:i.dir==="ltr"?0:void 0,left:i.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":Mt(n)+"px",...e.style},onThumbPointerDown:d=>e.onThumbPointerDown(d.y),onDragScroll:d=>e.onDragScroll(d.y),onWheelScroll:(d,u)=>{if(i.viewport){const p=i.viewport.scrollTop+d.deltaY;e.onWheelScroll(p),mi(p,u)&&d.preventDefault()}},onResize:()=>{l.current&&i.viewport&&s&&r({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:l.current.clientHeight,paddingStart:yt(s.paddingTop),paddingEnd:yt(s.paddingBottom)}})}})}),[Bu,di]=ii(te),ui=f.forwardRef((e,t)=>{const{__scopeScrollArea:n,sizes:r,hasThumb:o,onThumbChange:i,onThumbPointerUp:s,onThumbPointerDown:a,onThumbPositionChange:l,onDragScroll:c,onWheelScroll:d,onResize:u,...p}=e,h=X(te,n),[v,m]=f.useState(null),w=Te(t,R=>m(R)),g=f.useRef(null),x=f.useRef(""),S=h.viewport,b=r.content-r.viewport,C=we(d),T=we(l),P=Rt(u,10);function M(R){if(g.current){const E=R.clientX-g.current.left,A=R.clientY-g.current.top;c({x:E,y:A})}}return f.useEffect(()=>{const R=E=>{const A=E.target;(v==null?void 0:v.contains(A))&&C(E,b)};return document.addEventListener("wheel",R,{passive:!1}),()=>document.removeEventListener("wheel",R,{passive:!1})},[S,v,b,C]),f.useEffect(T,[r,T]),Ne(v,P),Ne(h.content,P),y.jsx(Bu,{scope:n,scrollbar:v,hasThumb:o,onThumbChange:we(i),onThumbPointerUp:we(s),onThumbPositionChange:T,onThumbPointerDown:we(a),children:y.jsx(Ye.div,{...p,ref:w,style:{position:"absolute",...p.style},onPointerDown:ye(e.onPointerDown,R=>{R.button===0&&(R.target.setPointerCapture(R.pointerId),g.current=v.getBoundingClientRect(),x.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",h.viewport&&(h.viewport.style.scrollBehavior="auto"),M(R))}),onPointerMove:ye(e.onPointerMove,M),onPointerUp:ye(e.onPointerUp,R=>{const E=R.target;E.hasPointerCapture(R.pointerId)&&E.releasePointerCapture(R.pointerId),document.body.style.webkitUserSelect=x.current,h.viewport&&(h.viewport.style.scrollBehavior=""),g.current=null})})})}),wt="ScrollAreaThumb",Hu=f.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=di(wt,e.__scopeScrollArea);return y.jsx(Xe,{present:n||o.hasThumb,children:y.jsx(Wu,{ref:t,...r})})}),Wu=f.forwardRef((e,t)=>{const{__scopeScrollArea:n,style:r,...o}=e,i=X(wt,n),s=di(wt,n),{onThumbPositionChange:a}=s,l=Te(t,u=>s.onThumbChange(u)),c=f.useRef(void 0),d=Rt(()=>{c.current&&(c.current(),c.current=void 0)},100);return f.useEffect(()=>{const u=i.viewport;if(u){const p=()=>{if(d(),!c.current){const h=Yu(u,a);c.current=h,a()}};return a(),u.addEventListener("scroll",p),()=>u.removeEventListener("scroll",p)}},[i.viewport,d,a]),y.jsx(Ye.div,{"data-state":s.hasThumb?"visible":"hidden",...o,ref:l,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...r},onPointerDownCapture:ye(e.onPointerDownCapture,u=>{const h=u.target.getBoundingClientRect(),v=u.clientX-h.left,m=u.clientY-h.top;s.onThumbPointerDown({x:v,y:m})}),onPointerUp:ye(e.onPointerUp,s.onThumbPointerUp)})});Hu.displayName=wt;var _n="ScrollAreaCorner",fi=f.forwardRef((e,t)=>{const n=X(_n,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return n.type!=="scroll"&&r?y.jsx(Gu,{...e,ref:t}):null});fi.displayName=_n;var Gu=f.forwardRef((e,t)=>{const{__scopeScrollArea:n,...r}=e,o=X(_n,n),[i,s]=f.useState(0),[a,l]=f.useState(0),c=!!(i&&a);return Ne(o.scrollbarX,()=>{var u;const d=((u=o.scrollbarX)==null?void 0:u.offsetHeight)||0;o.onCornerHeightChange(d),l(d)}),Ne(o.scrollbarY,()=>{var u;const d=((u=o.scrollbarY)==null?void 0:u.offsetWidth)||0;o.onCornerWidthChange(d),s(d)}),c?y.jsx(Ye.div,{...r,ref:t,style:{width:i,height:a,position:"absolute",right:o.dir==="ltr"?0:void 0,left:o.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function yt(e){return e?parseInt(e,10):0}function pi(e,t){const n=e/t;return isNaN(n)?0:n}function Mt(e){const t=pi(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,r=(e.scrollbar.size-n)*t;return Math.max(r,18)}function Uu(e,t,n,r="ltr"){const o=Mt(n),i=o/2,s=t||i,a=o-s,l=n.scrollbar.paddingStart+s,c=n.scrollbar.size-n.scrollbar.paddingEnd-a,d=n.content-n.viewport,u=r==="ltr"?[0,d]:[d*-1,0];return hi([l,c],u)(e)}function Ir(e,t,n="ltr"){const r=Mt(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,s=t.content-t.viewport,a=i-r,l=n==="ltr"?[0,s]:[s*-1,0],c=In(e,l);return hi([0,s],[0,a])(c)}function hi(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function mi(e,t){return e>0&&e<t}var Yu=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},r=0;return function o(){const i={left:e.scrollLeft,top:e.scrollTop},s=n.left!==i.left,a=n.top!==i.top;(s||a)&&t(),n=i,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)};function Rt(e,t){const n=we(e),r=f.useRef(0);return f.useEffect(()=>()=>window.clearTimeout(r.current),[]),f.useCallback(()=>{window.clearTimeout(r.current),r.current=window.setTimeout(n,t)},[n,t])}function Ne(e,t){const n=we(t);ln(()=>{let r=0;if(e){const o=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(n)});return o.observe(e),()=>{window.cancelAnimationFrame(r),o.unobserve(e)}}},[e,n])}var Th=si,Ph=li,Ah=fi;function ke(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Dr(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function vi(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Dr(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Dr(e[o],null)}}}}function ge(...e){return f.useCallback(vi(...e),e)}function gi(e,t=[]){let n=[];function r(i,s){const a=f.createContext(s),l=n.length;n=[...n,s];const c=u=>{var g;const{scope:p,children:h,...v}=u,m=((g=p==null?void 0:p[e])==null?void 0:g[l])||a,w=f.useMemo(()=>v,Object.values(v));return y.jsx(m.Provider,{value:w,children:h})};c.displayName=i+"Provider";function d(u,p){var m;const h=((m=p==null?void 0:p[e])==null?void 0:m[l])||a,v=f.useContext(h);if(v)return v;if(s!==void 0)return s;throw new Error(`\`${u}\` must be used within \`${i}\``)}return[c,d]}const o=()=>{const i=n.map(s=>f.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return f.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,Xu(o,...t)]}function Xu(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:c})=>{const u=l(i)[`__scope${c}`];return{...a,...u}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var wi=globalThis!=null&&globalThis.document?f.useLayoutEffect:()=>{},qu=_r[" useInsertionEffect ".trim().toString()]||wi;function Ku({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,s]=Zu({defaultProp:t,onChange:n}),a=e!==void 0,l=a?e:o;{const d=f.useRef(e!==void 0);f.useEffect(()=>{const u=d.current;u!==a&&console.warn(`${r} is changing from ${u?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),d.current=a},[a,r])}const c=f.useCallback(d=>{var u;if(a){const p=Qu(d)?d(e):d;p!==e&&((u=s.current)==null||u.call(s,p))}else i(d)},[a,e,i,s]);return[l,c]}function Zu({defaultProp:e,onChange:t}){const[n,r]=f.useState(e),o=f.useRef(n),i=f.useRef(t);return qu(()=>{i.current=t},[t]),f.useEffect(()=>{var s;o.current!==n&&((s=i.current)==null||s.call(i,n),o.current=n)},[n,o]),[n,r,i]}function Qu(e){return typeof e=="function"}var Ju=f.createContext(void 0);function ef(e){const t=f.useContext(Ju);return e||t||"ltr"}function tf(e){const t=f.useRef({value:e,previous:e});return f.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function nf(e){const[t,n]=f.useState(void 0);return wi(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,a;if("borderBoxSize"in i){const l=i.borderBoxSize,c=Array.isArray(l)?l[0]:l;s=c.inlineSize,a=c.blockSize}else s=e.offsetWidth,a=e.offsetHeight;n({width:s,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}function cn(e){const t=rf(e),n=f.forwardRef((r,o)=>{const{children:i,...s}=r,a=f.Children.toArray(i),l=a.find(sf);if(l){const c=l.props.children,d=a.map(u=>u===l?f.Children.count(c)>1?f.Children.only(null):f.isValidElement(c)?c.props.children:null:u);return y.jsx(t,{...s,ref:o,children:f.isValidElement(c)?f.cloneElement(c,void 0,d):null})}return y.jsx(t,{...s,ref:o,children:i})});return n.displayName=`${e}.Slot`,n}function rf(e){const t=f.forwardRef((n,r)=>{const{children:o,...i}=n;if(f.isValidElement(o)){const s=lf(o),a=af(i,o.props);return o.type!==f.Fragment&&(a.ref=r?vi(r,s):s),f.cloneElement(o,a)}return f.Children.count(o)>1?f.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var of=Symbol("radix.slottable");function sf(e){return f.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===of}function af(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{const l=i(...a);return o(...a),l}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function lf(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var cf=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],qe=cf.reduce((e,t)=>{const n=cn(`Primitive.${t}`),r=f.forwardRef((o,i)=>{const{asChild:s,...a}=o,l=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),y.jsx(l,{...a,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function df(e){const t=e+"CollectionProvider",[n,r]=gi(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=m=>{const{scope:w,children:g}=m,x=j.useRef(null),S=j.useRef(new Map).current;return y.jsx(o,{scope:w,itemMap:S,collectionRef:x,children:g})};s.displayName=t;const a=e+"CollectionSlot",l=cn(a),c=j.forwardRef((m,w)=>{const{scope:g,children:x}=m,S=i(a,g),b=ge(w,S.collectionRef);return y.jsx(l,{ref:b,children:x})});c.displayName=a;const d=e+"CollectionItemSlot",u="data-radix-collection-item",p=cn(d),h=j.forwardRef((m,w)=>{const{scope:g,children:x,...S}=m,b=j.useRef(null),C=ge(w,b),T=i(d,g);return j.useEffect(()=>(T.itemMap.set(b,{ref:b,...S}),()=>void T.itemMap.delete(b))),y.jsx(p,{[u]:"",ref:C,children:x})});h.displayName=d;function v(m){const w=i(e+"CollectionConsumer",m);return j.useCallback(()=>{const x=w.collectionRef.current;if(!x)return[];const S=Array.from(x.querySelectorAll(`[${u}]`));return Array.from(w.itemMap.values()).sort((T,P)=>S.indexOf(T.ref.current)-S.indexOf(P.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:s,Slot:c,ItemSlot:h},v,r]}var yi=["PageUp","PageDown"],xi=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],Si={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},Fe="Slider",[dn,uf,ff]=df(Fe),[bi,Mh]=gi(Fe,[ff]),[pf,kt]=bi(Fe),Ci=f.forwardRef((e,t)=>{const{name:n,min:r=0,max:o=100,step:i=1,orientation:s="horizontal",disabled:a=!1,minStepsBetweenThumbs:l=0,defaultValue:c=[r],value:d,onValueChange:u=()=>{},onValueCommit:p=()=>{},inverted:h=!1,form:v,...m}=e,w=f.useRef(new Set),g=f.useRef(0),S=s==="horizontal"?hf:mf,[b=[],C]=Ku({prop:d,defaultProp:c,onChange:A=>{var L;(L=[...w.current][g.current])==null||L.focus(),u(A)}}),T=f.useRef(b);function P(A){const O=xf(b,A);E(A,O)}function M(A){E(A,g.current)}function R(){const A=T.current[g.current];b[g.current]!==A&&p(b)}function E(A,O,{commit:L}={commit:!1}){const N=Ef(i),z=Tf(Math.round((A-r)/i)*i+r,N),k=In(z,[r,o]);C((_=[])=>{const I=wf(_,k,O);if(Cf(I,l*i)){g.current=I.indexOf(k);const $=String(I)!==String(_);return $&&L&&p(I),$?I:_}else return _})}return y.jsx(pf,{scope:e.__scopeSlider,name:n,disabled:a,min:r,max:o,valueIndexToChangeRef:g,thumbs:w.current,values:b,orientation:s,form:v,children:y.jsx(dn.Provider,{scope:e.__scopeSlider,children:y.jsx(dn.Slot,{scope:e.__scopeSlider,children:y.jsx(S,{"aria-disabled":a,"data-disabled":a?"":void 0,...m,ref:t,onPointerDown:ke(m.onPointerDown,()=>{a||(T.current=b)}),min:r,max:o,inverted:h,onSlideStart:a?void 0:P,onSlideMove:a?void 0:M,onSlideEnd:a?void 0:R,onHomeKeyDown:()=>!a&&E(r,0,{commit:!0}),onEndKeyDown:()=>!a&&E(o,b.length-1,{commit:!0}),onStepKeyDown:({event:A,direction:O})=>{if(!a){const z=yi.includes(A.key)||A.shiftKey&&xi.includes(A.key)?10:1,k=g.current,_=b[k],I=i*z*O;E(_+I,k,{commit:!0})}}})})})})});Ci.displayName=Fe;var[Ei,Ti]=bi(Fe,{startEdge:"left",endEdge:"right",size:"width",direction:1}),hf=f.forwardRef((e,t)=>{const{min:n,max:r,dir:o,inverted:i,onSlideStart:s,onSlideMove:a,onSlideEnd:l,onStepKeyDown:c,...d}=e,[u,p]=f.useState(null),h=ge(t,S=>p(S)),v=f.useRef(void 0),m=ef(o),w=m==="ltr",g=w&&!i||!w&&i;function x(S){const b=v.current||u.getBoundingClientRect(),C=[0,b.width],P=Nn(C,g?[n,r]:[r,n]);return v.current=b,P(S-b.left)}return y.jsx(Ei,{scope:e.__scopeSlider,startEdge:g?"left":"right",endEdge:g?"right":"left",direction:g?1:-1,size:"width",children:y.jsx(Pi,{dir:m,"data-orientation":"horizontal",...d,ref:h,style:{...d.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:S=>{const b=x(S.clientX);s==null||s(b)},onSlideMove:S=>{const b=x(S.clientX);a==null||a(b)},onSlideEnd:()=>{v.current=void 0,l==null||l()},onStepKeyDown:S=>{const C=Si[g?"from-left":"from-right"].includes(S.key);c==null||c({event:S,direction:C?-1:1})}})})}),mf=f.forwardRef((e,t)=>{const{min:n,max:r,inverted:o,onSlideStart:i,onSlideMove:s,onSlideEnd:a,onStepKeyDown:l,...c}=e,d=f.useRef(null),u=ge(t,d),p=f.useRef(void 0),h=!o;function v(m){const w=p.current||d.current.getBoundingClientRect(),g=[0,w.height],S=Nn(g,h?[r,n]:[n,r]);return p.current=w,S(m-w.top)}return y.jsx(Ei,{scope:e.__scopeSlider,startEdge:h?"bottom":"top",endEdge:h?"top":"bottom",size:"height",direction:h?1:-1,children:y.jsx(Pi,{"data-orientation":"vertical",...c,ref:u,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:m=>{const w=v(m.clientY);i==null||i(w)},onSlideMove:m=>{const w=v(m.clientY);s==null||s(w)},onSlideEnd:()=>{p.current=void 0,a==null||a()},onStepKeyDown:m=>{const g=Si[h?"from-bottom":"from-top"].includes(m.key);l==null||l({event:m,direction:g?-1:1})}})})}),Pi=f.forwardRef((e,t)=>{const{__scopeSlider:n,onSlideStart:r,onSlideMove:o,onSlideEnd:i,onHomeKeyDown:s,onEndKeyDown:a,onStepKeyDown:l,...c}=e,d=kt(Fe,n);return y.jsx(qe.span,{...c,ref:t,onKeyDown:ke(e.onKeyDown,u=>{u.key==="Home"?(s(u),u.preventDefault()):u.key==="End"?(a(u),u.preventDefault()):yi.concat(xi).includes(u.key)&&(l(u),u.preventDefault())}),onPointerDown:ke(e.onPointerDown,u=>{const p=u.target;p.setPointerCapture(u.pointerId),u.preventDefault(),d.thumbs.has(p)?p.focus():r(u)}),onPointerMove:ke(e.onPointerMove,u=>{u.target.hasPointerCapture(u.pointerId)&&o(u)}),onPointerUp:ke(e.onPointerUp,u=>{const p=u.target;p.hasPointerCapture(u.pointerId)&&(p.releasePointerCapture(u.pointerId),i(u))})})}),Ai="SliderTrack",Mi=f.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=kt(Ai,n);return y.jsx(qe.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...r,ref:t})});Mi.displayName=Ai;var un="SliderRange",Ri=f.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=kt(un,n),i=Ti(un,n),s=f.useRef(null),a=ge(t,s),l=o.values.length,c=o.values.map(p=>Ii(p,o.min,o.max)),d=l>1?Math.min(...c):0,u=100-Math.max(...c);return y.jsx(qe.span,{"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,...r,ref:a,style:{...e.style,[i.startEdge]:d+"%",[i.endEdge]:u+"%"}})});Ri.displayName=un;var fn="SliderThumb",ki=f.forwardRef((e,t)=>{const n=uf(e.__scopeSlider),[r,o]=f.useState(null),i=ge(t,a=>o(a)),s=f.useMemo(()=>r?n().findIndex(a=>a.ref.current===r):-1,[n,r]);return y.jsx(vf,{...e,ref:i,index:s})}),vf=f.forwardRef((e,t)=>{const{__scopeSlider:n,index:r,name:o,...i}=e,s=kt(fn,n),a=Ti(fn,n),[l,c]=f.useState(null),d=ge(t,x=>c(x)),u=l?s.form||!!l.closest("form"):!0,p=nf(l),h=s.values[r],v=h===void 0?0:Ii(h,s.min,s.max),m=yf(r,s.values.length),w=p==null?void 0:p[a.size],g=w?Sf(w,v,a.direction):0;return f.useEffect(()=>{if(l)return s.thumbs.add(l),()=>{s.thumbs.delete(l)}},[l,s.thumbs]),y.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[a.startEdge]:`calc(${v}% + ${g}px)`},children:[y.jsx(dn.ItemSlot,{scope:e.__scopeSlider,children:y.jsx(qe.span,{role:"slider","aria-label":e["aria-label"]||m,"aria-valuemin":s.min,"aria-valuenow":h,"aria-valuemax":s.max,"aria-orientation":s.orientation,"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0,tabIndex:s.disabled?void 0:0,...i,ref:d,style:h===void 0?{display:"none"}:e.style,onFocus:ke(e.onFocus,()=>{s.valueIndexToChangeRef.current=r})})}),u&&y.jsx(Oi,{name:o??(s.name?s.name+(s.values.length>1?"[]":""):void 0),form:s.form,value:h},r)]})});ki.displayName=fn;var gf="RadioBubbleInput",Oi=f.forwardRef(({__scopeSlider:e,value:t,...n},r)=>{const o=f.useRef(null),i=ge(o,r),s=tf(t);return f.useEffect(()=>{const a=o.current;if(!a)return;const l=window.HTMLInputElement.prototype,d=Object.getOwnPropertyDescriptor(l,"value").set;if(s!==t&&d){const u=new Event("input",{bubbles:!0});d.call(a,t),a.dispatchEvent(u)}},[s,t]),y.jsx(qe.input,{style:{display:"none"},...n,ref:i,defaultValue:t})});Oi.displayName=gf;function wf(e=[],t,n){const r=[...e];return r[n]=t,r.sort((o,i)=>o-i)}function Ii(e,t,n){const i=100/(n-t)*(e-t);return In(i,[0,100])}function yf(e,t){return t>2?`Value ${e+1} of ${t}`:t===2?["Minimum","Maximum"][e]:void 0}function xf(e,t){if(e.length===1)return 0;const n=e.map(o=>Math.abs(o-t)),r=Math.min(...n);return n.indexOf(r)}function Sf(e,t,n){const r=e/2,i=Nn([0,50],[0,r]);return(r-i(t)*n)*n}function bf(e){return e.slice(0,-1).map((t,n)=>e[n+1]-t)}function Cf(e,t){if(t>0){const n=bf(e);return Math.min(...n)>=t}return!0}function Nn(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function Ef(e){return(String(e).split(".")[1]||"").length}function Tf(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}var Rh=Ci,kh=Mi,Oh=Ri,Ih=ki;function Di(e){const t=f.useRef({value:e,previous:e});return f.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var $n="Switch",[Pf,Dh]=be($n),[Af,Mf]=Pf($n),Li=f.forwardRef((e,t)=>{const{__scopeSwitch:n,name:r,checked:o,defaultChecked:i,required:s,disabled:a,value:l="on",onCheckedChange:c,form:d,...u}=e,[p,h]=f.useState(null),v=B(t,S=>h(S)),m=f.useRef(!1),w=p?d||!!p.closest("form"):!0,[g=!1,x]=ze({prop:o,defaultProp:i,onChange:c});return y.jsxs(Af,{scope:n,checked:g,disabled:a,children:[y.jsx(F.button,{type:"button",role:"switch","aria-checked":g,"aria-required":s,"data-state":$i(g),"data-disabled":a?"":void 0,disabled:a,value:l,...u,ref:v,onClick:V(e.onClick,S=>{x(b=>!b),w&&(m.current=S.isPropagationStopped(),m.current||S.stopPropagation())})}),w&&y.jsx(Rf,{control:p,bubbles:!m.current,name:r,value:l,checked:g,required:s,disabled:a,form:d,style:{transform:"translateX(-100%)"}})]})});Li.displayName=$n;var _i="SwitchThumb",Ni=f.forwardRef((e,t)=>{const{__scopeSwitch:n,...r}=e,o=Mf(_i,n);return y.jsx(F.span,{"data-state":$i(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});Ni.displayName=_i;var Rf=e=>{const{control:t,checked:n,bubbles:r=!0,...o}=e,i=f.useRef(null),s=Di(n),a=bn(t);return f.useEffect(()=>{const l=i.current,c=window.HTMLInputElement.prototype,u=Object.getOwnPropertyDescriptor(c,"checked").set;if(s!==n&&u){const p=new Event("click",{bubbles:r});u.call(l,n),l.dispatchEvent(p)}},[s,n,r]),y.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...o,tabIndex:-1,ref:i,style:{...e.style,...a,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function $i(e){return e?"checked":"unchecked"}var Lh=Li,_h=Ni,ji="AlertDialog",[kf,Nh]=be(ji,[No]),ie=No(),zi=e=>{const{__scopeAlertDialog:t,...n}=e,r=ie(t);return y.jsx(Qd,{...r,...n,modal:!0})};zi.displayName=ji;var Of="AlertDialogTrigger",If=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=ie(n);return y.jsx(Jd,{...o,...r,ref:t})});If.displayName=Of;var Df="AlertDialogPortal",Fi=e=>{const{__scopeAlertDialog:t,...n}=e,r=ie(t);return y.jsx(eu,{...r,...n})};Fi.displayName=Df;var Lf="AlertDialogOverlay",Vi=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=ie(n);return y.jsx(tu,{...o,...r,ref:t})});Vi.displayName=Lf;var De="AlertDialogContent",[_f,Nf]=kf(De),Bi=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,children:r,...o}=e,i=ie(n),s=f.useRef(null),a=B(t,s),l=f.useRef(null);return y.jsx(Xd,{contentName:De,titleName:Hi,docsSlug:"alert-dialog",children:y.jsx(_f,{scope:n,cancelRef:l,children:y.jsxs(nu,{role:"alertdialog",...i,...o,ref:a,onOpenAutoFocus:V(o.onOpenAutoFocus,c=>{var d;c.preventDefault(),(d=l.current)==null||d.focus({preventScroll:!0})}),onPointerDownOutside:c=>c.preventDefault(),onInteractOutside:c=>c.preventDefault(),children:[y.jsx(hn,{children:r}),y.jsx(jf,{contentRef:s})]})})})});Bi.displayName=De;var Hi="AlertDialogTitle",Wi=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=ie(n);return y.jsx(ru,{...o,...r,ref:t})});Wi.displayName=Hi;var Gi="AlertDialogDescription",Ui=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=ie(n);return y.jsx(ou,{...o,...r,ref:t})});Ui.displayName=Gi;var $f="AlertDialogAction",Yi=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=ie(n);return y.jsx(Qo,{...o,...r,ref:t})});Yi.displayName=$f;var Xi="AlertDialogCancel",qi=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,{cancelRef:o}=Nf(Xi,n),i=ie(n),s=B(t,o);return y.jsx(Qo,{...i,...r,ref:s})});qi.displayName=Xi;var jf=({contentRef:e})=>{const t=`\`${De}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${De}\` by passing a \`${Gi}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${De}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return f.useEffect(()=>{var r;document.getElementById((r=e.current)==null?void 0:r.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},$h=zi,jh=Fi,zh=Vi,Fh=Bi,Vh=Yi,Bh=qi,Hh=Wi,Wh=Ui;function zf(e,t=[]){let n=[];function r(i,s){const a=f.createContext(s),l=n.length;n=[...n,s];function c(u){const{scope:p,children:h,...v}=u,m=(p==null?void 0:p[e][l])||a,w=f.useMemo(()=>v,Object.values(v));return y.jsx(m.Provider,{value:w,children:h})}function d(u,p){const h=(p==null?void 0:p[e][l])||a,v=f.useContext(h);if(v)return v;if(s!==void 0)return s;throw new Error(`\`${u}\` must be used within \`${i}\``)}return c.displayName=i+"Provider",[c,d]}const o=()=>{const i=n.map(s=>f.createContext(s));return function(a){const l=(a==null?void 0:a[e])||i;return f.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return o.scopeName=e,[r,Ff(o,...t)]}function Ff(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:l,scopeName:c})=>{const u=l(i)[`__scope${c}`];return{...a,...u}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function Vf(e){const t=e+"CollectionProvider",[n,r]=zf(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=h=>{const{scope:v,children:m}=h,w=j.useRef(null),g=j.useRef(new Map).current;return y.jsx(o,{scope:v,itemMap:g,collectionRef:w,children:m})};s.displayName=t;const a=e+"CollectionSlot",l=j.forwardRef((h,v)=>{const{scope:m,children:w}=h,g=i(a,m),x=B(v,g.collectionRef);return y.jsx(He,{ref:x,children:w})});l.displayName=a;const c=e+"CollectionItemSlot",d="data-radix-collection-item",u=j.forwardRef((h,v)=>{const{scope:m,children:w,...g}=h,x=j.useRef(null),S=B(v,x),b=i(c,m);return j.useEffect(()=>(b.itemMap.set(x,{ref:x,...g}),()=>void b.itemMap.delete(x))),y.jsx(He,{[d]:"",ref:S,children:w})});u.displayName=c;function p(h){const v=i(e+"CollectionConsumer",h);return j.useCallback(()=>{const w=v.collectionRef.current;if(!w)return[];const g=Array.from(w.querySelectorAll(`[${d}]`));return Array.from(v.itemMap.values()).sort((b,C)=>g.indexOf(b.ref.current)-g.indexOf(C.ref.current))},[v.collectionRef,v.itemMap])}return[{Provider:s,Slot:l,ItemSlot:u},p,r]}var jn="Collapsible",[Bf,Ki]=be(jn),[Hf,zn]=Bf(jn),Zi=f.forwardRef((e,t)=>{const{__scopeCollapsible:n,open:r,defaultOpen:o,disabled:i,onOpenChange:s,...a}=e,[l=!1,c]=ze({prop:r,defaultProp:o,onChange:s});return y.jsx(Hf,{scope:n,disabled:i,contentId:Be(),open:l,onOpenToggle:f.useCallback(()=>c(d=>!d),[c]),children:y.jsx(F.div,{"data-state":Vn(l),"data-disabled":i?"":void 0,...a,ref:t})})});Zi.displayName=jn;var Qi="CollapsibleTrigger",Ji=f.forwardRef((e,t)=>{const{__scopeCollapsible:n,...r}=e,o=zn(Qi,n);return y.jsx(F.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":Vn(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...r,ref:t,onClick:V(e.onClick,o.onOpenToggle)})});Ji.displayName=Qi;var Fn="CollapsibleContent",es=f.forwardRef((e,t)=>{const{forceMount:n,...r}=e,o=zn(Fn,e.__scopeCollapsible);return y.jsx(Ce,{present:n||o.open,children:({present:i})=>y.jsx(Wf,{...r,ref:t,present:i})})});es.displayName=Fn;var Wf=f.forwardRef((e,t)=>{const{__scopeCollapsible:n,present:r,children:o,...i}=e,s=zn(Fn,n),[a,l]=f.useState(r),c=f.useRef(null),d=B(t,c),u=f.useRef(0),p=u.current,h=f.useRef(0),v=h.current,m=s.open||a,w=f.useRef(m),g=f.useRef();return f.useEffect(()=>{const x=requestAnimationFrame(()=>w.current=!1);return()=>cancelAnimationFrame(x)},[]),fe(()=>{const x=c.current;if(x){g.current=g.current||{transitionDuration:x.style.transitionDuration,animationName:x.style.animationName},x.style.transitionDuration="0s",x.style.animationName="none";const S=x.getBoundingClientRect();u.current=S.height,h.current=S.width,w.current||(x.style.transitionDuration=g.current.transitionDuration,x.style.animationName=g.current.animationName),l(r)}},[s.open,r]),y.jsx(F.div,{"data-state":Vn(s.open),"data-disabled":s.disabled?"":void 0,id:s.contentId,hidden:!m,...i,ref:d,style:{"--radix-collapsible-content-height":p?`${p}px`:void 0,"--radix-collapsible-content-width":v?`${v}px`:void 0,...e.style},children:m&&o})});function Vn(e){return e?"open":"closed"}var Gf=Zi,Uf=Ji,Yf=es,Xf=f.createContext(void 0);function qf(e){const t=f.useContext(Xf);return e||t||"ltr"}var se="Accordion",Kf=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[Bn,Zf,Qf]=Vf(se),[Ot,Gh]=be(se,[Qf,Ki]),Hn=Ki(),ts=j.forwardRef((e,t)=>{const{type:n,...r}=e,o=r,i=r;return y.jsx(Bn.Provider,{scope:e.__scopeAccordion,children:n==="multiple"?y.jsx(np,{...i,ref:t}):y.jsx(tp,{...o,ref:t})})});ts.displayName=se;var[ns,Jf]=Ot(se),[rs,ep]=Ot(se,{collapsible:!1}),tp=j.forwardRef((e,t)=>{const{value:n,defaultValue:r,onValueChange:o=()=>{},collapsible:i=!1,...s}=e,[a,l]=ze({prop:n,defaultProp:r,onChange:o});return y.jsx(ns,{scope:e.__scopeAccordion,value:a?[a]:[],onItemOpen:l,onItemClose:j.useCallback(()=>i&&l(""),[i,l]),children:y.jsx(rs,{scope:e.__scopeAccordion,collapsible:i,children:y.jsx(os,{...s,ref:t})})})}),np=j.forwardRef((e,t)=>{const{value:n,defaultValue:r,onValueChange:o=()=>{},...i}=e,[s=[],a]=ze({prop:n,defaultProp:r,onChange:o}),l=j.useCallback(d=>a((u=[])=>[...u,d]),[a]),c=j.useCallback(d=>a((u=[])=>u.filter(p=>p!==d)),[a]);return y.jsx(ns,{scope:e.__scopeAccordion,value:s,onItemOpen:l,onItemClose:c,children:y.jsx(rs,{scope:e.__scopeAccordion,collapsible:!0,children:y.jsx(os,{...i,ref:t})})})}),[rp,It]=Ot(se),os=j.forwardRef((e,t)=>{const{__scopeAccordion:n,disabled:r,dir:o,orientation:i="vertical",...s}=e,a=j.useRef(null),l=B(a,t),c=Zf(n),u=qf(o)==="ltr",p=V(e.onKeyDown,h=>{var M;if(!Kf.includes(h.key))return;const v=h.target,m=c().filter(R=>{var E;return!((E=R.ref.current)!=null&&E.disabled)}),w=m.findIndex(R=>R.ref.current===v),g=m.length;if(w===-1)return;h.preventDefault();let x=w;const S=0,b=g-1,C=()=>{x=w+1,x>b&&(x=S)},T=()=>{x=w-1,x<S&&(x=b)};switch(h.key){case"Home":x=S;break;case"End":x=b;break;case"ArrowRight":i==="horizontal"&&(u?C():T());break;case"ArrowDown":i==="vertical"&&C();break;case"ArrowLeft":i==="horizontal"&&(u?T():C());break;case"ArrowUp":i==="vertical"&&T();break}const P=x%g;(M=m[P].ref.current)==null||M.focus()});return y.jsx(rp,{scope:n,disabled:r,direction:o,orientation:i,children:y.jsx(Bn.Slot,{scope:n,children:y.jsx(F.div,{...s,"data-orientation":i,ref:l,onKeyDown:r?void 0:p})})})}),xt="AccordionItem",[op,Wn]=Ot(xt),is=j.forwardRef((e,t)=>{const{__scopeAccordion:n,value:r,...o}=e,i=It(xt,n),s=Jf(xt,n),a=Hn(n),l=Be(),c=r&&s.value.includes(r)||!1,d=i.disabled||e.disabled;return y.jsx(op,{scope:n,open:c,disabled:d,triggerId:l,children:y.jsx(Gf,{"data-orientation":i.orientation,"data-state":us(c),...a,...o,ref:t,disabled:d,open:c,onOpenChange:u=>{u?s.onItemOpen(r):s.onItemClose(r)}})})});is.displayName=xt;var ss="AccordionHeader",as=j.forwardRef((e,t)=>{const{__scopeAccordion:n,...r}=e,o=It(se,n),i=Wn(ss,n);return y.jsx(F.h3,{"data-orientation":o.orientation,"data-state":us(i.open),"data-disabled":i.disabled?"":void 0,...r,ref:t})});as.displayName=ss;var pn="AccordionTrigger",ls=j.forwardRef((e,t)=>{const{__scopeAccordion:n,...r}=e,o=It(se,n),i=Wn(pn,n),s=ep(pn,n),a=Hn(n);return y.jsx(Bn.ItemSlot,{scope:n,children:y.jsx(Uf,{"aria-disabled":i.open&&!s.collapsible||void 0,"data-orientation":o.orientation,id:i.triggerId,...a,...r,ref:t})})});ls.displayName=pn;var cs="AccordionContent",ds=j.forwardRef((e,t)=>{const{__scopeAccordion:n,...r}=e,o=It(se,n),i=Wn(cs,n),s=Hn(n);return y.jsx(Yf,{role:"region","aria-labelledby":i.triggerId,"data-orientation":o.orientation,...s,...r,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});ds.displayName=cs;function us(e){return e?"open":"closed"}var Uh=ts,Yh=is,Xh=as,qh=ls,Kh=ds,Gn="Checkbox",[ip,Zh]=be(Gn),[sp,ap]=ip(Gn),fs=f.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:r,checked:o,defaultChecked:i,required:s,disabled:a,value:l="on",onCheckedChange:c,form:d,...u}=e,[p,h]=f.useState(null),v=B(t,b=>h(b)),m=f.useRef(!1),w=p?d||!!p.closest("form"):!0,[g=!1,x]=ze({prop:o,defaultProp:i,onChange:c}),S=f.useRef(g);return f.useEffect(()=>{const b=p==null?void 0:p.form;if(b){const C=()=>x(S.current);return b.addEventListener("reset",C),()=>b.removeEventListener("reset",C)}},[p,x]),y.jsxs(sp,{scope:n,state:g,disabled:a,children:[y.jsx(F.button,{type:"button",role:"checkbox","aria-checked":de(g)?"mixed":g,"aria-required":s,"data-state":ms(g),"data-disabled":a?"":void 0,disabled:a,value:l,...u,ref:v,onKeyDown:V(e.onKeyDown,b=>{b.key==="Enter"&&b.preventDefault()}),onClick:V(e.onClick,b=>{x(C=>de(C)?!0:!C),w&&(m.current=b.isPropagationStopped(),m.current||b.stopPropagation())})}),w&&y.jsx(lp,{control:p,bubbles:!m.current,name:r,value:l,checked:g,required:s,disabled:a,form:d,style:{transform:"translateX(-100%)"},defaultChecked:de(i)?!1:i})]})});fs.displayName=Gn;var ps="CheckboxIndicator",hs=f.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:r,...o}=e,i=ap(ps,n);return y.jsx(Ce,{present:r||de(i.state)||i.state===!0,children:y.jsx(F.span,{"data-state":ms(i.state),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});hs.displayName=ps;var lp=e=>{const{control:t,checked:n,bubbles:r=!0,defaultChecked:o,...i}=e,s=f.useRef(null),a=Di(n),l=bn(t);f.useEffect(()=>{const d=s.current,u=window.HTMLInputElement.prototype,h=Object.getOwnPropertyDescriptor(u,"checked").set;if(a!==n&&h){const v=new Event("click",{bubbles:r});d.indeterminate=de(n),h.call(d,de(n)?!1:n),d.dispatchEvent(v)}},[a,n,r]);const c=f.useRef(de(n)?!1:n);return y.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o??c.current,...i,tabIndex:-1,ref:s,style:{...e.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function de(e){return e==="indeterminate"}function ms(e){return de(e)?"indeterminate":e?"checked":"unchecked"}var Qh=fs,Jh=hs;export{Lh as $,Qd as A,bh as B,Sp as C,ou as D,Ch as E,Th as F,Ah as G,$u as H,_p as I,Hu as J,Ap as K,$p as L,Tp as M,Pp as N,tu as O,mh as P,jp as Q,Gp as R,q as S,ih as T,sh as U,Ph as V,Rh as W,ch as X,kh as Y,Oh as Z,Ih as _,Cp as a,_h as a0,hp as a1,Bp as a2,Kp as a3,th as a4,Yp as a5,Zp as a6,Dp as a7,zp as a8,eh as a9,up as aA,Xp as aB,Mp as aC,Jp as aD,bp as aE,Vp as aF,mp as aG,oh as aH,Wp as aI,rh as aJ,xp as aK,Np as aL,Qh as aM,Jh as aN,Op as aO,Qp as aa,Ip as ab,dh as ac,Up as ad,Rp as ae,Jd as af,zh as ag,Fh as ah,Hh as ai,Wh as aj,Vh as ak,Bh as al,jh as am,$h as an,Yh as ao,Xh as ap,qh as aq,gp as ar,Kh as as,Uh as at,Fp as au,kp as av,pp as aw,fp as ax,Ep as ay,Lp as az,vh as b,on as c,gr as d,ne as e,fr as f,ur as g,Fl as h,gh as i,y as j,yh as k,wp as l,wh as m,yp as n,Hp as o,He as p,ah as q,nh as r,xh as s,qp as t,lh as u,vp as v,nu as w,Qo as x,ru as y,eu as z};
