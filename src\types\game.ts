import { Suit, Rank } from "../utils/game/cardUtils";

export interface GameSession {
  id: string;
  status: "waiting" | "active" | "completed";
  created_at: string;
  updated_at: string;
  created_by: string;
  players: GamePlayer[];
  current_state?: GameState; // Sostituito any con GameState
  winner_team?: 0 | 1;
  difficulty?: "easy" | "medium" | "hard"; // Add difficulty setting
  // Private game properties
  is_private?: boolean;
  room_name?: string;
  host_id?: string;
  max_players?: number;
  victory_points?: number;
  expires_at?: string; // Auto-cleanup after 10 minutes
  game_timer?: number; // Turn timer in seconds (default 60)
}

export interface GamePlayer {
  id: string;
  user_id: string;
  username: string;
  position: "north" | "east" | "south" | "west";
  team: 0 | 1;
  connected: boolean;
  level?: number; // Add player level
  is_ai?: boolean; // Indicate if player is AI
  ai_difficulty?: "easy" | "medium" | "hard"; // AI difficulty if applicable
  avatar_url?: string; // Player avatar for private games
  rank?: string; // Player rank display
  joined_at?: string; // When player joined the session
}

export interface GameMove {
  session_id: string;
  player_id: string;
  action: "playCard" | "selectTrump" | "announceAction";
  payload: GameMovePayload; // Sostituito any con un tipo dedicato
  created_at: string;
}

// Tipo specifico per il payload delle mosse di gioco
export interface GameMovePayload {
  cardId?: string;
  suit?: string;
  action?: string;
  teamId?: 0 | 1;
  [key: string]: unknown; // Per eventuali proprietà aggiuntive future
}

// Update the Card interface to be more compatible with cardUtils.Card
export interface Card {
  id: string;
  suit?: Suit | string;
  rank?: Rank | string;
  value: string | number;
  playedBy?: number;
  // Adding these properties to match cardUtils.Card interface
  displayName?: string;
  order?: number;
  points?: number;
}

// Update the GameState interface needed by gameService
export interface GameState {
  players: Player[];
  playedCards: Card[];
  currentPlayer: number;
  trickWinner: number | null;
  trickHistory: {
    cards: Card[];
    winner: number | null;
    points: number;
  }[];
  teamPoints: {
    [key: number]: number;
  };
  briscola?: string;
  gameOver?: boolean;
  winner?: 0 | 1 | null;
  isLastTrick?: boolean; // Add flag to track last trick
  // Required properties for the improved version
  trumpSuit?: string | null;
  leadSuit?: string | null;
  gamePhase?: string;
  roundScore?: number[];
  gameScore?: number[];
  leadPlayer?: number;
  currentTrick?: Card[];
  message?: string;
  difficulty?: "easy" | "medium" | "hard"; // Add difficulty setting
  victoryPoints?: number; // Add configurable victory points
  ai_players?: {
    id: number;
    difficulty: "easy" | "medium" | "hard";
  }[]; // Track AI players and their difficulty
  teams?: TeamState[]; // Aggiunta per evitare any
  trickNumber?: number; // Aggiunta per evitare any in aiLogic.ts
}

// Definizione del tipo Team per evitare any
export interface TeamState {
  id: 0 | 1;
  score: number;
  tricksWon: Card[][];
  currentRoundPoints: number;
}

// Update the Player interface needed by gameService
export interface Player {
  hand: Card[];
  team: 0 | 1; // Updated to be numeric only
  id?: number;
  name?: string;
  position?: "north" | "east" | "south" | "west";
  level?: number; // Add player level
  is_ai?: boolean; // Indicate if player is AI
}

// Private Game Types
export interface PrivateGameSettings {
  roomName: string;
  victoryPoints: number;
  gameTimer: number; // seconds per turn
  maxPlayers: number;
}

export interface PrivateGameLobbyState {
  session: GameSession;
  isHost: boolean;
  canStart: boolean; // true when 4 players and 2 per team
  countdown?: number; // countdown timer before game starts
}

export interface TeamSelection {
  team: 0 | 1; // 0 = yellow team, 1 = red team
  playerId: string;
}

export interface LobbyPlayer {
  id: string;
  user_id: string;
  username: string;
  team: 0 | 1;
  connected: boolean;
  level?: number;
  rank?: string;
  avatar_url?: string;
  is_host?: boolean;
}

// Realtime Events for Private Games
export interface RealtimeGameEvent {
  type:
    | "player_joined"
    | "player_left"
    | "team_changed"
    | "game_starting"
    | "game_started"
    | "turn_timer"
    | "player_disconnected";
  payload: any;
  timestamp: string;
  session_id: string;
}

export interface TurnTimerState {
  currentPlayer: number;
  timeRemaining: number; // seconds
  isActive: boolean;
  startedAt: string;
}
