import { useState, useEffect, useCallback, useRef } from 'react';
import { TurnTimerState } from '@/types/game';

interface UseTurnTimerProps {
  isOnline: boolean;
  isPrivateGame: boolean;
  timerDuration: number; // seconds
  currentPlayer: number;
  isCurrentPlayerTurn: boolean;
  onTimeUp: () => void;
}

interface UseTurnTimerReturn {
  timerState: TurnTimerState;
  startTimer: () => void;
  stopTimer: () => void;
  resetTimer: () => void;
  isTimerActive: boolean;
}

export const useTurnTimer = ({
  isOnline,
  isPrivateGame,
  timerDuration,
  currentPlayer,
  isCurrentPlayerTurn,
  onTimeUp,
}: UseTurnTimerProps): UseTurnTimerReturn => {
  const [timerState, setTimerState] = useState<TurnTimerState>({
    currentPlayer,
    timeRemaining: timerDuration,
    isActive: false,
    startedAt: new Date().toISOString(),
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const hasCalledTimeUp = useRef(false);

  // Only use timer for online private games
  const shouldUseTimer = isOnline && isPrivateGame;

  // Reset timer when current player changes
  useEffect(() => {
    if (shouldUseTimer) {
      setTimerState(prev => ({
        ...prev,
        currentPlayer,
        timeRemaining: timerDuration,
        startedAt: new Date().toISOString(),
      }));
      hasCalledTimeUp.current = false;
    }
  }, [currentPlayer, timerDuration, shouldUseTimer]);

  // Start timer
  const startTimer = useCallback(() => {
    if (!shouldUseTimer || !isCurrentPlayerTurn) return;

    setTimerState(prev => ({
      ...prev,
      isActive: true,
      startedAt: new Date().toISOString(),
    }));

    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Start countdown
    intervalRef.current = setInterval(() => {
      setTimerState(prev => {
        const newTimeRemaining = prev.timeRemaining - 1;

        if (newTimeRemaining <= 0) {
          // Time's up - call onTimeUp only once
          if (!hasCalledTimeUp.current) {
            hasCalledTimeUp.current = true;
            setTimeout(() => onTimeUp(), 0);
          }

          return {
            ...prev,
            timeRemaining: 0,
            isActive: false,
          };
        }

        return {
          ...prev,
          timeRemaining: newTimeRemaining,
        };
      });
    }, 1000);
  }, [shouldUseTimer, isCurrentPlayerTurn, onTimeUp]);

  // Stop timer
  const stopTimer = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    setTimerState(prev => ({
      ...prev,
      isActive: false,
    }));
  }, []);

  // Reset timer
  const resetTimer = useCallback(() => {
    stopTimer();
    setTimerState(prev => ({
      ...prev,
      timeRemaining: timerDuration,
      isActive: false,
      startedAt: new Date().toISOString(),
    }));
    hasCalledTimeUp.current = false;
  }, [stopTimer, timerDuration]);

  // Auto-start timer when it's current player's turn
  useEffect(() => {
    if (shouldUseTimer && isCurrentPlayerTurn && timerState.timeRemaining > 0) {
      startTimer();
    } else {
      stopTimer();
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [shouldUseTimer, isCurrentPlayerTurn, startTimer, stopTimer, timerState.timeRemaining]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    timerState,
    startTimer,
    stopTimer,
    resetTimer,
    isTimerActive: shouldUseTimer && timerState.isActive,
  };
};
