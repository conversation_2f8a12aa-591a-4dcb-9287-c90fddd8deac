-- Private Games Schema for <PERSON>ffa Romagnola
-- This script creates the necessary tables for private multiplayer games

-- Create game_sessions table if it doesn't exist
CREATE TABLE IF NOT EXISTS game_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT CHECK (status IN ('waiting', 'active', 'completed')) DEFAULT 'waiting',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Private game specific fields
    is_private BOOLEAN DEFAULT FALSE,
    room_name TEXT,
    host_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    max_players INTEGER DEFAULT 4,
    victory_points INTEGER DEFAULT 31,
    game_timer INTEGER DEFAULT 60, -- seconds per turn
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Game state
    current_state JSONB,
    winner_team INTEGER CHECK (winner_team IN (0, 1)),
    difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard'))
);

-- Create game_players table if it doesn't exist
CREATE TABLE IF NOT EXISTS game_players (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id UUID REFERENCES game_sessions(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    username TEXT NOT NULL,
    position TEXT CHECK (position IN ('north', 'east', 'south', 'west')),
    team INTEGER CHECK (team IN (0, 1)) NOT NULL,
    connected BOOLEAN DEFAULT TRUE,
    level INTEGER,
    is_ai BOOLEAN DEFAULT FALSE,
    ai_difficulty TEXT CHECK (ai_difficulty IN ('easy', 'medium', 'hard')),
    avatar_url TEXT,
    rank TEXT,
    is_host BOOLEAN DEFAULT FALSE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Connection tracking
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    disconnected_at TIMESTAMP WITH TIME ZONE,

    -- Ensure unique user per session
    UNIQUE(session_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_game_sessions_room_name ON game_sessions(room_name) WHERE is_private = TRUE;
CREATE INDEX IF NOT EXISTS idx_game_sessions_status ON game_sessions(status);
CREATE INDEX IF NOT EXISTS idx_game_sessions_expires_at ON game_sessions(expires_at) WHERE is_private = TRUE;
CREATE INDEX IF NOT EXISTS idx_game_sessions_host_id ON game_sessions(host_id);
CREATE INDEX IF NOT EXISTS idx_game_players_session_id ON game_players(session_id);
CREATE INDEX IF NOT EXISTS idx_game_players_user_id ON game_players(user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for game_sessions updated_at
DROP TRIGGER IF EXISTS update_game_sessions_updated_at ON game_sessions;
CREATE TRIGGER update_game_sessions_updated_at
    BEFORE UPDATE ON game_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE game_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_players ENABLE ROW LEVEL SECURITY;

-- RLS Policies for game_sessions
-- Users can read sessions they are part of or public sessions
CREATE POLICY "Users can read their game sessions" ON game_sessions
    FOR SELECT USING (
        auth.uid() = created_by OR 
        auth.uid() IN (
            SELECT user_id FROM game_players WHERE session_id = game_sessions.id
        )
    );

-- Users can create their own sessions
CREATE POLICY "Users can create game sessions" ON game_sessions
    FOR INSERT WITH CHECK (auth.uid() = created_by);

-- Only host can update session
CREATE POLICY "Host can update game sessions" ON game_sessions
    FOR UPDATE USING (auth.uid() = host_id);

-- Host can delete session
CREATE POLICY "Host can delete game sessions" ON game_sessions
    FOR DELETE USING (auth.uid() = host_id);

-- RLS Policies for game_players
-- Users can read players in sessions they are part of
CREATE POLICY "Users can read game players" ON game_players
    FOR SELECT USING (
        auth.uid() = user_id OR
        auth.uid() IN (
            SELECT user_id FROM game_players gp2 WHERE gp2.session_id = game_players.session_id
        )
    );

-- Users can join sessions (insert themselves)
CREATE POLICY "Users can join game sessions" ON game_players
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own player data
CREATE POLICY "Users can update their player data" ON game_players
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can leave sessions (delete themselves) or host can remove players
CREATE POLICY "Users can leave or host can remove players" ON game_players
    FOR DELETE USING (
        auth.uid() = user_id OR
        auth.uid() IN (
            SELECT host_id FROM game_sessions WHERE id = game_players.session_id
        )
    );

-- Create function to cleanup expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS void AS $$
BEGIN
    -- Delete expired private game sessions
    DELETE FROM game_sessions 
    WHERE is_private = TRUE 
    AND status = 'waiting' 
    AND expires_at < NOW();
    
    -- Log cleanup
    RAISE NOTICE 'Cleaned up expired game sessions at %', NOW();
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run cleanup every 5 minutes
-- Note: This requires pg_cron extension to be enabled
-- SELECT cron.schedule('cleanup-expired-sessions', '*/5 * * * *', 'SELECT cleanup_expired_sessions();');

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON game_sessions TO authenticated;
GRANT ALL ON game_players TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
