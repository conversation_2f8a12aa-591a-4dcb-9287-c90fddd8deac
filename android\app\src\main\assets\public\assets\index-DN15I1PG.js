import{r as n}from"./game-hrinnRPS.js";import"./audioManager-oMWoQbcF.js";import"./audio-5UOY9KLB.js";import"./vendor-COrNHRvO.js";var t;(function(e){e.notDetermined="notDetermined",e.restricted="restricted",e.denied="denied",e.authorized="authorized"})(t||(t={}));const r=n("AppTrackingTransparency");function i(e){return()=>e().then(o=>o.value)}class c{constructor(){this.getStatus=i(r.getStatus),this.requestPermission=i(r.requestPermission)}}export{c as AppTrackingTransparency,t as AppTrackingTransparencyStatus};
