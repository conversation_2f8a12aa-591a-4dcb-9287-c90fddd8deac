import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { X, Search, Users, AlertCircle } from 'lucide-react';
import ActionButton from '@/components/ui/ActionButton';
import { useAuth } from '@/context/auth-context';
import { useToast } from '@/hooks/use-toast';
import { useAudio } from '@/hooks/useAudio';
import { joinPrivateGameSession } from '@/services/onlineGameService';

interface JoinPrivateGameModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const JoinPrivateGameModal: React.FC<JoinPrivateGameModalProps> = ({
  isOpen,
  onClose,
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const { playSound } = useAudio();

  const [roomName, setRoomName] = useState('');
  const [isJoining, setIsJoining] = useState(false);

  const handleJoinGame = async () => {
    if (!user) {
      toast({
        title: "Errore",
        description: "Devi essere loggato per unirti a una partita",
        variant: "destructive",
      });
      return;
    }

    if (!roomName.trim()) {
      toast({
        title: "Nome richiesto",
        description: "Inserisci il nome della stanza",
        variant: "destructive",
      });
      return;
    }

    setIsJoining(true);

    try {
      const session = await joinPrivateGameSession(
        roomName.trim(),
        user.id,
        user.user_metadata?.username || user.email?.split('@')[0] || 'Giocatore'
      );

      playSound('buttonClick');
      
      toast({
        title: "Entrato nella stanza!",
        description: `Ti sei unito alla stanza "${roomName}"`,
      });

      // Navigate to lobby
      navigate('/lobby', {
        state: {
          sessionId: session.id,
          isHost: false,
          roomName: roomName.trim(),
        },
      });

      onClose();
    } catch (error) {
      console.error('Error joining private game:', error);
      let errorMessage = "Errore durante l'accesso alla stanza";
      
      if (error instanceof Error) {
        if (error.message.includes('non trovata')) {
          errorMessage = "Stanza non trovata o scaduta";
        } else if (error.message.includes('piena')) {
          errorMessage = "La stanza è piena (massimo 4 giocatori)";
        } else if (error.message.includes('già in questa stanza')) {
          errorMessage = "Sei già in questa stanza";
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        title: "Impossibile entrare",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsJoining(false);
    }
  };

  const handleClose = () => {
    playSound('buttonClick');
    onClose();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isJoining && roomName.trim()) {
      handleJoinGame();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl p-6 w-full max-w-md mx-4 shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 
            className="text-2xl font-bold text-romagna-darkWood"
            style={{ fontFamily: "'DynaPuff', cursive" }}
          >
            🔍 Unisciti a Partita
          </h2>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <div className="space-y-6">
          {/* Room Name Input */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Nome Stanza
            </label>
            <div className="relative">
              <input
                type="text"
                value={roomName}
                onChange={(e) => setRoomName(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Inserisci il nome esatto della stanza"
                className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
                maxLength={20}
                disabled={isJoining}
                autoFocus
              />
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Chiedi il nome della stanza al tuo amico
            </p>
          </div>

          {/* Info */}
          <div className="bg-blue-50 p-4 rounded-xl">
            <div className="flex items-start gap-3">
              <Users className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-semibold mb-1">Come funziona:</p>
                <ul className="space-y-1 text-xs">
                  <li>• Inserisci il nome esatto della stanza</li>
                  <li>• Entrerai nella lobby con gli altri giocatori</li>
                  <li>• Potrai scegliere la tua squadra (gialla o rossa)</li>
                  <li>• L'host avvierà la partita quando tutti sono pronti</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Warning */}
          <div className="bg-amber-50 p-4 rounded-xl">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5" />
              <div className="text-sm text-amber-800">
                <p className="font-semibold mb-1">Attenzione:</p>
                <p className="text-xs">
                  Il nome della stanza deve essere esatto. Se non riesci a entrare, 
                  verifica di aver scritto correttamente il nome o che la stanza non sia scaduta.
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-3">
            <ActionButton
              onClick={handleClose}
              disabled={isJoining}
              className="flex-1 py-3 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-xl font-semibold transition-all duration-200"
            >
              Annulla
            </ActionButton>
            <ActionButton
              onClick={handleJoinGame}
              disabled={isJoining || !roomName.trim()}
              className="flex-1 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl font-bold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isJoining ? 'Entrando...' : 'Entra'}
            </ActionButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JoinPrivateGameModal;
