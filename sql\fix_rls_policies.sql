-- Fix RLS Policies for Private Games
-- This script fixes the infinite recursion issue in game_players policies

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can read game players" ON game_players;
DROP POLICY IF EXISTS "Users can join game sessions" ON game_players;
DROP POLICY IF EXISTS "Users can update their player data" ON game_players;
DROP POLICY IF EXISTS "Users can leave or host can remove players" ON game_players;

-- Recreate policies without recursion
-- Users can read players in sessions they are part of (fixed version)
CREATE POLICY "Users can read game players" ON game_players
    FOR SELECT USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM game_sessions gs 
            WHERE gs.id = game_players.session_id 
            AND (gs.created_by = auth.uid() OR gs.host_id = auth.uid())
        )
    );

-- Users can join sessions (insert themselves)
CREATE POLICY "Users can join game sessions" ON game_players
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own player data
CREATE POLICY "Users can update their player data" ON game_players
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can leave sessions (delete themselves) or host can remove players
CREATE POLICY "Users can leave or host can remove players" ON game_players
    FOR DELETE USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM game_sessions gs 
            WHERE gs.id = game_players.session_id 
            AND gs.host_id = auth.uid()
        )
    );

-- Also fix game_sessions policies if needed
DROP POLICY IF EXISTS "Users can read their game sessions" ON game_sessions;

-- Recreate game_sessions read policy
CREATE POLICY "Users can read their game sessions" ON game_sessions
    FOR SELECT USING (
        auth.uid() = created_by OR 
        auth.uid() = host_id OR
        EXISTS (
            SELECT 1 FROM game_players gp 
            WHERE gp.session_id = game_sessions.id 
            AND gp.user_id = auth.uid()
        )
    );
