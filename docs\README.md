# 📚 Documentazione - Marafone Romagnolo

Benvenuti nella documentazione completa del progetto **Marafone Romagnolo**, un'implementazione digitale del tradizionale gioco di carte romagnolo sviluppato con React, TypeScript e tecnologie moderne.

---

## 🚀 Navigazione Rapida

| 🎯 **Per Iniziare**                        | 🛠️ **Per Sviluppatori**                      | 📱 **Per Publishing**                       |
| ------------------------------------------ | -------------------------------------------- | ------------------------------------------- |
| [📋 Panoramica App](./APP_OVERVIEW.md)     | [🏗️ Architettura](./GUIDA_REPOSITORY.md)     | [🏪 Play Store](./PLAY_STORE_LISTING.md)    |
| [🎮 Regole di Gioco](./logica-di-gioco.md) | [🤖 Logica AI](./CPU_LOGIC_DOCUMENTATION.md) | [🍎 iOS Setup](./IOS_SETUP_GUIDE.md)        |
| [⚡ Quick Index](./QUICK_INDEX.md)         | [🎨 Assets Guide](./IMAGE_ASSETS.md)         | [💰 Monetizzazione](./ADMOB_INTEGRATION.md) |

---

## 📂 Documentazione Organizzata

### 🎯 **Essenziali - Inizia da Qui**

| Documento                                         | Descrizione                                          | Priorità |
| ------------------------------------------------- | ---------------------------------------------------- | -------- |
| **[📋 APP_OVERVIEW.md](./APP_OVERVIEW.md)**       | Panoramica completa dell'applicazione e funzionalità | 🔥 Alta  |
| **[⚡ QUICK_INDEX.md](./QUICK_INDEX.md)**         | Indice compresso con navigazione veloce              | 🔥 Alta  |
| **[🎮 logica-di-gioco.md](./logica-di-gioco.md)** | Regole complete del Marafone Romagnolo               | 🔥 Alta  |

### 🛠️ **Sviluppo e Architettura**

| Documento                                                                       | Descrizione                                             | Priorità |
| ------------------------------------------------------------------------------- | ------------------------------------------------------- | -------- |
| **[🏗️ GUIDA_REPOSITORY.md](./GUIDA_REPOSITORY.md)**                             | Architettura, stack tecnologico e organizzazione codice | 🔥 Alta  |
| **[🤖 CPU_LOGIC_DOCUMENTATION.md](./CPU_LOGIC_DOCUMENTATION.md)**               | Documentazione dettagliata logica AI e strategie        | 🔥 Alta  |
| **[📊 AI_RULES_IMPLEMENTATION_REPORT.md](./AI_RULES_IMPLEMENTATION_REPORT.md)** | Report implementazione regole AI                        | 🟡 Media |
| **[🎨 IMAGE_ASSETS.md](./IMAGE_ASSETS.md)**                                     | Organizzazione e convenzioni per assets grafici         | 🟡 Media |

### 📱 **Publishing e Distribuzione**

| Documento                                               | Descrizione                                      | Priorità |
| ------------------------------------------------------- | ------------------------------------------------ | -------- |
| **[🏪 PLAY_STORE_LISTING.md](./PLAY_STORE_LISTING.md)** | Informazioni per pubblicazione Google Play Store | 🔥 Alta  |
| **[🍎 IOS_SETUP_GUIDE.md](./IOS_SETUP_GUIDE.md)**       | Guida configurazione e pubblicazione iOS         | 🔥 Alta  |
| **[📱 IPHONE.md](./IPHONE.md)**                         | Specifiche e adattamenti per dispositivi iPhone  | 🟡 Media |

### 🔐 **Sicurezza e Integrazioni**

| Documento                                                               | Descrizione                         | Priorità |
| ----------------------------------------------------------------------- | ----------------------------------- | -------- |
| **[🔒 SECURITY_GUIDE.md](./SECURITY_GUIDE.md)**                         | Guida sicurezza e best practices    | 🔥 Alta  |
| **[🔑 OAUTH_SETUP.md](./OAUTH_SETUP.md)**                               | Configurazione autenticazione OAuth | 🟡 Media |
| **[💰 ADMOB_INTEGRATION.md](./ADMOB_INTEGRATION.md)**                   | Integrazione AdMob per pubblicità   | 🟡 Media |
| **[💎 PREMIUM_SUBSCRIPTION_SETUP.md](./PREMIUM_SUBSCRIPTION_SETUP.md)** | Setup abbonamenti premium           | 🟢 Bassa |

### 📄 **Legale e Compliance**

| Documento                                           | Descrizione                           | Priorità |
| --------------------------------------------------- | ------------------------------------- | -------- |
| **[🛡️ PRIVACY_POLICY.md](./PRIVACY_POLICY.md)**     | Policy privacy e gestione dati utente | 🔥 Alta  |
| **[📋 TERMS_OF_SERVICE.md](./TERMS_OF_SERVICE.md)** | Termini di servizio dell'applicazione | 🔥 Alta  |

---

## 🎯 Percorsi di Lettura Consigliati

### 🆕 **Nuovo Sviluppatore** (Prima volta nel progetto)

```
1️⃣ APP_OVERVIEW.md          → Capire cosa fa l'app
2️⃣ GUIDA_REPOSITORY.md      → Architettura del progetto
3️⃣ logica-di-gioco.md       → Regole implementate
4️⃣ CPU_LOGIC_DOCUMENTATION.md → Logica AI
```

### 🔧 **Contribuitore** (Vuoi aggiungere funzionalità)

```
1️⃣ GUIDA_REPOSITORY.md      → Struttura del codice
2️⃣ IMAGE_ASSETS.md          → Gestione assets grafici
3️⃣ SECURITY_GUIDE.md        → Best practices sicurezza
4️⃣ AI_RULES_IMPLEMENTATION_REPORT.md → Implementazione AI
```

### 📱 **Publisher Android** (Pubblicare su Play Store)

```
1️⃣ PLAY_STORE_LISTING.md    → Informazioni Play Store
2️⃣ ADMOB_INTEGRATION.md     → Configurazione pubblicità
3️⃣ PRIVACY_POLICY.md        → Policy privacy
4️⃣ TERMS_OF_SERVICE.md      → Termini di servizio
```

### 🍎 **Publisher iOS** (Pubblicare su App Store)

```
1️⃣ IOS_SETUP_GUIDE.md       → Configurazione iOS
2️⃣ IPHONE.md                → Adattamenti iPhone
3️⃣ OAUTH_SETUP.md           → Autenticazione
4️⃣ PRIVACY_POLICY.md        → Policy privacy
```

---

## 🎯 Caratteristiche del Progetto

### ✨ **Tecnologie Principali**

- **React 18** + **TypeScript** per l'interfaccia utente
- **Tailwind CSS** + **shadcn/ui** per lo styling
- **Vite** per il build system
- **Capacitor** per l'app nativa Android

### 🎮 **Funzionalità**

- Gioco completo del Marafone Romagnolo con regole autentiche
- Sistema AI con 3 livelli di difficoltà
- Design responsive e ottimizzazioni mobile
- Sistema avanzato di cache per le performance
- PWA ready con installazione nativa

### 📊 **Prestazioni**

- Caricamento immagini ottimizzato con cache intelligente
- Precaricamento adattivo basato su dispositivo e connessione
- Bundle splitting e lazy loading per tempi di caricamento veloci

---

## 📝 Standard di Documentazione

### Convenzioni Utilizzate

- **📁 File markdown** organizzati per argomento
- **🔗 Link interni** tra documenti correlati
- **📋 Tabelle** per informazioni strutturate
- **💡 Esempi pratici** per illustrare concetti
- **⚠️ Note importanti** evidenziate chiaramente

### Mantenimento

- ✅ Documentazione aggiornata ad ogni release
- ✅ Esempi di codice testati e funzionanti
- ✅ Link verificati e funzionanti
- ✅ Versioning sincronizzato con il codice

---

## 🔗 Riferimenti Utili

### Documentazione Tecnica

- [React Documentation](https://react.dev/) - Libreria UI principale
- [TypeScript Handbook](https://www.typescriptlang.org/docs/) - Type safety
- [Tailwind CSS](https://tailwindcss.com/docs) - Framework CSS
- [Vite Guide](https://vitejs.dev/guide/) - Build tool

### Deployment e Mobile

- [Capacitor Documentation](https://capacitorjs.com/docs) - Wrapper nativo
- [Android Developer Guide](https://developer.android.com/docs) - Sviluppo Android
- [PWA Guidelines](https://web.dev/progressive-web-apps/) - Progressive Web Apps

---

_Ultima modifica: 21 luglio 2025 | Versione documentazione: 2.1_
