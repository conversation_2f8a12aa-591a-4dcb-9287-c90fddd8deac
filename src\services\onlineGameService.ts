import { supabase } from "@/integrations/supabase/client";

// Utility function for API calls with retry and timeout
const apiCallWithRetry = async <T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  timeoutMs: number = 10000
): Promise<T> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 API call attempt ${attempt}/${maxRetries}`);

      // Create timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error("Request timeout")), timeoutMs);
      });

      // Race between API call and timeout
      const result = await Promise.race([apiCall(), timeoutPromise]);

      console.log(`✅ API call successful on attempt ${attempt}`);
      return result;
    } catch (error) {
      console.error(`❌ API call failed on attempt ${attempt}:`, error);

      if (attempt === maxRetries) {
        throw new Error(
          `API call failed after ${maxRetries} attempts: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }

      // Wait before retry (exponential backoff)
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
      console.log(`⏳ Retrying in ${delay}ms...`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw new Error("Unexpected error in apiCallWithRetry");
};
import {
  GameSession,
  GamePlayer,
  GameState,
  PrivateGameSettings,
  RealtimeGameEvent,
  LobbyPlayer,
} from "@/types/game";
import { MULTIPLAYER_CONFIG } from "@/config/multiplayer";
import { RealtimeChannel } from "@supabase/supabase-js";

// Utility function to generate room names
const generateRoomId = (): string => {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
};

// Utility function to get player position based on join order
const getPlayerPosition = (
  playerIndex: number
): "north" | "east" | "south" | "west" => {
  const positions: ("north" | "east" | "south" | "west")[] = [
    "north",
    "east",
    "south",
    "west",
  ];
  return positions[playerIndex % 4];
};

/**
 * Create a new private game session
 */
export const createPrivateGameSession = async (
  userId: string,
  username: string,
  settings: PrivateGameSettings
): Promise<GameSession> => {
  try {
    console.log("🎮 Creating private game session:", {
      userId,
      username,
      settings,
    });

    // First check if tables exist with retry
    console.log("🔍 Checking database connection...");
    const testResult = await apiCallWithRetry(async () => {
      return await supabase.from("game_sessions").select("id").limit(1);
    });

    if (testResult.error) {
      console.error("❌ Database connection test failed:", testResult.error);
      throw new Error(`Database error: ${testResult.error.message}`);
    }

    console.log("✅ Database connection OK");

    // Create the game session
    const sessionData = {
      created_by: userId,
      status: "waiting" as const,
      is_private: true,
      room_name: settings.roomName,
      host_id: userId,
      max_players: settings.maxPlayers,
      victory_points: settings.victoryPoints,
      game_timer: MULTIPLAYER_CONFIG.DEFAULT_GAME_TIMER, // Fixed 30 seconds
      expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes from now
    };

    console.log("📝 Inserting session data:", sessionData);

    const sessionResult = await apiCallWithRetry(async () => {
      return await supabase
        .from("game_sessions")
        .insert(sessionData)
        .select()
        .single();
    });

    if (sessionResult.error) {
      console.error("❌ Error creating game session:", sessionResult.error);
      console.error("❌ Session data that failed:", sessionData);
      throw new Error(
        `Failed to create game session: ${sessionResult.error.message}`
      );
    }

    const session = sessionResult.data;
    console.log("✅ Game session created:", session);

    // Add the host as the first player
    const playerData = {
      session_id: session.id,
      user_id: userId,
      username: username,
      position: "north" as const,
      team: 0 as const,
      connected: true,
      is_host: true,
      joined_at: new Date().toISOString(),
    };

    console.log("👤 Adding host player:", playerData);

    const playerResult = await apiCallWithRetry(async () => {
      return await supabase.from("game_players").insert(playerData);
    });

    if (playerResult.error) {
      console.error("❌ Error adding host player:", playerResult.error);
      console.error("❌ Player data that failed:", playerData);
      // Cleanup the session if player creation fails
      console.log("🧹 Cleaning up session due to player creation failure");
      await supabase.from("game_sessions").delete().eq("id", session.id);
      throw new Error(
        `Failed to add host player: ${playerResult.error.message}`
      );
    }

    console.log("✅ Host player added successfully");

    // Return the session with the host player
    const gameSession: GameSession = {
      ...session,
      players: [
        {
          id: "0",
          user_id: userId,
          username: username,
          position: "north",
          team: 0,
          connected: true,
          is_host: true,
          joined_at: new Date().toISOString(),
        },
      ],
    };

    console.log(
      "✅ Private game session created successfully:",
      gameSession.id
    );
    return gameSession;
  } catch (error) {
    console.error("❌ Error in createPrivateGameSession:", error);
    throw error;
  }
};

/**
 * Join a private game session by room name
 */
export const joinPrivateGameSession = async (
  roomName: string,
  userId: string,
  username: string
): Promise<GameSession> => {
  try {
    console.log("🚪 Joining private game session:", {
      roomName,
      userId,
      username,
    });

    // Find the game session by room name
    const { data: sessions, error: sessionError } = await supabase
      .from("game_sessions")
      .select(
        `
        *,
        game_players (*)
      `
      )
      .eq("room_name", roomName)
      .eq("is_private", true)
      .eq("status", "waiting")
      .gt("expires_at", new Date().toISOString());

    if (sessionError) {
      console.error("❌ Error finding game session:", sessionError);
      throw new Error(`Failed to find game session: ${sessionError.message}`);
    }

    if (!sessions || sessions.length === 0) {
      throw new Error("Stanza non trovata o scaduta");
    }

    const session = sessions[0];
    const currentPlayers = session.game_players || [];

    // Check if room is full
    if (currentPlayers.length >= (session.max_players || 4)) {
      throw new Error("La stanza è piena");
    }

    // Check if user is already in the session
    if (currentPlayers.some((p: any) => p.user_id === userId)) {
      throw new Error("Sei già in questa stanza");
    }

    // Add the player to the session
    const playerPosition = getPlayerPosition(currentPlayers.length);
    const playerTeam = currentPlayers.length % 2; // Alternate teams

    const playerData = {
      session_id: session.id,
      user_id: userId,
      username: username,
      position: playerPosition,
      team: playerTeam as 0 | 1,
      connected: true,
      is_host: false,
      joined_at: new Date().toISOString(),
    };

    const { error: playerError } = await supabase
      .from("game_players")
      .insert(playerData);

    if (playerError) {
      console.error("❌ Error adding player:", playerError);
      throw new Error(`Failed to join game: ${playerError.message}`);
    }

    // Get updated session with all players
    const updatedSession = await getSessionData(session.id);

    console.log("✅ Successfully joined private game session");
    return updatedSession;
  } catch (error) {
    console.error("❌ Error in joinPrivateGameSession:", error);
    throw error;
  }
};

/**
 * Get session data with all players
 */
export const getSessionData = async (
  sessionId: string
): Promise<GameSession> => {
  const { data: session, error } = await supabase
    .from("game_sessions")
    .select(
      `
      *,
      game_players (*)
    `
    )
    .eq("id", sessionId)
    .single();

  if (error) {
    throw new Error(`Failed to get session data: ${error.message}`);
  }

  // Transform players data
  const players: GamePlayer[] = (session.game_players || []).map(
    (p: any, index: number) => ({
      id: index.toString(),
      user_id: p.user_id,
      username: p.username,
      position: p.position,
      team: p.team,
      connected: p.connected,
      level: p.level,
      is_host: p.is_host,
      joined_at: p.joined_at,
    })
  );

  return {
    ...session,
    players,
  };
};

/**
 * Leave a game session
 */
export const leaveGameSession = async (
  sessionId: string,
  userId: string
): Promise<void> => {
  try {
    console.log("🚪 Leaving game session:", { sessionId, userId });

    // Remove player from session
    const { error } = await supabase
      .from("game_players")
      .delete()
      .eq("session_id", sessionId)
      .eq("user_id", userId);

    if (error) {
      console.error("❌ Error leaving game session:", error);
      throw new Error(`Failed to leave game: ${error.message}`);
    }

    // Check if session is now empty and delete if so
    const { data: remainingPlayers } = await supabase
      .from("game_players")
      .select("id")
      .eq("session_id", sessionId);

    if (!remainingPlayers || remainingPlayers.length === 0) {
      await supabase.from("game_sessions").delete().eq("id", sessionId);
      console.log("🗑️ Empty session deleted:", sessionId);
    }

    console.log("✅ Successfully left game session");
  } catch (error) {
    console.error("❌ Error in leaveGameSession:", error);
    throw error;
  }
};

/**
 * Update player team in lobby
 */
export const updatePlayerTeam = async (
  sessionId: string,
  userId: string,
  newTeam: 0 | 1
): Promise<void> => {
  try {
    const { error } = await supabase
      .from("game_players")
      .update({ team: newTeam })
      .eq("session_id", sessionId)
      .eq("user_id", userId);

    if (error) {
      throw new Error(`Failed to update team: ${error.message}`);
    }

    console.log("✅ Player team updated successfully");
  } catch (error) {
    console.error("❌ Error updating player team:", error);
    throw error;
  }
};

/**
 * Subscribe to realtime updates for a game session
 */
export const subscribeToGameSession = (
  sessionId: string,
  onUpdate: (event: RealtimeGameEvent) => void
): RealtimeChannel => {
  console.log("🔔 Subscribing to game session updates:", sessionId);

  const channel = supabase
    .channel(`game_session_${sessionId}`)
    .on(
      "postgres_changes",
      {
        event: "*",
        schema: "public",
        table: "game_players",
        filter: `session_id=eq.${sessionId}`,
      },
      (payload) => {
        console.log("🔄 Game players update:", payload);
        onUpdate({
          type:
            payload.eventType === "INSERT"
              ? "player_joined"
              : payload.eventType === "DELETE"
              ? "player_left"
              : "team_changed",
          payload: payload.new || payload.old,
          timestamp: new Date().toISOString(),
          session_id: sessionId,
        });
      }
    )
    .on(
      "postgres_changes",
      {
        event: "UPDATE",
        schema: "public",
        table: "game_sessions",
        filter: `id=eq.${sessionId}`,
      },
      (payload) => {
        console.log("🔄 Game session update:", payload);
        if (payload.new?.status === "active") {
          onUpdate({
            type: "game_started",
            payload: payload.new,
            timestamp: new Date().toISOString(),
            session_id: sessionId,
          });
        }
      }
    )
    .subscribe();

  return channel;
};

/**
 * Start a private game (host only)
 */
export const startPrivateGame = async (
  sessionId: string,
  hostId: string
): Promise<void> => {
  try {
    console.log("🚀 Starting private game:", { sessionId, hostId });

    // Verify host permissions
    const session = await getSessionData(sessionId);
    if (session.host_id !== hostId) {
      throw new Error("Only the host can start the game");
    }

    // Check if we have exactly 4 players with 2 per team
    const players = session.players;
    if (players.length !== 4) {
      throw new Error("Need exactly 4 players to start");
    }

    const team0Count = players.filter((p) => p.team === 0).length;
    const team1Count = players.filter((p) => p.team === 1).length;

    if (team0Count !== 2 || team1Count !== 2) {
      throw new Error("Need 2 players per team to start");
    }

    // Update session status to active
    const { error } = await supabase
      .from("game_sessions")
      .update({
        status: "active",
        updated_at: new Date().toISOString(),
      })
      .eq("id", sessionId);

    if (error) {
      throw new Error(`Failed to start game: ${error.message}`);
    }

    console.log("✅ Private game started successfully");
  } catch (error) {
    console.error("❌ Error starting private game:", error);
    throw error;
  }
};

/**
 * Cleanup expired game sessions
 */
export const cleanupExpiredSessions = async (): Promise<void> => {
  try {
    console.log("🧹 Cleaning up expired game sessions...");

    const { data: expiredSessions, error } = await supabase
      .from("game_sessions")
      .select("id")
      .eq("is_private", true)
      .eq("status", "waiting")
      .lt("expires_at", new Date().toISOString());

    if (error) {
      console.error("❌ Error finding expired sessions:", error);
      return;
    }

    if (expiredSessions && expiredSessions.length > 0) {
      const sessionIds = expiredSessions.map((s) => s.id);

      // Delete players first (foreign key constraint)
      await supabase.from("game_players").delete().in("session_id", sessionIds);

      // Delete sessions
      await supabase.from("game_sessions").delete().in("id", sessionIds);

      console.log(`🗑️ Cleaned up ${expiredSessions.length} expired sessions`);
    }
  } catch (error) {
    console.error("❌ Error in cleanupExpiredSessions:", error);
  }
};

/**
 * Handle player disconnection
 */
export const handlePlayerDisconnection = async (
  sessionId: string,
  userId: string,
  substituteWithCPU: boolean = false
): Promise<void> => {
  try {
    console.log("🔌 Handling player disconnection:", {
      sessionId,
      userId,
      substituteWithCPU,
    });

    if (substituteWithCPU) {
      // Replace player with CPU
      const { error } = await supabase
        .from("game_players")
        .update({
          is_ai: true,
          ai_difficulty: "medium",
          connected: false,
          disconnected_at: new Date().toISOString(),
        })
        .eq("session_id", sessionId)
        .eq("user_id", userId);

      if (error) {
        throw new Error(`Failed to substitute with CPU: ${error.message}`);
      }

      console.log("🤖 Player substituted with CPU");
    } else {
      // Just mark as disconnected
      const { error } = await supabase
        .from("game_players")
        .update({
          connected: false,
          disconnected_at: new Date().toISOString(),
        })
        .eq("session_id", sessionId)
        .eq("user_id", userId);

      if (error) {
        throw new Error(
          `Failed to mark player as disconnected: ${error.message}`
        );
      }

      console.log("🔌 Player marked as disconnected");
    }
  } catch (error) {
    console.error("❌ Error handling player disconnection:", error);
    throw error;
  }
};

/**
 * Handle player reconnection
 */
export const handlePlayerReconnection = async (
  sessionId: string,
  userId: string
): Promise<void> => {
  try {
    console.log("🔌 Handling player reconnection:", { sessionId, userId });

    const { error } = await supabase
      .from("game_players")
      .update({
        connected: true,
        is_ai: false,
        ai_difficulty: null,
        disconnected_at: null,
        last_seen: new Date().toISOString(),
      })
      .eq("session_id", sessionId)
      .eq("user_id", userId);

    if (error) {
      throw new Error(`Failed to handle reconnection: ${error.message}`);
    }

    console.log("✅ Player reconnected successfully");
  } catch (error) {
    console.error("❌ Error handling player reconnection:", error);
    throw error;
  }
};

/**
 * Check for disconnected players and handle them
 */
export const checkDisconnectedPlayers = async (
  sessionId: string
): Promise<void> => {
  try {
    const session = await getSessionData(sessionId);
    const now = new Date();
    const disconnectionThreshold = 60000; // 1 minute

    for (const player of session.players) {
      if (player.connected && player.last_seen) {
        const lastSeenTime = new Date(player.last_seen).getTime();
        const timeSinceLastSeen = now.getTime() - lastSeenTime;

        if (timeSinceLastSeen > disconnectionThreshold) {
          console.log(`🔌 Player ${player.username} appears disconnected`);
          await handlePlayerDisconnection(sessionId, player.user_id, true); // Substitute with CPU
        }
      }
    }
  } catch (error) {
    console.error("❌ Error checking disconnected players:", error);
  }
};

/**
 * Get available private games
 */
export const getAvailablePrivateGames = async (): Promise<GameSession[]> => {
  try {
    console.log("🔍 Loading available private games...");

    // First get sessions without join to avoid RLS issues
    const { data: sessions, error } = await supabase
      .from("game_sessions")
      .select("*")
      .eq("is_private", true)
      .eq("status", "waiting")
      .gt("expires_at", new Date().toISOString())
      .order("created_at", { ascending: false });

    if (error) {
      console.error("❌ Error loading available games:", error);
      throw new Error(`Failed to load available games: ${error.message}`);
    }

    if (!sessions || sessions.length === 0) {
      console.log("✅ No available games found");
      return [];
    }

    // Get player counts for each session separately
    const availableGames: GameSession[] = [];

    for (const session of sessions) {
      try {
        const { data: players, error: playersError } = await supabase
          .from("game_players")
          .select("*")
          .eq("session_id", session.id);

        if (playersError) {
          console.warn(
            `⚠️ Could not load players for session ${session.id}:`,
            playersError
          );
          continue;
        }

        const playerCount = players?.length || 0;
        const maxPlayers = session.max_players || 4;

        // Only include sessions that aren't full
        if (playerCount < maxPlayers) {
          const gameSession: GameSession = {
            ...session,
            players: (players || []).map((p: any, index: number) => ({
              id: index.toString(),
              user_id: p.user_id,
              username: p.username,
              position: p.position,
              team: p.team,
              connected: p.connected,
              level: p.level,
              is_host: p.is_host,
              joined_at: p.joined_at,
            })),
          };

          availableGames.push(gameSession);
        }
      } catch (sessionError) {
        console.warn(
          `⚠️ Error processing session ${session.id}:`,
          sessionError
        );
        continue;
      }
    }

    console.log(`✅ Found ${availableGames.length} available games`);
    return availableGames;
  } catch (error) {
    console.error("❌ Error in getAvailablePrivateGames:", error);
    throw error;
  }
};

// Auto-cleanup every 5 minutes
setInterval(cleanupExpiredSessions, 5 * 60 * 1000);

// Check for disconnected players every 30 seconds
setInterval(() => {
  // This would need to be called for active sessions
  // Implementation depends on how you track active sessions
}, 30 * 1000);

// Placeholder functions for compatibility with existing code
export const createGameSession = createPrivateGameSession;
export const getAvailableGameSessions = async () => [];
export const joinGameSession = joinPrivateGameSession;
export const updateGameState = async () => {};
export const updateGameCompleted = async () => {};
export const updatePlayerConnection = async () => {};
export const recordGameResult = async () => {};
