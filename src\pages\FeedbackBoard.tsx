import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useHardwareBackButton } from "@/hooks/useHardwareBackButton";
import { useAuth } from "@/context/auth-context";
import { useCustomToast } from "@/hooks/useCustomToast";
import { useAudio } from "@/hooks/useAudio";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import {
  ArrowLeft,
  Plus,
  ThumbsUp,
  MessageSquare,
  TrendingUp,
  Lightbulb,
  Users,
  Clock,
  AlertCircle,
} from "lucide-react";

interface FeedbackItem {
  id: string;
  title: string;
  description: string;
  category: "feature" | "improvement" | "bug" | "other";
  votes: number;
  user_votes: boolean;
  author: string;
  created_at: string;
  status: "pending" | "in_progress" | "completed" | "rejected";
}

const FeedbackBoard = () => {
  const navigate = useNavigate();
  const { user, isLoggedIn, apiCallWithAuth } = useAuth();
  const { showToast } = useCustomToast();
  const { playSound } = useAudio();

  // 🚀 USA PATTERN FETCH DIRETTO CON AUTENTICAZIONE QUANDO NECESSARIO
  const apiCallWithFetch = useCallback(
    async (url: string, options: RequestInit = {}) => {
      try {
        console.log("🚀 FeedbackBoard: API call con fetch diretto...");

        const headers: Record<string, string> = {
          apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
          "Content-Type": "application/json",
          Prefer: "return=representation",
          ...options.headers,
        };

        // 🔥 CORREZIONE: Usa autenticazione per operazioni che richiedono utente loggato
        if (isLoggedIn && user) {
          try {
            // Usa apiCallWithAuth per operazioni autenticate
            console.log(
              "🔐 FeedbackBoard: Usando autenticazione per operazione protetta"
            );
            return await apiCallWithAuth(url, options);
          } catch (authError) {
            console.warn(
              "⚠️ Errore con autenticazione, fallback a chiave anonima:",
              authError
            );
            // Fallback a chiave anonima se l'autenticazione fallisce
          }
        }

        // Per operazioni di sola lettura o fallback, usa solo chiave anonima
        console.log("🚨 FeedbackBoard: Usando solo chiave anonima");

        const response = await fetch(url, {
          ...options,
          headers,
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;
      } catch (error) {
        console.warn("⚠️ FeedbackBoard: Errore API fetch:", error);
        throw error;
      }
    },
    [isLoggedIn, user, apiCallWithAuth]
  );

  const [feedbackItems, setFeedbackItems] = useState<FeedbackItem[]>([]);
  const [allFeedbackItems, setAllFeedbackItems] = useState<FeedbackItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newFeedback, setNewFeedback] = useState<{
    title: string;
    description: string;
    category: "feature" | "improvement" | "bug" | "other";
  }>({
    title: "",
    description: "",
    category: "feature",
  });
  const [sortBy, setSortBy] = useState<"votes" | "recent">("votes");

  useHardwareBackButton("/account");

  // Effetto per ordinamento locale
  useEffect(() => {
    if (allFeedbackItems.length > 0) {
      const sorted = [...allFeedbackItems].sort((a, b) => {
        if (sortBy === "votes") {
          return b.votes - a.votes;
        } else {
          return (
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
        }
      });
      setFeedbackItems(sorted);
    }
  }, [sortBy, allFeedbackItems]);

  interface FeedbackVote {
    user_id: string;
  }

  interface FeedbackApiItem {
    id: string;
    title: string;
    description: string;
    category: "feature" | "improvement" | "bug" | "other";
    votes: number;
    author: string;
    created_at: string;
    status: "pending" | "in_progress" | "completed" | "rejected";
    user_votes?: FeedbackVote[];
  }

  const loadFeedbackItems = useCallback(async () => {
    try {
      setIsLoading(true);

      console.log("🔄 Caricamento feedback da Supabase...");
      console.log("🔍 FeedbackBoard: Stato utente:", {
        isLoggedIn,
        userId: user?.id,
      });

      // 🚀 USA FETCH DIRETTO CON SOLO CHIAVE ANONIMA PER LETTURA
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;

      // Per la lettura, usa sempre solo la chiave anonima
      const headers: Record<string, string> = {
        apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
        "Content-Type": "application/json",
      };

      // 🔥 CORREZIONE CRITICA: Carica feedback con conteggio voti accurato
      const response = await fetch(
        `${supabaseUrl}/rest/v1/feedback_requests?select=id,title,description,category,votes,author,created_at,status,user_votes:feedback_votes(user_id)&order=votes.desc`,
        {
          method: "GET",
          headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: FeedbackApiItem[] = await response.json();

      // 🔥 CORREZIONE CRITICA: Verifica e correggi i conteggi voti
      const processedData: FeedbackItem[] = [];

      for (const item of data || []) {
        // Ottieni il conteggio reale dei voti dal database
        const voteCountResponse = await fetch(
          `${supabaseUrl}/rest/v1/feedback_votes?feedback_id=eq.${item.id}&select=count`,
          {
            headers: {
              apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
              "Content-Type": "application/json",
            },
          }
        );

        let actualVoteCount = item.votes; // Fallback al valore esistente
        if (voteCountResponse.ok) {
          const voteCountData = await voteCountResponse.json();
          actualVoteCount = voteCountData.length || 0;

          // Se il conteggio è diverso, aggiorna il database
          if (actualVoteCount !== item.votes) {
            console.log(
              `🔄 Correggendo conteggio voti per ${item.title}: ${item.votes} -> ${actualVoteCount}`
            );
            try {
              await fetch(
                `${supabaseUrl}/rest/v1/feedback_requests?id=eq.${item.id}`,
                {
                  method: "PATCH",
                  headers: {
                    apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify({ votes: actualVoteCount }),
                }
              );
            } catch (updateError) {
              console.warn("Errore aggiornamento conteggio:", updateError);
            }
          }
        }

        processedData.push({
          ...item,
          votes: actualVoteCount, // Usa il conteggio corretto
          user_votes:
            isLoggedIn && user?.id
              ? item.user_votes?.some((vote) => vote.user_id === user.id) ||
                false
              : false,
        });
      }

      setAllFeedbackItems(processedData);
      setError(null); // Reset errore se caricamento ha successo
      console.log(`✅ Caricati ${processedData.length} feedback da Supabase`);
    } catch (error) {
      console.error("❌ Errore nel caricamento feedback:", error);

      // 🚨 NON MOSTRARE DATI MOCKATI - Chiedi di riavviare l'app
      showToast(
        "Impossibile caricare i feedback. Riavvia l'app per riprovare.",
        "error"
      );

      // Imposta stato di errore invece di dati mock
      setAllFeedbackItems([]);
      setError("Impossibile caricare i feedback. Riavvia l'app per riprovare.");
    } finally {
      setIsLoading(false);
    }
  }, [isLoggedIn, user?.id, showToast]);

  // Carica feedback items al mount (solo una volta)
  useEffect(() => {
    loadFeedbackItems();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Dipendenze vuote per caricare solo al mount

  const handleVote = async (
    feedbackId: string,
    currentVotes: number,
    hasVoted: boolean
  ) => {
    if (!isLoggedIn || !user) {
      showToast("Devi essere loggato per votare", "error");
      return;
    }

    // 🔥 CORREZIONE CRITICA: Aggiorna UI immediatamente per feedback istantaneo
    const optimisticVoteCount = hasVoted
      ? Math.max(0, currentVotes - 1)
      : currentVotes + 1;
    const optimisticUpdatedItems = allFeedbackItems.map((item) => {
      if (item.id === feedbackId) {
        return {
          ...item,
          votes: optimisticVoteCount,
          user_votes: !hasVoted,
        };
      }
      return item;
    });
    setAllFeedbackItems(optimisticUpdatedItems);

    try {
      playSound("buttonClick");
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;

      if (hasVoted) {
        // 🔥 CORREZIONE CRITICA: Rimuovi voto e ricalcola conteggio da database
        await apiCallWithAuth(
          `${supabaseUrl}/rest/v1/feedback_votes?feedback_id=eq.${feedbackId}&user_id=eq.${user.id}`,
          { method: "DELETE" }
        );

        // Ottieni il conteggio reale dal database
        const countResponse = await fetch(
          `${supabaseUrl}/rest/v1/feedback_votes?feedback_id=eq.${feedbackId}&select=count`,
          {
            headers: {
              apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
              "Content-Type": "application/json",
            },
          }
        );

        if (countResponse.ok) {
          const countData = await countResponse.json();
          const actualVoteCount = countData.length || 0;

          // Aggiorna il conteggio nella tabella feedback_requests
          await apiCallWithAuth(
            `${supabaseUrl}/rest/v1/feedback_requests?id=eq.${feedbackId}`,
            {
              method: "PATCH",
              body: JSON.stringify({ votes: actualVoteCount }),
            }
          );

          // Aggiorna UI con il conteggio reale
          const correctedItems = allFeedbackItems.map((item) => {
            if (item.id === feedbackId) {
              return {
                ...item,
                votes: actualVoteCount,
                user_votes: false,
              };
            }
            return item;
          });
          setAllFeedbackItems(correctedItems);
        }
      } else {
        // 🔥 CORREZIONE CRITICA: Aggiungi voto e ricalcola conteggio da database
        await apiCallWithAuth(`${supabaseUrl}/rest/v1/feedback_votes`, {
          method: "POST",
          body: JSON.stringify({ feedback_id: feedbackId, user_id: user.id }),
        });

        // Ottieni il conteggio reale dal database
        const countResponse = await fetch(
          `${supabaseUrl}/rest/v1/feedback_votes?feedback_id=eq.${feedbackId}&select=count`,
          {
            headers: {
              apikey: import.meta.env.VITE_SUPABASE_ANON_KEY,
              "Content-Type": "application/json",
            },
          }
        );

        if (countResponse.ok) {
          const countData = await countResponse.json();
          const actualVoteCount = countData.length || 0;

          // Aggiorna il conteggio nella tabella feedback_requests
          await apiCallWithAuth(
            `${supabaseUrl}/rest/v1/feedback_requests?id=eq.${feedbackId}`,
            {
              method: "PATCH",
              body: JSON.stringify({ votes: actualVoteCount }),
            }
          );

          // Aggiorna UI con il conteggio reale
          const correctedItems = allFeedbackItems.map((item) => {
            if (item.id === feedbackId) {
              return {
                ...item,
                votes: actualVoteCount,
                user_votes: true,
              };
            }
            return item;
          });
          setAllFeedbackItems(correctedItems);
        }
      }

      playSound("success");
    } catch (error) {
      console.error("Errore nel voto:", error);
      showToast("Errore nel voto", "error");

      // 🔥 CORREZIONE CRITICA: Ripristina stato precedente in caso di errore
      const revertedItems = allFeedbackItems.map((item) => {
        if (item.id === feedbackId) {
          return {
            ...item,
            votes: currentVotes, // Ripristina valore originale
            user_votes: hasVoted, // Ripristina stato originale
          };
        }
        return item;
      });
      setAllFeedbackItems(revertedItems);
    }
  };

  const handleSubmitFeedback = async () => {
    if (!isLoggedIn || !user) {
      showToast("Devi essere loggato per proporre funzionalità", "error");
      return;
    }

    if (!newFeedback.title.trim() || !newFeedback.description.trim()) {
      showToast("Compila tutti i campi", "error");
      return;
    }

    try {
      playSound("buttonClick");

      // 🚀 USA AUTENTICAZIONE PER INSERIMENTO FEEDBACK
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const data = await apiCallWithAuth(
        `${supabaseUrl}/rest/v1/feedback_requests`,
        {
          method: "POST",
          headers: {
            Prefer: "return=representation",
          },
          body: JSON.stringify({
            title: newFeedback.title.trim(),
            description: newFeedback.description.trim(),
            category: newFeedback.category,
            author: user.email || user.username || "Utente anonimo",
            votes: 0,
            status: "pending",
          }),
        }
      );

      // Aggiungi il nuovo feedback alla lista locale
      const newFeedbackItem: FeedbackItem = {
        ...data,
        user_votes: false,
      };

      setAllFeedbackItems((prev) => [newFeedbackItem, ...prev]);
      setNewFeedback({ title: "", description: "", category: "feature" });
      setShowAddForm(false);
      playSound("success");
      showToast("Feedback inviato con successo!", "success");
    } catch (error) {
      console.error("Errore nell'invio feedback:", error);
      showToast("Errore nell'invio del feedback", "error");
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "feature":
        return <Lightbulb className="w-4 h-4" />;
      case "improvement":
        return <TrendingUp className="w-4 h-4" />;
      case "bug":
        return <MessageSquare className="w-4 h-4" />;
      default:
        return <Users className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "feature":
        return "bg-blue-100 text-blue-800";
      case "improvement":
        return "bg-green-100 text-green-800";
      case "bug":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "in_progress":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50/80 to-orange-100/60">
      {/* Header compatto e coerente */}
      <div
        className="py-1.5 px-3 md:max-w-4xl md:mx-auto flex items-center gap-3 sticky top-0 z-30 border-b border-amber-300/80 shadow-lg bg-white/90"
        style={{
          paddingTop: `calc(0.25rem + env(safe-area-inset-top, 0px))`,
          minHeight: 0,
          background: "linear-gradient(90deg, #fffbe8ee 0%, #ffe3b8ee 100%)",
          boxShadow: "0 2px 12px 0 #eab30822, 0 1.5px 0 #eab30833",
        }}
      >
        <Button
          onClick={() => {
            playSound("buttonClick");
            navigate("/account");
          }}
          variant="ghost"
          size="icon"
          className="p-2 bg-romagna-wood/40 hover:bg-romagna-wood/60 rounded-full border border-romagna-wood/40 shadow-md backdrop-blur-sm transition-all duration-150"
        >
          <ArrowLeft className="h-5 w-5 text-romagna-darkWood" />
        </Button>
        <div className="flex items-center gap-2">
          <div className="flex items-center justify-center bg-amber-200/80 rounded-lg shadow-inner p-1">
            <MessageSquare className="h-7 w-7 text-amber-600 drop-shadow-md" />
          </div>
          <span
            className="text-xl md:text-2xl text-amber-900 font-serif font-bold drop-shadow-sm"
            style={{
              fontFamily: "'DynaPuff', cursive",
              fontWeight: 600,
              lineHeight: 1.1,
              letterSpacing: "-0.5px",
            }}
          >
            Bacheca Feedback
          </span>
        </div>
        <div className="flex-1" />
      </div>

      {/* Content */}
      <div className="p-4 max-w-4xl mx-auto ios-safe-bottom">
        {/* Authentication Status */}
        {(!isLoggedIn || !user) && (
          <div className="mb-4 p-3 bg-amber-100 border border-amber-300 rounded-lg">
            <p className="text-sm text-amber-800 text-center">
              <span className="font-medium">💡 Suggerimento:</span> Accedi per
              votare e proporre nuove funzionalità!
            </p>
          </div>
        )}

        {/* Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex gap-2">
            <Button
              onClick={() => setSortBy("votes")}
              variant={sortBy === "votes" ? "default" : "outline"}
              size="sm"
              className="flex items-center gap-2"
            >
              <TrendingUp className="w-4 h-4" />
              Più Votati
            </Button>
            <Button
              onClick={() => setSortBy("recent")}
              variant={sortBy === "recent" ? "default" : "outline"}
              size="sm"
              className="flex items-center gap-2"
            >
              <Clock className="w-4 h-4" />
              Recenti
            </Button>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={() => {
                if (!isLoggedIn || !user) {
                  showToast(
                    "Devi essere loggato per proporre funzionalità",
                    "error"
                  );
                  return;
                }
                playSound("buttonClick");
                setShowAddForm(!showAddForm);
              }}
              className={`flex items-center gap-2 ${
                isLoggedIn && user
                  ? "bg-amber-600 hover:bg-amber-700"
                  : "bg-gray-400 cursor-not-allowed"
              }`}
              disabled={!isLoggedIn || !user}
            >
              <Plus className="w-4 h-4" />
              {isLoggedIn && user
                ? "Proponi Funzionalità"
                : "Accedi per Proporre"}
            </Button>
          </div>
        </div>

        {/* Add Form */}
        {showAddForm && isLoggedIn && user && (
          <Card className="mb-6 border-amber-200">
            <CardHeader>
              <CardTitle className="text-amber-800">Nuova Proposta</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                placeholder="Titolo della proposta..."
                value={newFeedback.title}
                onChange={(e) =>
                  setNewFeedback((prev) => ({ ...prev, title: e.target.value }))
                }
                maxLength={100}
              />

              <Textarea
                placeholder="Descrivi la tua idea in dettaglio..."
                value={newFeedback.description}
                onChange={(e) =>
                  setNewFeedback((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                rows={4}
                maxLength={500}
              />

              <select
                value={newFeedback.category}
                onChange={(e) =>
                  setNewFeedback((prev) => ({
                    ...prev,
                    category: e.target.value as
                      | "feature"
                      | "improvement"
                      | "bug"
                      | "other",
                  }))
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="feature">Nuova Funzionalità</option>
                <option value="improvement">Miglioramento</option>
                <option value="bug">Segnalazione Bug</option>
                <option value="other">Altro</option>
              </select>

              <div className="flex gap-2">
                <Button
                  onClick={handleSubmitFeedback}
                  className="bg-amber-600 hover:bg-amber-700"
                >
                  Invia Proposta
                </Button>
                <Button onClick={() => setShowAddForm(false)} variant="outline">
                  Annulla
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Feedback Items */}
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto"></div>
            <p className="mt-2 text-amber-700">Caricamento feedback...</p>
          </div>
        ) : (
          <div className="space-y-4">
            {feedbackItems.map((item) => (
              <Card
                key={item.id}
                className="border-amber-200 hover:shadow-md transition-shadow"
              >
                <CardContent className="p-4">
                  <div className="flex flex-col gap-3">
                    {/* Header con badges */}
                    <div className="flex items-center gap-2 flex-wrap">
                      <Badge className={getCategoryColor(item.category)}>
                        {getCategoryIcon(item.category)}
                        <span className="ml-1 capitalize">{item.category}</span>
                      </Badge>
                      <Badge className={getStatusColor(item.status)}>
                        {item.status === "pending" && "In Attesa"}
                        {item.status === "in_progress" && "In Sviluppo"}
                        {item.status === "completed" && "Completato"}
                        {item.status === "rejected" && "Rifiutato"}
                      </Badge>
                    </div>

                    {/* Contenuto principale */}
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-2">
                        {item.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-3">
                        {item.description}
                      </p>
                    </div>

                    {/* Footer con info e voto */}
                    <div className="flex items-center justify-between gap-4">
                      <div className="flex items-center gap-4 text-xs text-gray-500 flex-1 min-w-0">
                        <span className="truncate">da {item.author}</span>
                        <span className="whitespace-nowrap">
                          {new Date(item.created_at).toLocaleDateString(
                            "it-IT"
                          )}
                        </span>
                      </div>

                      <Button
                        onClick={() => {
                          if (!isLoggedIn || !user) {
                            showToast(
                              "Devi essere loggato per votare",
                              "error"
                            );
                            return;
                          }
                          handleVote(item.id, item.votes, item.user_votes);
                        }}
                        variant={item.user_votes ? "default" : "outline"}
                        size="sm"
                        className={`flex items-center gap-2 shrink-0 ${
                          item.user_votes
                            ? "bg-amber-600 hover:bg-amber-700 text-white"
                            : !isLoggedIn || !user
                            ? "opacity-50 cursor-not-allowed"
                            : "hover:bg-amber-50"
                        }`}
                        disabled={!isLoggedIn || !user}
                        title={!isLoggedIn || !user ? "Accedi per votare" : ""}
                      >
                        <ThumbsUp className="w-4 h-4" />
                        <span className="min-w-[1rem] text-center">
                          {item.votes}
                        </span>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {feedbackItems.length === 0 && !isLoading && (
              <div className="text-center py-12">
                {error ? (
                  <>
                    <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Errore di Caricamento
                    </h3>
                    <p className="text-gray-600 mb-4">{error}</p>
                    <Button
                      onClick={() => window.location.reload()}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      Riavvia App
                    </Button>
                  </>
                ) : (
                  <>
                    <Lightbulb className="w-16 h-16 text-amber-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Nessun feedback ancora
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Sii il primo a proporre una nuova funzionalità!
                    </p>
                    <Button
                      onClick={() => {
                        if (!isLoggedIn || !user) {
                          showToast(
                            "Devi essere loggato per proporre funzionalità",
                            "error"
                          );
                          return;
                        }
                        setShowAddForm(true);
                      }}
                      className={`${
                        isLoggedIn && user
                          ? "bg-amber-600 hover:bg-amber-700"
                          : "bg-gray-400 hover:bg-gray-500 cursor-not-allowed"
                      }`}
                      disabled={!isLoggedIn || !user}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      {isLoggedIn && user
                        ? "Proponi Funzionalità"
                        : "Accedi per Proporre"}
                    </Button>
                  </>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FeedbackBoard;
