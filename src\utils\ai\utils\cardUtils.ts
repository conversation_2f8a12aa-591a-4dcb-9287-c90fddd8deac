import { Suit, Rank, Card, createDeck } from "../../game/cardUtils";
import { GameState } from "../../game/gameLogic";
import { CardMemory } from "../types";
import { analyzeCardMemory } from "../memory/analyzer";

export const createFullDeck = (): Card[] => {
  return createDeck();
};

export const getCardOrder = (card: Card): number => {
  const rankOrder: Record<string, number> = {
    "3": 10, // Highest
    "2": 9,
    A: 8,
    K: 7,
    H: 6,
    J: 5,
    "7": 4,
    "6": 3,
    "5": 2,
    "4": 1, // Lowest
  };

  return rankOrder[(card.rank as string) || ""] || 0;
};

export const getCardValue = (card: Card): number => {
  switch (card.rank) {
    case Rank.Ace:
      return 1.0; // Asso vale 1 punto
    case Rank.Three:
    case Rank.Two:
    case Rank.King:
    case Rank.Horse:
    case Rank.Jack:
      return 0.3; // Figure valgono 0.3 punti
    default:
      return 0; // Carte dal 4 al 7 non valgono nulla
  }
};

// NUOVE FUNZIONI PER GESTIONE STRATEGICA DELLE CARTE DI VALORE

/**
 * Verifica se una carta è adatta per dare punti al compagno
 * Esclude 2 e 3 che sono carte strategiche da conservare
 */
export const isGoodForTeammate = (card: Card): boolean => {
  return (
    card.rank === Rank.Ace || // Asso: ottimo per il compagno (1 punto)
    card.rank === Rank.King || // Re: buono per il compagno (0.3 punti)
    card.rank === Rank.Horse || // Cavallo: buono per il compagno (0.3 punti)
    card.rank === Rank.Jack // Fante: buono per il compagno (0.3 punti)
  );
};

/**
 * Verifica se una carta è strategica e da conservare
 * Solo i 2 sono sempre strategici da conservare
 * I 3 non di briscola sono per prendere punti!
 */
export const isStrategicCard = (card: Card): boolean => {
  return card.rank === Rank.Two; // Solo i 2 sono strategici da conservare
};

/**
 * Verifica se un 3 dovrebbe essere usato per controllo e punti
 * I 3 non di briscola sono ottimi per aprire e prendere
 */
export const isThreeForOpening = (
  card: Card,
  trumpSuit: Suit | null = null
): boolean => {
  return card.rank === Rank.Three && card.suit !== trumpSuit;
};

/**
 * Filtra le carte che sono buone per dare punti al compagno
 * Esclude automaticamente le carte strategiche (2, 3)
 */
export const getTeammateCards = (cards: Card[]): Card[] => {
  return cards.filter(isGoodForTeammate);
};

/**
 * Filtra le carte strategiche che dovrebbero essere conservate
 */
export const getStrategicCards = (cards: Card[]): Card[] => {
  return cards.filter(isStrategicCard);
};

/**
 * Verifica se una carta del compagno è praticamente imbattibile
 * considerando le carte già giocate e quelle ancora in gioco
 */
export const isTeammateCardUnbeatable = (
  teammateCard: Card,
  memory: CardMemory,
  state: GameState
): boolean => {
  const trumpSuit = state.trumpSuit;
  const leadSuit = state.leadSuit;

  // Se il compagno ha giocato il 3 di briscola, è sempre imbattibile
  if (teammateCard.suit === trumpSuit && teammateCard.rank === Rank.Three) {
    return true;
  }

  // Se il compagno ha giocato una briscola
  if (teammateCard.suit === trumpSuit) {
    // Controlla se ci sono briscole più forti non ancora giocate
    const strongerTrumpRanks = [];
    if (teammateCard.rank !== Rank.Three) strongerTrumpRanks.push(Rank.Three);
    if (teammateCard.rank !== Rank.Two && teammateCard.rank !== Rank.Three)
      strongerTrumpRanks.push(Rank.Two);
    if (
      teammateCard.rank !== Rank.Ace &&
      teammateCard.rank !== Rank.Two &&
      teammateCard.rank !== Rank.Three
    )
      strongerTrumpRanks.push(Rank.Ace);

    // Controlla se tutte le briscole più forti sono già state giocate
    const playedTrumps = memory.playedBySuit[trumpSuit] || [];
    const allStrongerTrumpsPlayed = strongerTrumpRanks.every((rank) =>
      playedTrumps.some((card) => card.rank === rank)
    );

    return allStrongerTrumpsPlayed;
  }

  // Se il compagno ha giocato una carta del seme di uscita
  if (leadSuit && teammateCard.suit === leadSuit) {
    // Controlla se tutte le carte più forti del seme sono già state giocate
    const strongerLeadRanks = [];
    const cardOrder = getCardOrder(teammateCard);

    // Identifica tutti i rank più forti
    const allRanks = [
      Rank.Three,
      Rank.Two,
      Rank.Ace,
      Rank.King,
      Rank.Horse,
      Rank.Jack,
      "7",
      "6",
      "5",
      "4",
    ];
    for (const rank of allRanks) {
      const rankOrder = getCardOrder({ rank, suit: teammateCard.suit } as Card);
      if (rankOrder > cardOrder) {
        strongerLeadRanks.push(rank);
      }
    }

    // Controlla se tutte le carte più forti del seme sono già state giocate
    const playedOfSuit = memory.playedBySuit[leadSuit] || [];
    const allStrongerOfSuitPlayed = strongerLeadRanks.every((rank) =>
      playedOfSuit.some((card) => card.rank === rank)
    );

    // Se non ci sono più briscole in gioco che possano superarla
    const trumpsInGame = memory.playedBySuit[trumpSuit] || [];
    const allTrumpsPlayed = trumpsInGame.length >= 10; // Tutte le 10 briscole giocate

    return allStrongerOfSuitPlayed && allTrumpsPlayed;
  }

  return false;
};

export const getCardStrengthScore = (card: Card): number => {
  const strengthOrder: Record<string, number> = {
    "3": 10, // Più forte
    "2": 9,
    A: 8,
    K: 7,
    H: 6,
    J: 5,
    "7": 4,
    "6": 3,
    "5": 2,
    "4": 1, // Più debole
  };

  return strengthOrder[card.rank as string] || 0;
};

export const countCardsBySuit = (hand: Card[]): Record<string, number> => {
  const counts: Record<string, number> = {};
  [Suit.Coins, Suit.Cups, Suit.Swords, Suit.Clubs].forEach((suit) => {
    counts[suit] = hand.filter((card) => card.suit === suit).length;
  });
  return counts;
};

export const findMaraffa = (hand: Card[]): Suit | null => {
  const suits = [Suit.Coins, Suit.Cups, Suit.Swords, Suit.Clubs];

  for (const suit of suits) {
    const suitCards = hand.filter((card) => card.suit === suit);
    const hasAce = suitCards.some((card) => card.rank === "A");
    const hasTwo = suitCards.some((card) => card.rank === "2");
    const hasThree = suitCards.some((card) => card.rank === "3");

    if (hasAce && hasTwo && hasThree) {
      // 🚨 RIMOSSO LOG AUTOMATICO: Stamperà il log solo quando necessario nel chiamante
      return suit;
    }
  }

  // 🚨 RIMOSSO LOG AUTOMATICO: Non stampa più "Nessuna Maraffa" automaticamente
  return null;
};

export const chooseSuitWithHighestCards = (hand: Card[]): Suit => {
  const suitScores: Record<string, number> = {};

  [Suit.Coins, Suit.Cups, Suit.Swords, Suit.Clubs].forEach((suit) => {
    const suitCards = hand.filter((card) => card.suit === suit);
    suitScores[suit] = suitCards.reduce((score, card) => {
      return score + getCardStrengthScore(card);
    }, 0);
  });

  return Object.entries(suitScores).reduce(
    (best, [suit, score]) => (score > suitScores[best] ? suit : best),
    Suit.Coins
  ) as Suit;
};

// REGOLA 4: Funzione per verificare se una carta può vincere la presa corrente
export const canCardWinTrick = (
  card: Card,
  currentTrick: Card[],
  trumpSuit: Suit | null,
  leadSuit: Suit | null
): boolean => {
  if (!currentTrick || currentTrick.length === 0) {
    return true; // Se è il primo a giocare, può sempre "vincere"
  }

  const isTrump = card.suit === trumpSuit;
  let canWin = true;

  for (const trickCard of currentTrick) {
    const trickIsTrump = trickCard.suit === trumpSuit;

    if (isTrump && !trickIsTrump) {
      // La nostra carta è briscola, quella in presa no: vince sempre
      continue;
    } else if (!isTrump && trickIsTrump) {
      // La nostra carta non è briscola, quella in presa sì: non può vincere
      canWin = false;
      break;
    } else if (isTrump && trickIsTrump) {
      // Entrambe briscole: confronta gli ordini
      if (getCardOrder(card) <= getCardOrder(trickCard)) {
        canWin = false;
        break;
      }
    } else {
      // Nessuna delle due è briscola
      if (card.suit === leadSuit && trickCard.suit === leadSuit) {
        // Entrambe del seme di uscita: confronta gli ordini
        if (getCardOrder(card) <= getCardOrder(trickCard)) {
          canWin = false;
          break;
        }
      } else if (card.suit !== leadSuit && trickCard.suit === leadSuit) {
        // La nostra non è del seme di uscita, quella in presa sì: non può vincere
        canWin = false;
        break;
      }
      // Se la nostra è del seme di uscita e quella in presa no, vince
    }
  }

  return canWin;
};

// REGOLA 4: Funzione per ottenere la carta di valore più basso che può seguire il seme
export const getLowestValueCardForSuit = (
  cards: Card[],
  requiredSuit: Suit
): Card | null => {
  const suitCards = cards.filter((card) => card.suit === requiredSuit);
  if (suitCards.length === 0) return null;

  // Prima preferenza: carte senza valore
  const noValueCards = suitCards.filter((card) => getCardValue(card) === 0);
  if (noValueCards.length > 0) {
    // Tra le carte senza valore, prendi la più forte
    return noValueCards.reduce((best, card) =>
      getCardStrengthScore(card) > getCardStrengthScore(best) ? card : best
    );
  }

  // Seconda preferenza: carta con valore più basso
  return suitCards.reduce((lowest, card) =>
    getCardValue(card) < getCardValue(lowest) ? card : lowest
  );
};

// REGOLA 4: Funzione per ottenere la carta di scarto migliore (senza valore)
export const getBestDiscardCard = (
  cards: Card[],
  trumpSuit: Suit | null
): Card | null => {
  // Prima preferenza: carte senza valore che non sono briscole
  const noValueNonTrumps = cards.filter(
    (card) => getCardValue(card) === 0 && card.suit !== trumpSuit
  );

  if (noValueNonTrumps.length > 0) {
    // Ritorna una carta casuale tra quelle senza valore
    return noValueNonTrumps[
      Math.floor(Math.random() * noValueNonTrumps.length)
    ];
  }

  // Seconda preferenza: carte senza valore (anche briscole)
  const noValueCards = cards.filter((card) => getCardValue(card) === 0);
  if (noValueCards.length > 0) {
    return noValueCards[Math.floor(Math.random() * noValueCards.length)];
  }

  // Ultima risorsa: carta con valore più basso
  return cards.reduce((lowest, card) =>
    getCardValue(card) < getCardValue(lowest) ? card : lowest
  );
};

// REGOLA 4 CRITICA: Determina se una carta può vincere la presa corrente
export const canWinCurrentTrick = (
  card: Card,
  currentTrick: Card[],
  leadSuit: Suit | null,
  trumpSuit: Suit | null
): boolean => {
  if (!currentTrick || currentTrick.length === 0) {
    return true; // Se non ci sono carte giocate, qualsiasi carta può "vincere"
  }

  // Controlla se la carta è una briscola
  const isCardTrump = trumpSuit && card.suit === trumpSuit;

  // Controlla se ci sono già briscole giocate
  const trumpsInTrick = currentTrick.filter(
    (trickCard) => trumpSuit && trickCard.suit === trumpSuit
  );

  if (isCardTrump) {
    // Se la nostra carta è briscola
    if (trumpsInTrick.length === 0) {
      // Non ci sono briscole in tavola: la nostra briscola vince sempre
      return true;
    } else {
      // Ci sono già briscole: dobbiamo battere la briscola più alta
      const highestTrump = trumpsInTrick.reduce((highest, trickCard) =>
        getCardOrder(trickCard) > getCardOrder(highest) ? trickCard : highest
      );
      return getCardOrder(card) > getCardOrder(highestTrump);
    }
  } else {
    // La nostra carta non è briscola
    if (trumpsInTrick.length > 0) {
      // Ci sono briscole in tavola: non possiamo vincere
      return false;
    }

    // Non ci sono briscole: controlliamo se possiamo battere il seme di uscita
    if (!leadSuit || card.suit !== leadSuit) {
      // Non seguiamo il seme di uscita: non possiamo vincere
      return false;
    }

    // Seguiamo il seme: dobbiamo battere la carta più alta del seme di uscita
    const leadSuitCards = currentTrick.filter(
      (trickCard) => leadSuit && trickCard.suit === leadSuit
    );

    if (leadSuitCards.length === 0) {
      return true; // Nessuna carta del seme di uscita: vinciamo
    }

    const highestLeadCard = leadSuitCards.reduce((highest, trickCard) =>
      getCardOrder(trickCard) > getCardOrder(highest) ? trickCard : highest
    );

    return getCardOrder(card) > getCardOrder(highestLeadCard);
  }
};

// REGOLA 4: Trova la carta di minor valore che può seguire le regole
// 🔥 AGGIORNATO: Nuovo ordine di scarto: 4,5,6,7,fante,cavallo,re,2,3,asso
export const findLowestValidCard = (
  cards: Card[],
  leadSuit: Suit | null,
  trumpSuit: Suit | null
): Card => {
  // Ordinamento per scarto: dal meno prezioso al più prezioso
  const getDiscardOrderScore = (card: Card): number => {
    const discardOrder: Record<string, number> = {
      "4": 1, // Primo da scartare
      "5": 2,
      "6": 3,
      "7": 4,
      J: 5, // Fante
      H: 6, // Cavallo
      K: 7, // Re
      "2": 8, // 2
      "3": 9, // 3
      A: 10, // Asso - ultimo da scartare!
    };

    return discardOrder[card.rank as string] || 0;
  };

  if (!leadSuit) {
    // Nessun seme di uscita: priorità alle carte non-briscola, poi ordine di scarto
    const nonTrumpCards = cards.filter((card) => card.suit !== trumpSuit);

    if (nonTrumpCards.length > 0) {
      nonTrumpCards.sort(
        (a, b) => getDiscardOrderScore(a) - getDiscardOrderScore(b)
      );
      console.log(
        `[FIND_LOWEST] ✅ Uso carta non-briscola da scartare: ${nonTrumpCards[0].rank} di ${nonTrumpCards[0].suit}`
      );
      return nonTrumpCards[0];
    }

    // Solo briscole: usa ordine di scarto
    const sortedCards = [...cards].sort(
      (a, b) => getDiscardOrderScore(a) - getDiscardOrderScore(b)
    );
    console.log(
      `[FIND_LOWEST] ✅ Uso briscola da scartare: ${sortedCards[0].rank} di ${sortedCards[0].suit}`
    );
    return sortedCards[0];
  }

  // Deve seguire il seme se possibile
  const leadSuitCards = cards.filter((card) => card.suit === leadSuit);

  if (leadSuitCards.length > 0) {
    // Ordina le carte del seme per ordine di scarto
    leadSuitCards.sort(
      (a, b) => getDiscardOrderScore(a) - getDiscardOrderScore(b)
    );
    console.log(
      `[FIND_LOWEST] ✅ Seguo seme con carta da scartare: ${leadSuitCards[0].rank} di ${leadSuitCards[0].suit}`
    );
    return leadSuitCards[0];
  }

  // Non ha il seme: priorità alle carte non-briscola, poi ordine di scarto
  const nonTrumpCards = cards.filter((card) => card.suit !== trumpSuit);

  if (nonTrumpCards.length > 0) {
    nonTrumpCards.sort(
      (a, b) => getDiscardOrderScore(a) - getDiscardOrderScore(b)
    );
    console.log(
      `[FIND_LOWEST] ✅ Non ho seme, uso carta non-briscola: ${nonTrumpCards[0].rank} di ${nonTrumpCards[0].suit}`
    );
    return nonTrumpCards[0];
  }

  // Solo briscole rimaste: usa ordine di scarto
  const sortedCards = [...cards].sort(
    (a, b) => getDiscardOrderScore(a) - getDiscardOrderScore(b)
  );
  console.log(
    `[FIND_LOWEST] ✅ Solo briscole, scarto: ${sortedCards[0].rank} di ${sortedCards[0].suit}`
  );
  return sortedCards[0];
};

/**
 * Inizializza la memoria delle carte con un mazzo completo
 */
export const initializeCardMemory = (): CardMemory => {
  const fullDeck = createFullDeck();
  const suitDistribution: Record<string, number> = {};
  const playedBySuit: Record<string, Card[]> = {};
  const highCardsRemaining: Record<string, Card[]> = {};

  // Inizializza contatori per ogni seme
  [Suit.Coins, Suit.Cups, Suit.Swords, Suit.Clubs].forEach((suit) => {
    suitDistribution[suit] = 10; // 10 carte per seme
    playedBySuit[suit] = [];

    // Identifica le carte alte per ogni seme (3, 2, A)
    highCardsRemaining[suit] = fullDeck.filter(
      (card) =>
        card.suit === suit &&
        [Rank.Three, Rank.Two, Rank.Ace].includes(card.rank)
    );
  });

  return {
    playedCards: [],
    playedBySuit,
    playedByPlayer: {},
    remainingCards: [...fullDeck],
    suitDistribution,
    trumpsRemaining: [],
    highCardsRemaining,
  };
};

/**
 * Aggiorna la memoria quando una carta viene giocata
 */
export const updateCardMemory = (
  memory: CardMemory,
  playedCard: Card,
  playerIndex: number
): CardMemory => {
  const newMemory = { ...memory };

  // Aggiungi alla lista delle carte giocate
  newMemory.playedCards = [...memory.playedCards, playedCard];

  // Aggiorna carte giocate per seme
  if (!newMemory.playedBySuit[playedCard.suit]) {
    newMemory.playedBySuit[playedCard.suit] = [];
  }
  newMemory.playedBySuit[playedCard.suit] = [
    ...newMemory.playedBySuit[playedCard.suit],
    playedCard,
  ];

  // Aggiorna carte giocate per giocatore
  if (!newMemory.playedByPlayer[playerIndex]) {
    newMemory.playedByPlayer[playerIndex] = [];
  }
  newMemory.playedByPlayer[playerIndex] = [
    ...newMemory.playedByPlayer[playerIndex],
    playedCard,
  ];

  // Rimuovi dalle carte rimanenti
  newMemory.remainingCards = memory.remainingCards.filter(
    (card) => card.id !== playedCard.id
  );

  // Aggiorna distribuzione per seme
  newMemory.suitDistribution = { ...memory.suitDistribution };
  newMemory.suitDistribution[playedCard.suit] =
    (newMemory.suitDistribution[playedCard.suit] || 0) - 1;

  // Rimuovi dalle carte alte rimanenti
  newMemory.highCardsRemaining = { ...memory.highCardsRemaining };
  if (newMemory.highCardsRemaining[playedCard.suit]) {
    newMemory.highCardsRemaining[playedCard.suit] =
      newMemory.highCardsRemaining[playedCard.suit].filter(
        (card) => card.id !== playedCard.id
      );
  }

  return newMemory;
};

/**
 * Aggiorna le briscole rimanenti nella memoria
 */
export const updateTrumpsRemaining = (
  memory: CardMemory,
  trumpSuit: Suit | null
): CardMemory => {
  if (!trumpSuit) return memory;

  const trumpsRemaining = memory.remainingCards.filter(
    (card) => card.suit === trumpSuit
  );

  return {
    ...memory,
    trumpsRemaining,
  };
};

/**
 * Verifica se una carta è "sicura" (non può essere superata da altre carte ancora in gioco)
 */
export const isCardSafe = (
  card: Card,
  memory: CardMemory,
  trumpSuit: Suit | null,
  leadSuit: Suit | null
): boolean => {
  // Se la carta è una briscola
  if (trumpSuit && card.suit === trumpSuit) {
    return isCardSafeInTrump(card, memory, trumpSuit);
  }

  // Se c'è un seme di uscita e la carta non è di quel seme
  if (leadSuit && card.suit !== leadSuit) {
    // La carta può essere superata da qualsiasi briscola rimanente
    return memory.trumpsRemaining.length === 0;
  }

  // Se la carta è del seme di uscita o non c'è seme di uscita
  return isCardSafeInSuit(card, memory, trumpSuit);
};

/**
 * Verifica se una briscola è sicura
 */
export const isCardSafeInTrump = (
  card: Card,
  memory: CardMemory,
  trumpSuit: Suit
): boolean => {
  const trumpsRemaining = memory.trumpsRemaining;
  const cardOrder = getCardOrder(card);

  // Controlla se ci sono briscole più forti ancora in gioco
  const strongerTrumpsRemaining = trumpsRemaining.filter(
    (trump) => getCardOrder(trump) > cardOrder
  );

  return strongerTrumpsRemaining.length === 0;
};

/**
 * Verifica se una carta è sicura nel suo seme
 */
export const isCardSafeInSuit = (
  card: Card,
  memory: CardMemory,
  trumpSuit: Suit | null
): boolean => {
  const suitCards = memory.remainingCards.filter((c) => c.suit === card.suit);
  const cardOrder = getCardOrder(card);

  // Controlla se ci sono carte più forti del stesso seme ancora in gioco
  const strongerSuitCards = suitCards.filter(
    (c) => getCardOrder(c) > cardOrder
  );

  // Se ci sono carte più forti del seme, non è sicura
  if (strongerSuitCards.length > 0) {
    return false;
  }

  // Se ci sono briscole in gioco e questa carta non è una briscola, non è sicura
  if (trumpSuit && card.suit !== trumpSuit) {
    return memory.trumpsRemaining.length === 0;
  }

  return true;
};

/**
 * Trova tutte le carte sicure nella mano del giocatore
 */
export const getSafeCards = (
  hand: Card[],
  memory: CardMemory,
  trumpSuit: Suit | null,
  leadSuit: Suit | null
): Card[] => {
  return hand.filter((card) => isCardSafe(card, memory, trumpSuit, leadSuit));
};

/**
 * Trova la carta più alta sicura per un determinato seme
 */
export const getHighestSafeCardInSuit = (
  hand: Card[],
  suit: Suit,
  memory: CardMemory,
  trumpSuit: Suit | null
): Card | null => {
  const suitCards = hand.filter((card) => card.suit === suit);
  const safeCards = suitCards.filter((card) =>
    isCardSafeInSuit(card, memory, trumpSuit)
  );

  if (safeCards.length === 0) return null;

  return safeCards.reduce((highest, card) =>
    getCardOrder(card) > getCardOrder(highest) ? card : highest
  );
};

/**
 * Calcola il rischio di giocare una carta (probabilità di essere superata)
 */
export const calculateCardRisk = (
  card: Card,
  memory: CardMemory,
  trumpSuit: Suit | null,
  leadSuit: Suit | null
): number => {
  if (isCardSafe(card, memory, trumpSuit, leadSuit)) {
    return 0; // Nessun rischio se è sicura
  }
  const totalRemainingCards = memory.remainingCards.length;
  let dangerousCards = 0;

  // Conta le carte che possono superare questa carta
  if (trumpSuit && card.suit === trumpSuit) {
    // Se è una briscola, conta briscole più forti
    dangerousCards = memory.trumpsRemaining.filter(
      (trump) => getCardOrder(trump) > getCardOrder(card)
    ).length;
  } else {
    // Se non è una briscola
    const suitCards = memory.remainingCards.filter((c) => c.suit === card.suit);
    const strongerSuitCards = suitCards.filter(
      (c) => getCardOrder(c) > getCardOrder(card)
    );

    // Aggiungi tutte le briscole se la carta non è una briscola
    dangerousCards = strongerSuitCards.length;
    if (trumpSuit && card.suit !== trumpSuit) {
      dangerousCards += memory.trumpsRemaining.length;
    }
  }

  return totalRemainingCards > 0 ? dangerousCards / totalRemainingCards : 0;
};

/**
 * 🔥 MODALITÀ DIFFICILE: Sistema di valutazione perfetta delle carte basato sulla memoria
 */

/**
 * Calcola il valore strategico dinamico di una carta basato sulla memoria perfetta
 */
export const calculateDynamicCardValue = (
  card: Card,
  memory: CardMemory,
  gameState: GameState,
  isFirstToPlay: boolean
): {
  baseValue: number;
  safetyScore: number;
  winningPotential: number;
  strategicValue: number;
  overallScore: number;
  reasoning: string[];
} => {
  const reasoning: string[] = [];
  const baseValue = getCardValue(card);
  let safetyScore = 0;
  let winningPotential = 0;
  let strategicValue = 0;

  // 1. ANALISI SICUREZZA
  const isSafe = isCardSafe(
    card,
    memory,
    gameState.trumpSuit,
    gameState.leadSuit
  );
  const risk = calculateCardRisk(
    card,
    memory,
    gameState.trumpSuit,
    gameState.leadSuit
  );

  if (isSafe) {
    safetyScore = 10;
    reasoning.push(`Carta SICURA - non può essere superata`);
  } else {
    safetyScore = Math.max(0, 10 - risk * 10);
    reasoning.push(`Rischio ${(risk * 100).toFixed(1)}% di essere superata`);
  }

  // 2. ANALISI POTENZIALE VINCENTE
  if (gameState.trumpSuit && card.suit === gameState.trumpSuit) {
    // È una briscola
    const strongerTrumpsRemaining = memory.trumpsRemaining.filter(
      (trump) => getCardOrder(trump) > getCardOrder(card)
    ).length;

    winningPotential = Math.max(0, 10 - strongerTrumpsRemaining);
    reasoning.push(
      `Briscola con ${strongerTrumpsRemaining} briscole più forti rimaste`
    );
  } else {
    // Non è una briscola
    const sameRankCards = memory.remainingCards.filter(
      (c) => c.suit === card.suit && getCardOrder(c) > getCardOrder(card)
    ).length;

    const trumpsRemaining = memory.trumpsRemaining.length;

    if (trumpsRemaining === 0) {
      winningPotential = Math.max(0, 10 - sameRankCards);
      reasoning.push(
        `Nessuna briscola rimasta, ${sameRankCards} carte più forti del seme`
      );
    } else {
      winningPotential = Math.max(0, 5 - sameRankCards - trumpsRemaining * 0.5);
      reasoning.push(
        `${trumpsRemaining} briscole rimaste, ${sameRankCards} carte più forti del seme`
      );
    }
  }

  // 3. ANALISI VALORE STRATEGICO
  if (isStrategicCard(card)) {
    // 2 e 3 sono sempre strategici
    const gamePhase =
      gameState.trickNumber <= 3
        ? "early"
        : gameState.trickNumber <= 7
        ? "mid"
        : "late";

    if (gamePhase === "early") {
      strategicValue = 8; // Alto valore strategico
      reasoning.push(
        `Carta strategica (${card.rank}) - conserva per late game`
      );
    } else if (gamePhase === "mid") {
      strategicValue = 6;
      reasoning.push(`Carta strategica (${card.rank}) - valuta attentamente`);
    } else {
      strategicValue = 4;
      reasoning.push(
        `Carta strategica (${card.rank}) - late game, usa se necessario`
      );
    }
  } else if (isGoodForTeammate(card)) {
    // Asso, Re, Cavallo, Fante
    strategicValue = baseValue * 2; // Raddoppia il valore base
    reasoning.push(`Carta buona per supportare il compagno`);
  }

  // 4. ANALISI CONTESTO DI GIOCO
  let contextBonus = 0;

  if (isFirstToPlay) {
    // Se giochi per primo, carte sicure valgono di più
    if (isSafe && baseValue > 0) {
      contextBonus = 3;
      reasoning.push(`Bonus per giocare carta sicura con punti per primo`);
    }
  } else {
    // Se segui, considera il valore della presa
    const currentTrickValue =
      gameState.currentTrick?.reduce((sum, c) => sum + getCardValue(c), 0) || 0;

    if (currentTrickValue >= 2 && winningPotential >= 7) {
      contextBonus = 5;
      reasoning.push(
        `Presa preziosa (${currentTrickValue} punti) e alta probabilità di vincita`
      );
    }
  }

  // 5. CALCOLO PUNTEGGIO FINALE
  const overallScore =
    (baseValue * 2 + // Valore base delle carte
      safetyScore * 0.8 + // Sicurezza
      winningPotential * 0.6 + // Potenziale vincente
      strategicValue * 0.7 + // Valore strategico
      contextBonus) / // Bonus contestuale
    5; // Normalizza su scala 0-10

  return {
    baseValue,
    safetyScore,
    winningPotential,
    strategicValue,
    overallScore,
    reasoning,
  };
};

/**
 * Valuta tutte le carte in mano e le ordina per valore strategico
 */
export const evaluateHandWithPerfectMemory = (
  hand: Card[],
  memory: CardMemory,
  gameState: GameState
): Array<{
  card: Card;
  evaluation: ReturnType<typeof calculateDynamicCardValue>;
  rank: number;
}> => {
  const isFirstToPlay = !gameState.leadSuit;

  const evaluations = hand.map((card) => ({
    card,
    evaluation: calculateDynamicCardValue(
      card,
      memory,
      gameState,
      isFirstToPlay
    ),
  }));

  // Ordina per punteggio complessivo (dal più alto al più basso)
  evaluations.sort(
    (a, b) => b.evaluation.overallScore - a.evaluation.overallScore
  );

  // Assegna rank
  return evaluations.map((item, index) => ({
    ...item,
    rank: index + 1,
  }));
};

/**
 * Trova la migliore carta da giocare basandosi sulla memoria perfetta
 */
export const selectBestCardWithPerfectMemory = (
  hand: Card[],
  memory: CardMemory,
  gameState: GameState,
  strategy: "conservative" | "aggressive" | "balanced" = "balanced"
): {
  selectedCard: Card;
  evaluation: ReturnType<typeof calculateDynamicCardValue>;
  alternatives: Array<{ card: Card; reason: string; score: number }>;
} => {
  const evaluatedHand = evaluateHandWithPerfectMemory(hand, memory, gameState);

  let selectedCard: Card;
  let selectedEvaluation: ReturnType<typeof calculateDynamicCardValue>;

  if (strategy === "conservative") {
    // Strategia conservativa: preferisci carte sicure anche se con meno punti
    const safestCards = evaluatedHand.filter(
      (item) => item.evaluation.safetyScore >= 8
    );

    if (safestCards.length > 0) {
      const bestSafe = safestCards[0];
      selectedCard = bestSafe.card;
      selectedEvaluation = bestSafe.evaluation;
    } else {
      // Se non ci sono carte sicure, prendi quella con minor rischio
      const leastRisky = evaluatedHand.reduce((prev, current) =>
        current.evaluation.safetyScore > prev.evaluation.safetyScore
          ? current
          : prev
      );
      selectedCard = leastRisky.card;
      selectedEvaluation = leastRisky.evaluation;
    }
  } else if (strategy === "aggressive") {
    // Strategia aggressiva: massimizza punti e potenziale vincente
    const aggressiveScore = (item: (typeof evaluatedHand)[0]) =>
      item.evaluation.baseValue * 3 +
      item.evaluation.winningPotential * 2 +
      item.evaluation.safetyScore;

    const bestAggressive = evaluatedHand.reduce((prev, current) =>
      aggressiveScore(current) > aggressiveScore(prev) ? current : prev
    );

    selectedCard = bestAggressive.card;
    selectedEvaluation = bestAggressive.evaluation;
  } else {
    // Strategia bilanciata: usa il punteggio complessivo
    selectedCard = evaluatedHand[0].card;
    selectedEvaluation = evaluatedHand[0].evaluation;
  }

  // Genera alternative per debugging
  const alternatives = evaluatedHand
    .filter((item) => item.card.id !== selectedCard.id)
    .slice(0, 3)
    .map((item) => ({
      card: item.card,
      reason: `Score: ${item.evaluation.overallScore.toFixed(1)} - ${
        item.evaluation.reasoning[0]
      }`,
      score: item.evaluation.overallScore,
    }));

  return {
    selectedCard,
    evaluation: selectedEvaluation,
    alternatives,
  };
};

/**
 * Analizza le debolezze degli avversari basandosi sulla memoria
 */
export const analyzeOpponentWeaknesses = (
  memory: CardMemory,
  gameState: GameState
): {
  playerAnalysis: Record<
    number,
    {
      likelyMissingSuits: string[];
      strongSuits: string[];
      playedHighCards: Card[];
      estimatedStrength: "weak" | "medium" | "strong";
    }
  >;
  teamAnalysis: {
    [key: number]: {
      combinedStrength: "weak" | "medium" | "strong";
      likelyStrategy: "defensive" | "aggressive" | "cooperative";
    };
  };
} => {
  const playerAnalysis: Record<
    number,
    {
      likelyMissingSuits: string[];
      strongSuits: string[];
      playedHighCards: Card[];
      estimatedStrength: "weak" | "medium" | "strong";
    }
  > = {};

  // Analizza ogni giocatore
  for (let playerIndex = 0; playerIndex < 4; playerIndex++) {
    const playedCards = memory.playedByPlayer[playerIndex] || [];
    const likelyMissingSuits: string[] = [];
    const strongSuits: string[] = [];
    const playedHighCards = playedCards.filter((card) =>
      ["3", "2", "A"].includes(card.rank as string)
    );

    // Rileva semi probabilmente mancanti
    Object.keys(memory.playedBySuit).forEach((suit) => {
      const playerCardsOfSuit = playedCards.filter(
        (card) => card.suit === suit
      );
      const totalCardsOfSuitPlayed = memory.playedBySuit[suit].length;

      if (playerCardsOfSuit.length === 0 && totalCardsOfSuitPlayed > 3) {
        likelyMissingSuits.push(suit);
      } else if (playerCardsOfSuit.length >= 3) {
        strongSuits.push(suit);
      }
    });

    // Stima forza del giocatore
    let estimatedStrength: "weak" | "medium" | "strong";
    if (playedHighCards.length >= 3) estimatedStrength = "strong";
    else if (playedHighCards.length >= 1) estimatedStrength = "medium";
    else estimatedStrength = "weak";

    playerAnalysis[playerIndex] = {
      likelyMissingSuits,
      strongSuits,
      playedHighCards,
      estimatedStrength,
    };
  }
  // Analizza i team
  const teamAnalysis: {
    [key: number]: {
      combinedStrength: "weak" | "medium" | "strong";
      likelyStrategy: "defensive" | "aggressive" | "cooperative";
    };
  } = {};
  for (let teamId = 0; teamId < 2; teamId++) {
    // CORRETTO: Team 0 = giocatori 0,2 (Sud,Nord) - Team 1 = giocatori 1,3 (Est,Ovest)
    const teamPlayers = teamId === 0 ? [0, 2] : [1, 3];
    const combinedHighCards = teamPlayers.reduce(
      (sum, playerId) =>
        sum + (playerAnalysis[playerId]?.playedHighCards.length || 0),
      0
    );

    let combinedStrength: "weak" | "medium" | "strong";
    if (combinedHighCards >= 4) combinedStrength = "strong";
    else if (combinedHighCards >= 2) combinedStrength = "medium";
    else combinedStrength = "weak";

    // Analizza strategia probabile del team
    let likelyStrategy: "defensive" | "aggressive" | "cooperative" =
      "cooperative";
    // Logica semplificata per ora
    if (combinedStrength === "strong") likelyStrategy = "aggressive";
    else if (combinedStrength === "weak") likelyStrategy = "defensive";

    teamAnalysis[teamId] = {
      combinedStrength,
      likelyStrategy,
    };
  }

  return { playerAnalysis, teamAnalysis };
};

/**
 * 🔥 SISTEMA UNIFICATO DI VALUTAZIONE ANTI-SPRECO
 * Combina tutti i controlli anti-spreco in una singola funzione ottimizzata
 */
interface CardWasteAnalysis {
  isWaste: boolean;
  reason: string;
  severity: "low" | "medium" | "high" | "critical";
  alternativesExist: boolean;
}

export const analyzeCardWaste = (
  card: Card,
  availableCards: Card[],
  currentTrick: Card[],
  state: GameState,
  playerIndex: number,
  memory: CardMemory | null = null
): CardWasteAnalysis => {
  const cardValue = getCardValue(card);

  // Le carte senza valore non sono mai spreco
  if (cardValue === 0) {
    return {
      isWaste: false,
      reason: "Carta senza valore - sempre sicura da giocare",
      severity: "low",
      alternativesExist: false,
    };
  }

  const trickValue = currentTrick.reduce(
    (sum, trickCard) => sum + getCardValue(trickCard),
    0
  );

  // 🔥 CONTROLLO 1: SPRECO AGLI AVVERSARI (CRITICO)
  if (card.rank === "3" || card.rank === "2" || card.rank === "A") {
    const canWin = canWinCurrentTrick(
      card,
      currentTrick,
      state.leadSuit,
      state.trumpSuit
    );

    let teammateIsWinning = false;
    if (currentTrick.length > 0 && state.leadPlayer !== undefined) {
      const winnerIndex = getCurrentTrickWinner(
        currentTrick,
        state.leadSuit,
        state.trumpSuit,
        state.leadPlayer
      );
      const winnerPlayerIndex = (state.leadPlayer + winnerIndex) % 4;
      const myTeam = state.players[playerIndex].team;
      const winnerTeam = state.players[winnerPlayerIndex].team;
      teammateIsWinning =
        winnerTeam === myTeam && winnerPlayerIndex !== playerIndex;
    }

    if (!canWin && !teammateIsWinning) {
      return {
        isWaste: true,
        reason: `CRITICO: ${card.rank} andrebbe agli avversari!`,
        severity: "critical",
        alternativesExist: availableCards.length > 1,
      };
    }
  }

  // 🔥 CONTROLLO 2: TAGLIO INTELLIGENTE
  if (state.trumpSuit && card.suit === state.trumpSuit) {
    const isStrategicTrump = card.rank === "3" || card.rank === "2";

    if (isStrategicTrump && state.leadSuit) {
      const leadSuitCards = availableCards.filter(
        (c) => c.suit === state.leadSuit
      );
      if (leadSuitCards.length === 0) {
        // È un taglio!
        const trumpCards = availableCards.filter(
          (c) => c.suit === state.trumpSuit
        );
        const weakerTrumps = trumpCards.filter(
          (trump) => trump.rank !== "3" && trump.rank !== "2"
        );

        if (weakerTrumps.length > 0) {
          const trickNumber = state.trickNumber || 1;
          const isLastTrick = trickNumber === 10;
          const hasAceOfTrumps = currentTrick.some(
            (c) => c.suit === state.trumpSuit && c.rank === "A"
          );

          if (!isLastTrick && !hasAceOfTrumps) {
            return {
              isWaste: true,
              reason: `TAGLIO: Ho ${weakerTrumps.length} briscole più deboli per tagliare`,
              severity: "high",
              alternativesExist: true,
            };
          }
        }
      }
    }
  }

  // 🔥 CONTROLLO 3: CARTE DOMINANTI
  if (memory && isCardDominantInSuit(card, memory, state)) {
    const canWin = canWinCurrentTrick(
      card,
      currentTrick,
      state.leadSuit,
      state.trumpSuit
    );

    if (!canWin) {
      // Verifica alternative
      let hasAlternatives = false;
      if (state.leadSuit && card.suit === state.leadSuit) {
        const leadSuitCards = availableCards.filter(
          (c) => c.suit === state.leadSuit
        );
        hasAlternatives =
          leadSuitCards.filter((c) => c.id !== card.id).length > 0;
      } else {
        hasAlternatives =
          availableCards.filter((c) => c.id !== card.id).length > 0;
      }

      if (hasAlternatives) {
        return {
          isWaste: true,
          reason: `DOMINANZA: Carta dominante sprecata, ho alternative`,
          severity: "high",
          alternativesExist: true,
        };
      }
    }
  }

  // 🔥 CONTROLLO 4: VALORE PRESA VS VALORE CARTA
  if (trickValue === 0) {
    return {
      isWaste: true,
      reason: `PRESA SENZA VALORE: Non do ${cardValue} punti per 0 punti`,
      severity: "medium",
      alternativesExist: availableCards.some((c) => getCardValue(c) === 0),
    };
  }

  // Soglie specifiche per tipo di carta
  if (card.rank === "A" && trickValue < 3) {
    return {
      isWaste: true,
      reason: `ASSO: Richiede almeno 3 punti, presa vale ${trickValue}`,
      severity: "high",
      alternativesExist: availableCards.some((c) => c.rank !== "A"),
    };
  }

  if (card.rank === "K" && trickValue < 2) {
    return {
      isWaste: true,
      reason: `RE: Richiede almeno 2 punti, presa vale ${trickValue}`,
      severity: "medium",
      alternativesExist: availableCards.some((c) => c.rank !== "K"),
    };
  }

  if (isStrategicCard(card)) {
    if (trickValue < 2 && state.trickNumber !== 10) {
      return {
        isWaste: true,
        reason: `STRATEGICA ${card.rank}: Richiede almeno 2 punti, presa vale ${trickValue}`,
        severity: "high",
        alternativesExist: availableCards.some((c) => !isStrategicCard(c)),
      };
    }
  }

  if (cardValue > 0 && trickValue < cardValue * 0.8) {
    return {
      isWaste: true,
      reason: `VALORE: Carta vale ${cardValue}, presa solo ${trickValue}`,
      severity: "low",
      alternativesExist: availableCards.some(
        (c) => getCardValue(c) < cardValue
      ),
    };
  }

  return {
    isWaste: false,
    reason: "Carta appropriata per questa situazione",
    severity: "low",
    alternativesExist: false,
  };
};

/**
 * 🔥 MIGLIORAMENTO: Trova la migliore carta di scarto per prese senza valore
 * Priorità ASSOLUTA: carte lisce (4,5,6,7) > carte basse dello stesso seme > carte di minor valore
 * Questa funzione è DRACONIANA nel prioritizzare carte senza valore!
 */
export const findBestDiscardForWorthlessTrick = (
  cards: Card[],
  leadSuit: Suit | null,
  trumpSuit: Suit | null
): Card => {
  console.log(
    `[BEST_DISCARD] 🗑️ Cerco carta di scarto ottimale tra ${cards.length} carte`
  );

  // PRIORITÀ MASSIMA: carte senza valore non-briscola di qualsiasi seme
  const noValueNonTrumps = cards.filter(
    (card) => getCardValue(card) === 0 && card.suit !== trumpSuit
  );

  if (noValueNonTrumps.length > 0) {
    // Se devo seguire il seme, priorità a carte senza valore del seme richiesto
    if (leadSuit) {
      const noValueLeadSuit = noValueNonTrumps.filter(
        (card) => card.suit === leadSuit
      );
      if (noValueLeadSuit.length > 0) {
        console.log(
          `[BEST_DISCARD] ✅ PERFETTO: carta senza valore del seme richiesto: ${noValueLeadSuit[0].rank} di ${noValueLeadSuit[0].suit}`
        );
        return noValueLeadSuit[0];
      }
    }

    // Altrimenti qualsiasi carta senza valore non-briscola va bene
    const bestNoValueNonTrump = noValueNonTrumps[0];
    console.log(
      `[BEST_DISCARD] ✅ OTTIMO: carta senza valore non-briscola: ${bestNoValueNonTrump.rank} di ${bestNoValueNonTrump.suit}`
    );
    return bestNoValueNonTrump;
  }

  // PRIORITÀ SECONDA: carte senza valore (anche briscole se necessario)
  const noValueCards = cards.filter((card) => getCardValue(card) === 0);
  if (noValueCards.length > 0) {
    // Se devo seguire il seme, priorità a carte senza valore del seme richiesto
    if (leadSuit) {
      const noValueLeadSuit = noValueCards.filter(
        (card) => card.suit === leadSuit
      );
      if (noValueLeadSuit.length > 0) {
        console.log(
          `[BEST_DISCARD] ✅ BUONO: carta senza valore del seme richiesto: ${noValueLeadSuit[0].rank} di ${noValueLeadSuit[0].suit}`
        );
        return noValueLeadSuit[0];
      }
    }

    const bestNoValue = noValueCards[0];
    console.log(
      `[BEST_DISCARD] ✅ ACCETTABILE: carta senza valore: ${bestNoValue.rank} di ${bestNoValue.suit}`
    );
    return bestNoValue;
  }

  // PRIORITÀ TERZA: se devo seguire il seme, carte di minor valore del seme richiesto
  if (leadSuit) {
    const leadSuitCards = cards.filter((card) => card.suit === leadSuit);
    if (leadSuitCards.length > 0) {
      const lowestValueLead = leadSuitCards.reduce((lowest, card) =>
        getCardValue(card) < getCardValue(lowest) ? card : lowest
      );
      console.log(
        `[BEST_DISCARD] ⚠️ MALE MINORE: carta di minor valore del seme richiesto: ${lowestValueLead.rank} di ${lowestValueLead.suit}`
      );
      return lowestValueLead;
    }
  }

  // ULTIMA RISORSA: carta di minor valore disponibile (evita comunque briscole preziose)
  const result = cards.reduce((lowest, card) => {
    const lowestValue =
      getCardValue(lowest) + (lowest.suit === trumpSuit ? 20 : 0); // Penalizza molto le briscole
    const cardValue = getCardValue(card) + (card.suit === trumpSuit ? 20 : 0);
    return cardValue < lowestValue ? card : lowest;
  });

  console.log(
    `[BEST_DISCARD] ❌ ULTIMA RISORSA: ${result.rank} di ${
      result.suit
    } (valore: ${getCardValue(result)})`
  );
  return result;
};

/**
 * 🔥 SISTEMA UNIFICATO DECISIONALE: Combina tutte le logiche in una funzione principale
 */
export const getOptimalCardDecision = (
  cards: Card[],
  state: GameState,
  playerIndex: number,
  difficulty: "easy" | "medium" | "hard" = "medium"
): Card => {
  const currentTrick = state.currentTrick || [];

  // Step 1: Analisi ottimale delle carte disponibili
  const analysis = getOptimalCards(cards, currentTrick, state, playerIndex);

  // Step 2: Se abbiamo una raccomandazione forte, usala
  if (
    analysis.recommendation.bestCard &&
    analysis.recommendation.confidence > 0.8
  ) {
    console.log(
      `[DECISIONE UNIFICATA] ✅ Alta confidenza: ${analysis.recommendation.bestCard.rank} - ${analysis.recommendation.reason}`
    );
    return analysis.recommendation.bestCard;
  }

  // Step 3: Applica logica specifica per difficoltà
  const optimalCards =
    analysis.optimalCards.length > 0 ? analysis.optimalCards : cards;

  if (difficulty === "easy") {
    // Easy: 50% casualità per evitare predicibilità
    if (Math.random() < 0.5 && analysis.recommendation.bestCard) {
      return analysis.recommendation.bestCard;
    }
    // Fallback casuale tra carte ottimali
    return optimalCards[Math.floor(Math.random() * optimalCards.length)];
  }

  if (difficulty === "medium") {
    // Medium: logica bilanciata
    if (
      analysis.recommendation.bestCard &&
      analysis.recommendation.confidence > 0.6
    ) {
      return analysis.recommendation.bestCard;
    }

    // Ordina per valore e prendi carta appropriata
    if (currentTrick.length === 0) {
      // Primo a giocare: carta media sicura
      const sortedByValue = [...optimalCards].sort(
        (a, b) => getCardValue(a) - getCardValue(b)
      );
      const midIndex = Math.floor(sortedByValue.length / 2);
      return sortedByValue[midIndex];
    } else {
      // Seguire: logica vincere o minimizzare spreco
      return findBestDiscardForWorthlessTrick(
        optimalCards,
        state.leadSuit,
        state.trumpSuit
      );
    }
  }

  // Hard: usa sempre la raccomandazione se disponibile
  if (analysis.recommendation.bestCard) {
    return analysis.recommendation.bestCard;
  }

  // Fallback: carta di minor valore tra quelle ottimali
  return optimalCards.reduce((lowest, card) =>
    getCardValue(card) < getCardValue(lowest) ? card : lowest
  );
};

/**
 * Helper: Trova chi sta vincendo la presa corrente
 */
export const getCurrentTrickWinner = (
  currentTrick: Card[],
  leadSuit: Suit | null,
  trumpSuit: Suit | null,
  leadPlayer: number
): number => {
  if (currentTrick.length === 0) return 0;

  let winnerIndex = 0;
  let winningCard = currentTrick[0];

  for (let i = 1; i < currentTrick.length; i++) {
    const card = currentTrick[i];

    // Se la carta corrente può battere quella vincente
    if (canCardBeatCard(card, winningCard, leadSuit, trumpSuit)) {
      winnerIndex = i;
      winningCard = card;
    }
  }

  return winnerIndex;
};

/**
 * Helper: Verifica se una carta può battere un'altra
 */
export const canCardBeatCard = (
  attackingCard: Card,
  defendingCard: Card,
  leadSuit: Suit | null,
  trumpSuit: Suit | null
): boolean => {
  // Se entrambe sono briscole
  if (
    trumpSuit &&
    attackingCard.suit === trumpSuit &&
    defendingCard.suit === trumpSuit
  ) {
    return getCardOrder(attackingCard) > getCardOrder(defendingCard);
  }

  // Se solo l'attaccante è briscola
  if (
    trumpSuit &&
    attackingCard.suit === trumpSuit &&
    defendingCard.suit !== trumpSuit
  ) {
    return true;
  }

  // Se solo il difensore è briscola
  if (
    trumpSuit &&
    attackingCard.suit !== trumpSuit &&
    defendingCard.suit === trumpSuit
  ) {
    return false;
  }

  // Se entrambe sono del seme di uscita
  if (
    leadSuit &&
    attackingCard.suit === leadSuit &&
    defendingCard.suit === leadSuit
  ) {
    return getCardOrder(attackingCard) > getCardOrder(defendingCard);
  }

  // Se solo l'attaccante è del seme di uscita
  if (
    leadSuit &&
    attackingCard.suit === leadSuit &&
    defendingCard.suit !== leadSuit
  ) {
    return true;
  }

  // Se solo il difensore è del seme di uscita
  if (
    leadSuit &&
    attackingCard.suit !== leadSuit &&
    defendingCard.suit === leadSuit
  ) {
    return false;
  }

  // Se nessuna è del seme di uscita o briscola, non può vincere
  return false;
};

/**
 * 🔥 CONTROLLO ASSOLUTO POTENZIATO: NON SPRECARE MAI CARTE PREZIOSE AGLI AVVERSARI
 * Questa funzione è DRACONIANA e blocca SEMPRE assi, figure e carte strategiche quando vince l'avversario
 * MIGLIORAMENTO: Include anche figure (K, H, J) nel controllo rigoroso
 */
export const isCardWasteToOpponent = (
  card: Card,
  availableCards: Card[],
  currentTrick: Card[],
  state: GameState,
  playerIndex: number
): boolean => {
  const cardValue = getCardValue(card);

  // Calcola il valore della presa
  const trickValue = currentTrick.reduce(
    (sum, trickCard) => sum + getCardValue(trickCard),
    0
  );
  // 🚨 REGOLA ASSOLUTA: Se ci sono punti sul tavolo e posso vincere, MAI SPRECO!
  if (trickValue >= 1) {
    const canWin = canWinCurrentTrick(
      card,
      currentTrick,
      state.leadSuit,
      state.trumpSuit
    );

    if (canWin) {
      console.log(
        `[ANTI-SPRECO] ✅ REGOLA ASSOLUTA: ${trickValue.toFixed(
          1
        )} punti sul tavolo e posso vincere con ${card.rank} - MAI SPRECO!`
      );
      return false;
    }

    // 🤝 REGOLA ASSOLUTA BIS: Se il compagno sta vincendo con punti, aiutarlo MAI è spreco!
    let teammateIsWinning = false;
    if (currentTrick.length > 0 && state.leadPlayer !== undefined) {
      const winnerIndex = getCurrentTrickWinner(
        currentTrick,
        state.leadSuit,
        state.trumpSuit,
        state.leadPlayer
      );
      const winnerPlayerIndex = (state.leadPlayer + winnerIndex) % 4;
      const myTeam = state.players[playerIndex].team;
      const winnerTeam = state.players[winnerPlayerIndex].team;
      teammateIsWinning =
        winnerTeam === myTeam && winnerPlayerIndex !== playerIndex;
    }

    if (teammateIsWinning) {
      console.log(
        `[ANTI-SPRECO] ✅ REGOLA ASSOLUTA BIS: ${trickValue.toFixed(
          1
        )} punti e compagno vince - aiutare con ${card.rank} MAI è spreco!`
      );
      return false;
    }
  }

  // Le carte senza valore non sono mai spreco
  if (cardValue === 0) {
    console.log(
      `[ANTI-SPRECO] ✅ Carta ${card.rank} senza valore - OK giocare`
    );
    return false;
  }

  // 🚫 REGOLA ASSOLUTA POTENZIATA: NON DARE 3, 2, ASSO, FIGURE agli avversari
  const isPreciousCard =
    card.rank === "3" ||
    card.rank === "2" ||
    card.rank === "A" ||
    card.rank === "K" ||
    card.rank === "H" ||
    card.rank === "J";
  if (isPreciousCard) {
    console.log(
      `[ANTI-SPRECO ASSOLUTO] 🚫 CARTA PREZIOSA ${card.rank} (valore: ${cardValue}) - verifica obbligatoria!`
    );

    // 🚨 PRIORITÀ ASSOLUTA: ASSO SUL TAVOLO = SEMPRE OK USARE QUALSIASI CARTA!
    const hasAceOnTable = currentTrick.some(
      (trickCard) => trickCard.rank === "A"
    );
    if (hasAceOnTable) {
      console.log(
        `[ANTI-SPRECO] ✅ ASSO SUL TAVOLO! ${card.rank} OK per prendere 1 punto!`
      );
      return false; // NON è spreco quando c'è un asso sul tavolo!
    }

    // Verifica se posso vincere con questa carta
    const canWin = canWinCurrentTrick(
      card,
      currentTrick,
      state.leadSuit,
      state.trumpSuit
    );

    // Verifica se il compagno sta vincendo
    let teammateIsWinning = false;
    if (currentTrick.length > 0 && state.leadPlayer !== undefined) {
      const winnerIndex = getCurrentTrickWinner(
        currentTrick,
        state.leadSuit,
        state.trumpSuit,
        state.leadPlayer
      );
      const winnerPlayerIndex = (state.leadPlayer + winnerIndex) % 4;
      const myTeam = state.players[playerIndex].team;
      const winnerTeam = state.players[winnerPlayerIndex].team;
      teammateIsWinning =
        winnerTeam === myTeam && winnerPlayerIndex !== playerIndex;
    }

    console.log(`[ANTI-SPRECO] ${card.rank} di ${card.suit}:`);
    console.log(`  - Posso vincere: ${canWin ? "SÌ" : "NO"}`);
    console.log(`  - Compagno vince: ${teammateIsWinning ? "SÌ" : "NO"}`);

    // CONTROLLO RIGOROSO: Se non posso vincere E il compagno non sta vincendo = SPRECO AGLI AVVERSARI!
    if (!canWin && !teammateIsWinning) {
      // Verifica se ho alternative senza valore
      const hasNoValueAlternatives = availableCards.some(
        (c) => getCardValue(c) === 0
      );

      if (hasNoValueAlternatives) {
        console.log(
          `[ANTI-SPRECO ASSOLUTO] ❌❌❌ BLOCCO TOTALE ${card.rank}! Ho carte senza valore disponibili!`
        );
        return true; // È uno spreco GRAVE!
      } else {
        console.log(
          `[ANTI-SPRECO ASSOLUTO] ⚠️ Nessuna alternativa senza valore per ${card.rank}, ma valuterò altri fattori`
        ); // Anche senza alternative, blocca comunque 3, 2, A se la presa vale poco
        if (card.rank === "3" || card.rank === "2" || card.rank === "A") {
          // Blocca assi, 2 e 3 se la presa vale meno di 2 punti
          if (trickValue < 2) {
            console.log(
              `[ANTI-SPRECO ASSOLUTO] ❌❌❌ BLOCCO ${card.rank}! Presa vale solo ${trickValue} punti!`
            );
            return true;
          }
        }
      }
    }
  }
  // Per tutte le carte con valore, verifica se la presa vale almeno qualcosa
  if (cardValue > 0) {
    // CONTROLLO RIGOROSO: Se la presa vale 0 punti, è sempre spreco dare carte con valore
    if (trickValue === 0) {
      console.log(
        `[ANTI-SPRECO] ❌ PRESA SENZA VALORE - non do ${card.rank} (vale ${cardValue} punti)`
      );
      return true;
    }

    // CONTROLLO AGGIUNTIVO: Se carta vale più della presa, è probabile spreco
    if (cardValue > trickValue * 1.5) {
      console.log(
        `[ANTI-SPRECO] ⚠️ POSSIBILE SPRECO - ${card.rank} vale ${cardValue}, presa vale ${trickValue}`
      );
      return true;
    }
  }

  return false;
};

/**
 * 🔥 FILTRO OTTIMIZZATO: Sistema unificato di filtro anti-spreco
 * Sostituisce filterNonWasteCards con logica più efficiente
 */
export const getOptimalCards = (
  cards: Card[],
  currentTrick: Card[],
  state: GameState,
  playerIndex: number
): {
  optimalCards: Card[];
  wasteAnalysis: CardWasteAnalysis[];
  recommendation: {
    bestCard: Card | null;
    reason: string;
    confidence: number;
  };
} => {
  // Ottieni memoria una sola volta
  let memory: CardMemory | null = null;
  try {
    memory = analyzeCardMemory(state);
  } catch (error) {
    console.warn("Errore nell'analisi memoria:", error);
  }

  // Analizza ogni carta una sola volta
  const wasteAnalysis = cards.map((card) =>
    analyzeCardWaste(card, cards, currentTrick, state, playerIndex, memory)
  );

  // Filtra carte non-spreco
  const optimalCards = cards.filter(
    (_, index) => !wasteAnalysis[index].isWaste
  );

  // Se tutte le carte sono spreco, prendi quella con severity più bassa
  let finalCards = optimalCards;
  if (optimalCards.length === 0) {
    console.log(
      `[SISTEMA ANTI-SPRECO] ⚠️ Tutte le carte sono spreco, scelgo male minore`
    );

    const minSeverity = Math.min(
      ...wasteAnalysis.map((analysis) => {
        const severityMap = { low: 1, medium: 2, high: 3, critical: 4 };
        return severityMap[analysis.severity];
      })
    );

    finalCards = cards.filter((_, index) => {
      const severityMap = { low: 1, medium: 2, high: 3, critical: 4 };
      return severityMap[wasteAnalysis[index].severity] === minSeverity;
    });
  }

  // Raccomandazione intelligente
  let bestCard: Card | null = null;
  let reason = "";
  let confidence = 0;

  if (finalCards.length > 0) {
    if (currentTrick.length === 0) {
      // Prima a giocare: carta sicura o strategica
      const safeCards = finalCards.filter((card) =>
        memory
          ? isCardSafe(card, memory, state.trumpSuit, state.leadSuit)
          : false
      );
      bestCard = safeCards.length > 0 ? safeCards[0] : finalCards[0];
      reason =
        safeCards.length > 0
          ? "Carta sicura per aprire"
          : "Carta strategica per aprire";
      confidence = safeCards.length > 0 ? 0.9 : 0.7;
    } else {
      // Seguire: cerca carta che può vincere o supporta compagno
      const winningCards = finalCards.filter((card) =>
        canWinCurrentTrick(card, currentTrick, state.leadSuit, state.trumpSuit)
      );

      if (winningCards.length > 0) {
        // Ordina per valore crescente (usa la più debole che può vincere)
        winningCards.sort((a, b) => getCardValue(a) - getCardValue(b));
        bestCard = winningCards[0];
        reason = "Carta più debole che può vincere";
        confidence = 0.85;
      } else {
        // Non posso vincere: carta di minor valore
        finalCards.sort((a, b) => getCardValue(a) - getCardValue(b));
        bestCard = finalCards[0];
        reason = "Carta di minor valore (non posso vincere)";
        confidence = 0.6;
      }
    }
  }

  console.log(
    `[SISTEMA ANTI-SPRECO] Carte ottimali: ${finalCards.length}/${cards.length}`
  );
  if (bestCard) {
    console.log(
      `[RACCOMANDAZIONE] ${bestCard.rank} di ${bestCard.suit} - ${reason} (confidenza: ${confidence})`
    );
  }

  return {
    optimalCards: finalCards,
    wasteAnalysis,
    recommendation: {
      bestCard,
      reason,
      confidence,
    },
  };
};

/**
 * 🔥 CONTROLLO DOMINANZA: Verifica se una carta è diventata dominante nel suo seme
 * Una carta è dominante se tutte le carte più forti del suo seme sono già state giocate
 */
export const isCardDominantInSuit = (
  card: Card,
  memory: CardMemory | null,
  state: GameState
): boolean => {
  if (!memory) return false;

  // Lista di tutti i rank in ordine di forza (dal più forte al più debole)
  const allRanks = ["3", "2", "A", "K", "H", "J", "7", "6", "5", "4"];
  const cardOrder = getCardOrder(card);

  // Trova tutti i rank più forti della carta corrente
  const strongerRanks = allRanks.filter((rank) => {
    const testCard = { rank, suit: card.suit } as Card;
    return getCardOrder(testCard) > cardOrder;
  });

  // Verifica se tutte le carte più forti sono state giocate
  const playedCardsOfSuit = memory.playedBySuit[card.suit] || [];
  const allStrongerCardsPlayed = strongerRanks.every((rank) =>
    playedCardsOfSuit.some((playedCard) => playedCard.rank === rank)
  );

  if (allStrongerCardsPlayed) {
    console.log(
      `[DOMINANZA] 🏆 ${card.rank} di ${card.suit} è DOMINANTE! Tutte le carte più forti sono state giocate`
    );
    console.log(
      `[DOMINANZA] Carte più forti giocate: ${strongerRanks.join(", ")}`
    );
    return true;
  }

  return false;
};

/**
 * 🔥 CONTROLLO ANTI-SPRECO DOMINANZA: NON buttare mai carte dominanti se hai alternative
 */
export const isWastingDominantCard = (
  card: Card,
  availableCards: Card[],
  currentTrick: Card[],
  state: GameState,
  playerIndex: number,
  memory: CardMemory | null
): boolean => {
  // Se la carta non è dominante, non è spreco per dominanza
  if (!isCardDominantInSuit(card, memory, state)) {
    return false;
  }

  console.log(
    `[ANTI-SPRECO DOMINANZA] ⚠️ VERIFICA CARTA DOMINANTE: ${card.rank} di ${card.suit}`
  );

  // Verifica se posso vincere con questa carta dominante
  const canWin = canWinCurrentTrick(
    card,
    currentTrick,
    state.leadSuit,
    state.trumpSuit
  );

  // Verifica se il compagno sta vincendo
  let teammateIsWinning = false;
  if (currentTrick.length > 0 && state.leadPlayer !== undefined) {
    const winnerIndex = getCurrentTrickWinner(
      currentTrick,
      state.leadSuit,
      state.trumpSuit,
      state.leadPlayer
    );
    const winnerPlayerIndex = (state.leadPlayer + winnerIndex) % 4;
    const myTeam = state.players[playerIndex].team;
    const winnerTeam = state.players[winnerPlayerIndex].team;
    teammateIsWinning =
      winnerTeam === myTeam && winnerPlayerIndex !== playerIndex;
  }

  // Se posso vincere con la carta dominante O il compagno sta vincendo, è OK usarla
  if (canWin || teammateIsWinning) {
    console.log(
      `[ANTI-SPRECO DOMINANZA] ✅ OK usare carta dominante - ${
        canWin ? "posso vincere" : "compagno vince"
      }`
    );
    return false;
  }

  // Verifica se ho alternative dello stesso seme (se devo seguire il seme)
  if (state.leadSuit && card.suit === state.leadSuit) {
    const leadSuitCards = availableCards.filter(
      (c) => c.suit === state.leadSuit
    );
    const alternatives = leadSuitCards.filter((c) => c.id !== card.id);

    if (alternatives.length > 0) {
      console.log(
        `[ANTI-SPRECO DOMINANZA] ❌❌❌ SPRECO! Ho ${alternatives.length} alternative del seme, non butto carta DOMINANTE!`
      );
      return true; // È spreco!
    }
  }

  // Verifica se ho alternative generali (se non devo seguire il seme)
  if (!state.leadSuit || card.suit !== state.leadSuit) {
    const alternatives = availableCards.filter((c) => c.id !== card.id);

    if (alternatives.length > 0) {
      console.log(
        `[ANTI-SPRECO DOMINANZA] ❌❌❌ SPRECO! Ho ${alternatives.length} alternative, non butto carta DOMINANTE!`
      );
      return true; // È spreco!
    }
  }

  // Se non ho alternative, devo giocarla anche se è dominante
  console.log(
    `[ANTI-SPRECO DOMINANZA] ⚠️ Nessuna alternativa, devo giocare carta dominante`
  );
  return false;
};

/**
 * 🔥 CONTROLLO TAGLIO INTELLIGENTE: NON sprecare 3 e 2 di briscola quando tagli!
 * Quando non hai il seme richiesto e devi tagliare con una briscola,
 * usa SEMPRE la briscola più debole possibile, conservando 3 e 2 per situazioni critiche
 */
export const isWastingTrumpForCutting = (
  card: Card,
  availableCards: Card[],
  state: GameState,
  playerIndex: number
): boolean => {
  // Se non è una briscola, non è applicabile
  if (!state.trumpSuit || card.suit !== state.trumpSuit) {
    return false;
  }

  // Se devo seguire il seme (ho carte del seme richiesto), non è un taglio
  if (state.leadSuit) {
    const leadSuitCards = availableCards.filter(
      (c) => c.suit === state.leadSuit
    );
    if (leadSuitCards.length > 0) {
      return false; // Non è un taglio, devo seguire il seme
    }
  }

  // È un taglio! Verifica se sto sprecando una briscola strategica
  const isStrategicTrump = card.rank === "3" || card.rank === "2";

  if (!isStrategicTrump) {
    return false; // Non è una briscola strategica, va bene usarla per tagliare
  }

  console.log(
    `[TAGLIO INTELLIGENTE] ⚠️ VERIFICA TAGLIO con ${card.rank} di briscola`
  );

  // Trova tutte le briscole disponibili
  const trumpCards = availableCards.filter((c) => c.suit === state.trumpSuit);

  // Cerca briscole alternative più deboli per tagliare
  const weakerTrumps = trumpCards.filter((trump) => {
    // Qualsiasi briscola tranne 3 e 2 è accettabile per tagliare
    return trump.rank !== "3" && trump.rank !== "2";
  });

  // Se ho briscole più deboli disponibili, non usare il 3 o il 2
  if (weakerTrumps.length > 0) {
    console.log(
      `[TAGLIO INTELLIGENTE] ❌ BLOCCO ${card.rank}! Ho ${weakerTrumps.length} briscole più deboli disponibili`
    );
    return true; // È spreco, usa una briscola più debole
  }

  if (weakerTrumps.length > 0) {
    console.log(
      `[TAGLIO INTELLIGENTE] ❌❌❌ SPRECO! Ho ${weakerTrumps.length} briscole più deboli per tagliare, non uso ${card.rank}!`
    );
    console.log(
      `[TAGLIO INTELLIGENTE] 💎 CONSERVO ${card.rank} per situazioni critiche (asso di briscola, ultima mano)`
    );
    return true; // È spreco! Ho alternative più deboli
  }

  // Casi speciali dove è OK usare 3 o 2 per tagliare
  const trickNumber = state.trickNumber || 1;
  const isLastTrick = trickNumber === 10;

  // Verifica se c'è un asso di briscola nella presa corrente
  const currentTrick = state.currentTrick || [];
  const hasAceOfTrumps = currentTrick.some(
    (c) => c.suit === state.trumpSuit && c.rank === "A"
  );

  if (isLastTrick) {
    console.log(
      `[TAGLIO INTELLIGENTE] ✅ OK usare ${card.rank} - È L'ULTIMA MANO!`
    );
    return false;
  }

  if (hasAceOfTrumps) {
    console.log(
      `[TAGLIO INTELLIGENTE] ✅ OK usare ${card.rank} - C'è l'ASSO DI BRISCOLA nella presa!`
    );
    return false;
  }

  // Calcola il valore della presa per decidere se vale la pena usare briscole strategiche
  const evaluator = new CardEvaluator();
  const trickValue = currentTrick.reduce(
    (sum, card) => sum + evaluator.getCardValue(card),
    0
  );

  // REGOLA SPECIALE PER IL 3 DI BRISCOLA: Molto più restrittivo
  if (card.rank === "3") {
    // Il 3 di briscola dovrebbe essere usato solo per:
    // 1. L'ultima presa della mano
    // 2. Prese con molti punti (≥3 punti)
    // 3. Quando c'è un asso di briscola nella presa

    if (trickValue >= 3) {
      console.log(
        `[TAGLIO INTELLIGENTE] ✅ OK usare 3 di briscola - Presa di alto valore (${trickValue} punti)!`
      );
      return false;
    }

    console.log(
      `[TAGLIO INTELLIGENTE] ❌ BLOCCO 3 DI BRISCOLA! Presa di basso valore (${trickValue} punti), tengo per prese importanti`
    );
    return true; // È spreco per una presa di basso valore
  }

  // Per il 2 di briscola: meno restrittivo del 3, ma comunque attento
  if (card.rank === "2" && trickValue < 2) {
    console.log(
      `[TAGLIO INTELLIGENTE] ❌ BLOCCO 2 DI BRISCOLA! Presa di basso valore (${trickValue} punti)`
    );
    return true;
  }

  // Se ho solo briscole strategiche, devo usarle
  if (
    trumpCards.length ===
    trumpCards.filter((t) => t.rank === "3" || t.rank === "2").length
  ) {
    console.log(
      `[TAGLIO INTELLIGENTE] ⚠️ Solo briscole strategiche disponibili, devo usare ${card.rank}`
    );
    return false;
  }

  console.log(
    `[TAGLIO INTELLIGENTE] ❌ BLOCCO ${card.rank}! È un taglio normale, uso briscola più debole`
  );
  return true; // È spreco per un taglio normale
};

/**
 * 🎯 CONTA BRISCOLE USCITE: Calcola quante briscole sono già state giocate
 */
export const countPlayedTrumps = (state: GameState): number => {
  if (!state.trumpSuit) return 0;

  const allPlayedCards = [
    ...state.teams[0].tricksWon,
    ...state.teams[1].tricksWon,
    ...(state.currentTrick || []),
  ];

  return allPlayedCards.filter((card) => card && card.suit === state.trumpSuit)
    .length;
};

/**
 * 🎯 STRATEGIA MOLTE BRISCOLE: Se ho 4+ briscole, l'obiettivo è far finire le briscole agli avversari
 */
export const getManyTrumpsStrategy = (
  availableCards: Card[],
  state: GameState,
  playerIndex: number
): {
  shouldPlayTrump: boolean;
  recommendedCard: Card | null;
  reason: string;
} => {
  if (!state.trumpSuit) {
    return {
      shouldPlayTrump: false,
      recommendedCard: null,
      reason: "Nessuna briscola selezionata",
    };
  }

  const trumpCards = availableCards.filter(
    (card) => card.suit === state.trumpSuit
  );

  if (trumpCards.length < 4) {
    return {
      shouldPlayTrump: false,
      recommendedCard: null,
      reason: `Solo ${trumpCards.length} briscole, strategia non applicabile`,
    };
  }

  const playedTrumps = countPlayedTrumps(state);
  const remainingTrumps = 10 - playedTrumps; // 10 briscole totali per seme
  const opponentTrumps = remainingTrumps - trumpCards.length;

  console.log(
    `[STRATEGIA MOLTE BRISCOLE] 🎯 Ho ${trumpCards.length} briscole, giocate: ${playedTrumps}, rimaste agli avversari: ~${opponentTrumps}`
  );

  // Se gli avversari hanno ancora molte briscole, continua a giocarle per farle finire
  if (opponentTrumps > 2) {
    // Gioca una briscola media, non la più alta
    const mediumTrumps = trumpCards.filter(
      (card) => !["3", "2", "A"].includes(card.rank) // Evita le briscole più forti
    );

    let trumpToPlay: Card;
    if (mediumTrumps.length > 0) {
      trumpToPlay = mediumTrumps[0];
    } else {
      // Se ho solo briscole forti, gioca quella meno forte
      const evaluator = new CardEvaluator();
      const sortedTrumps = trumpCards.sort(
        (a, b) => evaluator.getCardOrder(a) - evaluator.getCardOrder(b)
      );
      trumpToPlay = sortedTrumps[0];
    }

    return {
      shouldPlayTrump: true,
      recommendedCard: trumpToPlay,
      reason: `Strategia molte briscole: gioco ${trumpToPlay.rank} per far finire le briscole agli avversari (~${opponentTrumps} rimaste)`,
    };
  }

  return {
    shouldPlayTrump: false,
    recommendedCard: null,
    reason: `Avversari hanno poche briscole (~${opponentTrumps}), non serve continuare la strategia`,
  };
};

/**
 * 🎯 STRATEGIA CARTE FORTI PER ULTIMO TURNO: Conserva carte forti per l'endgame
 */
export const getEndgameStrategy = (
  availableCards: Card[],
  state: GameState,
  playerIndex: number
): { shouldConserve: boolean; cardToAvoid: Card | null; reason: string } => {
  if (!state.trumpSuit) {
    return {
      shouldConserve: false,
      cardToAvoid: null,
      reason: "Nessuna briscola selezionata",
    };
  }

  const trumpCards = availableCards.filter(
    (card) => card.suit === state.trumpSuit
  );
  const trickNumber = state.trickNumber || 1;
  const isNearEndgame = trickNumber >= 8; // Ultimi 3 turni
  const isLastTrick = trickNumber === 10;

  if (isLastTrick) {
    return {
      shouldConserve: false,
      cardToAvoid: null,
      reason: "È l'ultimo turno, gioca tutto",
    };
  }

  // Se non ho briscole alte, non c'è strategia di conservazione
  const highTrumps = trumpCards.filter((card) =>
    ["3", "2", "A"].includes(card.rank)
  );
  if (highTrumps.length === 0) {
    return {
      shouldConserve: false,
      cardToAvoid: null,
      reason: "Non ho briscole alte da conservare",
    };
  }

  // Determina quale carta conservare per l'ultimo turno
  const playedTrumps = countPlayedTrumps(state);
  const allPlayedCards = [
    ...state.teams[0].tricksWon,
    ...state.teams[1].tricksWon,
    ...(state.currentTrick || []),
  ];

  // Controlla quali briscole forti sono già uscite
  const played3 = allPlayedCards.some(
    (card) => card && card.suit === state.trumpSuit && card.rank === "3"
  );
  const played2 = allPlayedCards.some(
    (card) => card && card.suit === state.trumpSuit && card.rank === "2"
  );
  const playedAce = allPlayedCards.some(
    (card) => card && card.suit === state.trumpSuit && card.rank === "A"
  );

  let cardToConserve: Card | null = null;

  // Priorità di conservazione: 3 > 2 > A
  if (!played3 && trumpCards.some((card) => card.rank === "3")) {
    cardToConserve = trumpCards.find((card) => card.rank === "3") || null;
  } else if (!played2 && trumpCards.some((card) => card.rank === "2")) {
    cardToConserve = trumpCards.find((card) => card.rank === "2") || null;
  } else if (!playedAce && trumpCards.some((card) => card.rank === "A")) {
    cardToConserve = trumpCards.find((card) => card.rank === "A") || null;
  }

  if (!cardToConserve) {
    return {
      shouldConserve: false,
      cardToAvoid: null,
      reason: "Tutte le briscole forti sono già uscite",
    };
  }

  // Solo conserva se siamo vicini all'endgame
  if (isNearEndgame) {
    return {
      shouldConserve: true,
      cardToAvoid: cardToConserve,
      reason: `Conservo ${cardToConserve.rank} di briscola per l'ultimo turno (turno ${trickNumber}/10)`,
    };
  }

  return {
    shouldConserve: false,
    cardToAvoid: null,
    reason: "Troppo presto per conservare carte forti",
  };
};

/**
 * 🎯 PRIORITÀ 3 SU 2: Se ho sia 2 che 3 dello stesso seme, preferisci sempre il 3
 * Regola: Non giocare il 2 se il 3 dello stesso seme non è ancora uscito
 */
export const prioritize3Over2 = (cards: Card[], memory: CardMemory): Card[] => {
  const result: Card[] = [];
  const cardsBySuit: { [suit: string]: Card[] } = {};

  // Raggruppa le carte per seme
  cards.forEach((card) => {
    if (!cardsBySuit[card.suit]) {
      cardsBySuit[card.suit] = [];
    }
    cardsBySuit[card.suit].push(card);
  });

  // Per ogni seme, applica la regola di priorità
  Object.keys(cardsBySuit).forEach((suit) => {
    const suitCards = cardsBySuit[suit];
    const has2 = suitCards.some((card) => card.rank === "2");
    const has3 = suitCards.some((card) => card.rank === "3");

    if (has2 && has3) {
      // Ho sia 2 che 3: controlla se il 3 è già uscito
      const three3Played = memory.playedCards.some(
        (card) => card.suit === suit && card.rank === "3"
      );

      if (!three3Played) {
        // Il 3 non è ancora uscito: preferisci il 3, escludi il 2
        console.log(
          `[PRIORITÀ 3 SU 2] 🎯 Ho sia 2 che 3 di ${suit}, il 3 non è uscito - preferisco il 3`
        );
        suitCards.forEach((card) => {
          if (card.rank !== "2") {
            result.push(card);
          }
        });
      } else {
        // Il 3 è già uscito: ora il 2 è sicuro
        console.log(
          `[PRIORITÀ 3 SU 2] ✅ Il 3 di ${suit} è già uscito - ora il 2 è sicuro`
        );
        result.push(...suitCards);
      }
    } else {
      // Non ho entrambi: aggiungi tutte le carte del seme
      result.push(...suitCards);
    }
  });

  return result;
};

/**
 * 🎯 STRATEGIA BUSSO COOPERATIVA MIGLIORATA:
 * 1. Se il compagno ha chiamato busso, devo cercare di prendere con quel seme
 * 2. Se ho preso dopo un busso del compagno, DEVO tornare in quel seme il turno dopo (TASSATIVO)
 */
export const getBussoFollowUpStrategy = (
  availableCards: Card[],
  state: GameState,
  playerIndex: number
): {
  shouldPlaySuit: boolean;
  recommendedCard: Card | null;
  reason: string;
} => {
  const currentTrick = state.currentTrick || [];
  const isFirstInTrick = currentTrick.length === 0;

  // Se non sono primo del turno, non posso applicare questa strategia
  if (!isFirstInTrick) {
    return {
      shouldPlaySuit: false,
      recommendedCard: null,
      reason: "Non sono primo del turno",
    };
  }

  // Controlla se c'è stata una dichiarazione busso recente del compagno
  if (typeof window !== "undefined" && (window as any).aiMemory) {
    const aiMemory = (window as any).aiMemory;
    const currentPlayer = state.players[playerIndex];

    // 🎯 PRIORITÀ ASSOLUTA: Controlla le obbligazioni di busso
    if (aiMemory.bussoObligations) {
      const myObligation = aiMemory.bussoObligations.find(
        (obl: any) =>
          obl.playerIndex === playerIndex &&
          obl.mustReturnInNextTrick &&
          obl.trickNumber === state.trickNumber - 1 // Obbligo dal turno precedente
      );

      if (myObligation) {
        const obligationSuit = myObligation.suit;
        const suitCards = availableCards.filter(
          (card) => card.suit === obligationSuit
        );

        if (suitCards.length > 0) {
          // TASSATIVO: gioca una carta di quel seme
          const strongCards = suitCards.filter((card) =>
            ["A", "3", "2"].includes(card.rank as string)
          );
          const recommendedCard =
            strongCards.length > 0 ? strongCards[0] : suitCards[0];

          // Rimuovi l'obbligo dopo averlo soddisfatto
          aiMemory.bussoObligations = aiMemory.bussoObligations.filter(
            (obl: any) => obl !== myObligation
          );

          return {
            shouldPlaySuit: true,
            recommendedCard,
            reason: `OBBLIGO BUSSO: Devo tornare in ${obligationSuit} con ${recommendedCard.rank} dopo aver preso`,
          };
        } else {
          // Rimuovi l'obbligo anche se non posso soddisfarlo
          aiMemory.bussoObligations = aiMemory.bussoObligations.filter(
            (obl: any) => obl !== myObligation
          );

          return {
            shouldPlaySuit: false,
            recommendedCard: null,
            reason: `ERRORE OBBLIGO: Dovrei tornare in ${obligationSuit} ma non ho più carte di quel seme`,
          };
        }
      }
    }

    // 🎯 PRIORITÀ 2: Controlla dichiarazioni busso recenti per cercare di prendere
    if (aiMemory.strategicAnnouncements) {
      const announcements = aiMemory.strategicAnnouncements;

      // Cerca dichiarazioni busso recenti del compagno (ultimi 2 turni)
      const recentBusso = announcements
        .filter(
          (ann: any) =>
            ann.type === "busso" &&
            state.trickNumber - ann.trickNumber <= 2 &&
            state.players[ann.playerIndex].team === currentPlayer.team &&
            ann.playerIndex !== playerIndex
        )
        .sort((a: any, b: any) => b.trickNumber - a.trickNumber)[0]; // Più recente

      if (recentBusso) {
        // Il compagno ha fatto busso - devo cercare di prendere con quel seme
        const bussoSuit = recentBusso.suit;
        const suitCards = availableCards.filter(
          (card) => card.suit === bussoSuit
        );

        if (suitCards.length > 0) {
          // Cerca di prendere con la carta più forte del seme
          const strongCards = suitCards.filter((card) =>
            ["A", "3", "2"].includes(card.rank as string)
          );
          const recommendedCard =
            strongCards.length > 0 ? strongCards[0] : suitCards[0];

          return {
            shouldPlaySuit: true,
            recommendedCard,
            reason: `Seguo busso del compagno: cerco di prendere con ${recommendedCard.rank} di ${bussoSuit}`,
          };
        } else {
          return {
            shouldPlaySuit: false,
            recommendedCard: null,
            reason: `Vorrei seguire busso del compagno (${bussoSuit}) ma non ho carte di quel seme`,
          };
        }
      }
    }
  }

  return {
    shouldPlaySuit: false,
    recommendedCard: null,
    reason: "Nessun busso recente del compagno da seguire",
  };
};

/**
 * 🔥 TROVA BRISCOLA OTTIMALE PER TAGLIO: Seleziona la briscola più debole possibile
 */
export const findBestTrumpForCutting = (
  trumpCards: Card[],
  state: GameState,
  currentTrick: Card[]
): Card => {
  if (trumpCards.length === 0) {
    throw new Error("Nessuna briscola disponibile per tagliare");
  }

  console.log(
    `[TAGLIO INTELLIGENTE] 🎯 Seleziono briscola ottimale tra ${trumpCards.length} disponibili`
  );

  // Priorità 1: Briscole senza valore (lisce)
  const trumpsNoValue = trumpCards.filter((card) => getCardValue(card) === 0);

  if (trumpsNoValue.length > 0) {
    // Tra quelle senza valore, prendi la più debole
    const weakestNoValue = trumpsNoValue.reduce((weakest, card) =>
      getCardOrder(card) < getCardOrder(weakest) ? card : weakest
    );
    console.log(
      `[TAGLIO INTELLIGENTE] ✅ Uso briscola senza valore: ${weakestNoValue.rank}`
    );
    return weakestNoValue;
  }

  // Priorità 2: Briscole con poco valore (escludi 3, 2, A)
  const trumpsLowValue = trumpCards.filter(
    (card) => !["3", "2", "A"].includes(card.rank)
  );

  if (trumpsLowValue.length > 0) {
    // Tra quelle a basso valore, prendi la più debole
    const weakestLowValue = trumpsLowValue.reduce((weakest, card) =>
      getCardOrder(card) < getCardOrder(weakest) ? card : weakest
    );
    console.log(
      `[TAGLIO INTELLIGENTE] ✅ Uso briscola a basso valore: ${weakestLowValue.rank}`
    );
    return weakestLowValue;
  }

  // Priorità 3: Se necessario, usa A prima di 2 e 3
  const trumpsHighValue = trumpCards.filter((card) =>
    ["A", "2", "3"].includes(card.rank)
  );

  if (trumpsHighValue.length > 0) {
    // Preferisci A > 2 > 3 in ordine di "sacrificabilità"
    const aceOfTrumps = trumpsHighValue.find((card) => card.rank === "A");
    if (aceOfTrumps) {
      console.log(
        `[TAGLIO INTELLIGENTE] ⚠️ Uso ASSO di briscola (situazione critica)`
      );
      return aceOfTrumps;
    }

    const twoOfTrumps = trumpsHighValue.find((card) => card.rank === "2");
    if (twoOfTrumps) {
      console.log(
        `[TAGLIO INTELLIGENTE] ⚠️ Uso 2 di briscola (situazione critica)`
      );
      return twoOfTrumps;
    }

    const threeOfTrumps = trumpsHighValue.find((card) => card.rank === "3");
    if (threeOfTrumps) {
      console.log(
        `[TAGLIO INTELLIGENTE] ⚠️⚠️ Uso 3 di briscola (ULTIMA RISORSA!)`
      );
      return threeOfTrumps;
    }
  }

  // Fallback: la prima briscola disponibile
  console.log(
    `[TAGLIO INTELLIGENTE] ⚠️ Fallback: uso prima briscola disponibile`
  );
  return trumpCards[0];
};

/**
 * 🔥 CONTROLLO FINALE DRACONIANO: Ultima verifica per evitare sprechi
 * Questa funzione è l'ultimo baluardo contro gli sprechi di carte di valore
 */
export const enforceNoWastePolicy = (
  selectedCard: Card,
  availableCards: Card[],
  currentTrick: Card[],
  state: GameState,
  playerIndex: number
): Card => {
  const cardValue = getCardValue(selectedCard);

  // Se la carta selezionata non ha valore, è sempre OK
  if (cardValue === 0) {
    console.log(
      `[ENFORCE_NO_WASTE] ✅ Carta ${selectedCard.rank} senza valore - OK`
    );
    return selectedCard;
  }

  console.log(
    `[ENFORCE_NO_WASTE] 🔍 Controllo finale per carta di valore: ${selectedCard.rank} di ${selectedCard.suit}`
  );

  // Verifica se posso vincere con questa carta
  const canWin = canWinCurrentTrick(
    selectedCard,
    currentTrick,
    state.leadSuit,
    state.trumpSuit
  );

  // Verifica se il compagno sta vincendo
  let teammateIsWinning = false;
  if (currentTrick.length > 0 && state.leadPlayer !== undefined) {
    const winnerIndex = getCurrentTrickWinner(
      currentTrick,
      state.leadSuit,
      state.trumpSuit,
      state.leadPlayer
    );
    const winnerPlayerIndex = (state.leadPlayer + winnerIndex) % 4;
    const myTeam = state.players[playerIndex].team;
    const winnerTeam = state.players[winnerPlayerIndex].team;
    teammateIsWinning =
      winnerTeam === myTeam && winnerPlayerIndex !== playerIndex;
  }

  // 🚨 REGOLA DRACONIANA SPECIALE PER L'ASSO:
  // L'asso è consentito SOLO se può vincere O se il team sta vincendo
  if (selectedCard.rank === "A") {
    console.log(
      `[ENFORCE_NO_WASTE] 🚨 CONTROLLO DRACONIANO ASSO: ${selectedCard.rank} di ${selectedCard.suit}`
    );

    // Per l'asso, solo il fatto che POSSA VINCERE lo giustifica
    // NON basta che il compagno stia vincendo!
    if (canWin) {
      console.log(
        `[ENFORCE_NO_WASTE] ✅ ASSO CONSENTITO: può vincere la presa`
      );
      return selectedCard;
    } else {
      console.log(
        `[ENFORCE_NO_WASTE] 🚫 ASSO BLOCCATO: non può vincere, cercando alternative`
      );
      // Per l'asso, forza la ricerca di alternative anche se il compagno sta vincendo
      // Continua con la logica di ricerca alternative qui sotto
    }
  } else {
    // Per le altre carte di valore, se posso vincere o il compagno sta vincendo, è giustificata
    if (canWin || teammateIsWinning) {
      console.log(
        `[ENFORCE_NO_WASTE] ✅ Carta di valore giustificata - posso vincere: ${canWin}, compagno vince: ${teammateIsWinning}`
      );
      return selectedCard;
    }
  }

  // NON POSSO VINCERE E IL COMPAGNO NON STA VINCENDO
  // Cerco disperatamente carte senza valore da usare al posto di quella selezionata
  console.log(
    `[ENFORCE_NO_WASTE] ⚠️ PERICOLO! Carta di valore andrebbe agli avversari!`
  );

  // Cerca carte senza valore che posso giocare
  let noValueAlternatives: Card[] = [];

  if (state.leadSuit) {
    // Devo seguire il seme: cerco carte senza valore del seme richiesto
    const leadSuitCards = availableCards.filter(
      (card) => card.suit === state.leadSuit
    );
    noValueAlternatives = leadSuitCards.filter(
      (card) => getCardValue(card) === 0
    );

    if (noValueAlternatives.length === 0) {
      // Non ho carte senza valore del seme richiesto, devo usare quella che ho
      console.log(
        `[ENFORCE_NO_WASTE] ⚠️ Devo seguire il seme ${state.leadSuit}, non ho alternative senza valore`
      );
      return selectedCard;
    }
  } else {
    // Posso giocare qualsiasi carta: priorità assoluta a carte senza valore non-briscola
    noValueAlternatives = availableCards.filter(
      (card) => getCardValue(card) === 0 && card.suit !== state.trumpSuit
    );

    if (noValueAlternatives.length === 0) {
      // Nessuna carta senza valore non-briscola, provo con qualsiasi carta senza valore
      noValueAlternatives = availableCards.filter(
        (card) => getCardValue(card) === 0
      );
    }
  }

  if (noValueAlternatives.length > 0) {
    const alternative = noValueAlternatives[0];
    console.log(
      `[ENFORCE_NO_WASTE] ✅ SALVATO! Uso ${alternative.rank} di ${alternative.suit} invece di ${selectedCard.rank}`
    );
    return alternative;
  }

  // Non ho alternative senza valore - controllo se almeno posso usare una carta di minor valore
  const lowerValueCards = availableCards.filter(
    (card) => getCardValue(card) < cardValue && card.id !== selectedCard.id
  );

  if (state.leadSuit) {
    // Tra le carte del seme richiesto, cerca quella di minor valore
    const leadSuitCards = availableCards.filter(
      (card) => card.suit === state.leadSuit
    );
    const lowerValueLeadCards = leadSuitCards.filter(
      (card) => getCardValue(card) < cardValue && card.id !== selectedCard.id
    );

    if (lowerValueLeadCards.length > 0) {
      const betterChoice = lowerValueLeadCards.reduce((lowest, card) =>
        getCardValue(card) < getCardValue(lowest) ? card : lowest
      );
      console.log(
        `[ENFORCE_NO_WASTE] ⚠️ MITIGAZIONE: Uso ${
          betterChoice.rank
        } (valore ${getCardValue(betterChoice)}) invece di ${
          selectedCard.rank
        } (valore ${cardValue})`
      );
      return betterChoice;
    }
  }

  // Ultima risorsa: almeno una carta di minor valore
  if (lowerValueCards.length > 0) {
    const betterChoice = lowerValueCards.reduce((lowest, card) =>
      getCardValue(card) < getCardValue(lowest) ? card : lowest
    );
    console.log(
      `[ENFORCE_NO_WASTE] ⚠️ MITIGAZIONE FINALE: Uso ${
        betterChoice.rank
      } (valore ${getCardValue(betterChoice)}) invece di ${
        selectedCard.rank
      } (valore ${cardValue})`
    );
    return betterChoice;
  }

  // Non ho proprio alternative migliori
  console.log(
    `[ENFORCE_NO_WASTE] 😞 Nessuna alternativa migliore per ${selectedCard.rank}, devo accettare lo spreco`
  );
  return selectedCard;
};
