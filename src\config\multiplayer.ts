/**
 * Multiplayer Configuration Constants
 * Centralizes all multiplayer-related configuration
 */

export const MULTIPLAYER_CONFIG = {
  // Game Settings
  DEFAULT_GAME_TIMER: 60, // seconds per turn
  DEFAULT_VICTORY_POINTS: 31,
  MAX_PLAYERS: 4,
  PLAYERS_PER_TEAM: 2,
  
  // Room Settings
  ROOM_NAME_MIN_LENGTH: 3,
  ROOM_NAME_MAX_LENGTH: 20,
  ROOM_EXPIRY_MINUTES: 10,
  
  // Connection Settings
  HEARTBEAT_INTERVAL: 30000, // 30 seconds
  DISCONNECTION_THRESHOLD: 60000, // 1 minute
  RECONNECT_MAX_ATTEMPTS: 5,
  RECONNECT_BASE_DELAY: 1000, // 1 second
  
  // Timer Settings
  TIMER_WARNING_THRESHOLD: 10, // seconds
  TIMER_CRITICAL_THRESHOLD: 5, // seconds
  COUNTDOWN_DURATION: 5, // seconds before game starts
  
  // Cleanup Settings
  CLEANUP_INTERVAL: 5 * 60 * 1000, // 5 minutes
  DISCONNECTION_CHECK_INTERVAL: 30 * 1000, // 30 seconds
  
  // UI Settings
  ANIMATION_DURATION: 200, // milliseconds
  TOAST_DURATION: 3000, // milliseconds
  
  // Rate Limiting
  MAX_ROOMS_PER_USER_PER_HOUR: 5,
  MIN_HEARTBEAT_INTERVAL: 10000, // 10 seconds
  
  // Team Colors
  TEAM_COLORS: {
    0: {
      name: 'Squadra Gialla',
      bg: 'bg-yellow-100',
      border: 'border-yellow-300',
      button: 'bg-yellow-400',
      buttonHover: 'hover:bg-yellow-200',
      text: 'text-yellow-700',
    },
    1: {
      name: 'Squadra Rossa',
      bg: 'bg-red-100',
      border: 'border-red-300',
      button: 'bg-red-400',
      buttonHover: 'hover:bg-red-200',
      text: 'text-red-700',
    },
  } as const,
  
  // Timer Colors
  TIMER_COLORS: {
    normal: 'text-blue-500 border-blue-500',
    warning: 'text-orange-500 border-orange-500',
    critical: 'text-red-500 border-red-500',
  } as const,
  
  // Connection Status Colors
  CONNECTION_COLORS: {
    online: 'text-green-500',
    offline: 'text-red-500',
    reconnecting: 'text-yellow-500',
  } as const,
} as const;

/**
 * Game Timer Configurations
 */
export const TIMER_OPTIONS = [
  { value: 30, label: '30s', description: 'Veloce' },
  { value: 60, label: '60s', description: 'Standard' },
  { value: 90, label: '90s', description: 'Rilassato' },
] as const;

/**
 * Victory Points Configurations
 */
export const VICTORY_POINTS_OPTIONS = [
  { value: 21, label: '21', description: 'Partita veloce' },
  { value: 31, label: '31', description: 'Standard' },
  { value: 41, label: '41', description: 'Partita lunga' },
] as const;

/**
 * Player Positions
 */
export const PLAYER_POSITIONS = ['north', 'east', 'south', 'west'] as const;

/**
 * Game Session Status
 */
export const SESSION_STATUS = {
  WAITING: 'waiting',
  ACTIVE: 'active',
  COMPLETED: 'completed',
} as const;

/**
 * Realtime Event Types
 */
export const REALTIME_EVENTS = {
  PLAYER_JOINED: 'player_joined',
  PLAYER_LEFT: 'player_left',
  TEAM_CHANGED: 'team_changed',
  GAME_STARTING: 'game_starting',
  GAME_STARTED: 'game_started',
  TURN_TIMER: 'turn_timer',
  PLAYER_DISCONNECTED: 'player_disconnected',
} as const;

/**
 * Error Messages
 */
export const ERROR_MESSAGES = {
  ROOM_NOT_FOUND: 'Stanza non trovata o scaduta',
  ROOM_FULL: 'La stanza è piena (massimo 4 giocatori)',
  ALREADY_IN_ROOM: 'Sei già in questa stanza',
  NOT_HOST: 'Solo l\'host può eseguire questa azione',
  INVALID_ROOM_NAME: 'Nome stanza non valido',
  ROOM_NAME_TOO_SHORT: 'Il nome della stanza deve avere almeno 3 caratteri',
  ROOM_NAME_TOO_LONG: 'Il nome della stanza non può superare i 20 caratteri',
  NEED_4_PLAYERS: 'Servono esattamente 4 giocatori per iniziare',
  NEED_BALANCED_TEAMS: 'Servono 2 giocatori per squadra per iniziare',
  CONNECTION_LOST: 'Connessione persa. Tentativo di riconnessione...',
  RECONNECTION_FAILED: 'Riconnessione fallita. Riprova più tardi.',
  LOGIN_REQUIRED: 'Devi essere loggato per accedere al multiplayer',
} as const;

/**
 * Success Messages
 */
export const SUCCESS_MESSAGES = {
  ROOM_CREATED: 'Stanza creata con successo!',
  JOINED_ROOM: 'Ti sei unito alla stanza!',
  PLAYER_JOINED: 'Un nuovo giocatore si è unito',
  PLAYER_LEFT: 'Un giocatore ha lasciato la partita',
  GAME_STARTING: 'La partita sta per iniziare!',
  RECONNECTED: 'Riconnessione avvenuta con successo',
} as const;

/**
 * Utility function to get team color configuration
 */
export const getTeamColor = (team: 0 | 1) => {
  return MULTIPLAYER_CONFIG.TEAM_COLORS[team];
};

/**
 * Utility function to get timer color based on remaining time
 */
export const getTimerColor = (timeRemaining: number, duration: number) => {
  const percentage = (timeRemaining / duration) * 100;
  
  if (percentage <= (MULTIPLAYER_CONFIG.TIMER_CRITICAL_THRESHOLD / duration) * 100) {
    return MULTIPLAYER_CONFIG.TIMER_COLORS.critical;
  } else if (percentage <= (MULTIPLAYER_CONFIG.TIMER_WARNING_THRESHOLD / duration) * 100) {
    return MULTIPLAYER_CONFIG.TIMER_COLORS.warning;
  } else {
    return MULTIPLAYER_CONFIG.TIMER_COLORS.normal;
  }
};

/**
 * Utility function to validate room name
 */
export const validateRoomName = (name: string): { valid: boolean; error?: string } => {
  if (!name.trim()) {
    return { valid: false, error: ERROR_MESSAGES.INVALID_ROOM_NAME };
  }
  
  if (name.length < MULTIPLAYER_CONFIG.ROOM_NAME_MIN_LENGTH) {
    return { valid: false, error: ERROR_MESSAGES.ROOM_NAME_TOO_SHORT };
  }
  
  if (name.length > MULTIPLAYER_CONFIG.ROOM_NAME_MAX_LENGTH) {
    return { valid: false, error: ERROR_MESSAGES.ROOM_NAME_TOO_LONG };
  }
  
  return { valid: true };
};

/**
 * Utility function to check if teams are balanced
 */
export const areTeamsBalanced = (players: Array<{ team: 0 | 1 }>) => {
  const team0Count = players.filter(p => p.team === 0).length;
  const team1Count = players.filter(p => p.team === 1).length;
  
  return team0Count === MULTIPLAYER_CONFIG.PLAYERS_PER_TEAM && 
         team1Count === MULTIPLAYER_CONFIG.PLAYERS_PER_TEAM;
};

/**
 * Utility function to check if game can start
 */
export const canStartGame = (players: Array<{ team: 0 | 1 }>) => {
  return players.length === MULTIPLAYER_CONFIG.MAX_PLAYERS && areTeamsBalanced(players);
};
