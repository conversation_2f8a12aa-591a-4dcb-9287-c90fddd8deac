import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { X, Users, Trophy, Clock } from "lucide-react";
import ActionButton from "@/components/ui/ActionButton";
import { useAuth } from "@/context/auth-context";
import { useToast } from "@/hooks/use-toast";
import { useAudio } from "@/hooks/useAudio";
import { createPrivateGameSession } from "@/services/onlineGameService";
import { PrivateGameSettings } from "@/types/game";
import {
  validateRoomName,
  VICTORY_POINTS_OPTIONS,
  MULTIPLAYER_CONFIG,
} from "@/config/multiplayer";

interface CreatePrivateGameModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreatePrivateGameModal: React.FC<CreatePrivateGameModalProps> = ({
  isOpen,
  onClose,
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const { playSound } = useAudio();

  const [roomName, setRoomName] = useState("");
  const [victoryPoints, setVictoryPoints] = useState<number>(31);
  const [isCreating, setIsCreating] = useState(false);

  // Timer fisso a 30 secondi
  const gameTimer = MULTIPLAYER_CONFIG.DEFAULT_GAME_TIMER;

  const handleCreateGame = async () => {
    if (!user) {
      toast({
        title: "Errore",
        description: "Devi essere loggato per creare una partita",
        variant: "destructive",
      });
      return;
    }

    const validation = validateRoomName(roomName);
    if (!validation.valid) {
      toast({
        title: "Nome non valido",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);

    try {
      const settings: PrivateGameSettings = {
        roomName: roomName.trim(),
        victoryPoints,
        gameTimer,
        maxPlayers: 4,
      };

      const session = await createPrivateGameSession(
        user.id,
        user.user_metadata?.username ||
          user.email?.split("@")[0] ||
          "Giocatore",
        settings
      );

      playSound("buttonClick");

      // Navigate to lobby
      navigate("/lobby", {
        state: {
          sessionId: session.id,
          isHost: true,
          roomName: roomName.trim(),
        },
      });

      onClose();
    } catch (error) {
      console.error("Error creating private game:", error);
      toast({
        title: "Errore",
        description:
          error instanceof Error
            ? error.message
            : "Errore durante la creazione della stanza",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    playSound("buttonClick");
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl p-6 w-full max-w-md mx-4 shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2
            className="text-2xl font-bold text-romagna-darkWood"
            style={{ fontFamily: "'DynaPuff', cursive" }}
          >
            🔘 Crea Partita Privata
          </h2>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <div className="space-y-6">
          {/* Room Name */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Nome Stanza
            </label>
            <input
              type="text"
              value={roomName}
              onChange={(e) => setRoomName(e.target.value)}
              placeholder="es. bar_savio, cesenatico01"
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
              maxLength={20}
              disabled={isCreating}
            />
            <p className="text-xs text-gray-500 mt-1">
              {roomName.length}/20 caratteri
            </p>
          </div>

          {/* Victory Points */}
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              <Trophy className="inline h-4 w-4 mr-1" />
              Punti Vittoria
            </label>
            <div className="flex gap-2">
              {VICTORY_POINTS_OPTIONS.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setVictoryPoints(option.value)}
                  disabled={isCreating}
                  className={`flex-1 py-2 px-3 rounded-lg font-semibold transition-all ${
                    victoryPoints === option.value
                      ? "bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                  title={option.description}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          {/* Timer Info (Fixed) */}

          {/* Actions */}
          <div className="flex gap-3">
            <ActionButton
              onClick={handleClose}
              disabled={isCreating}
              className="flex-1 py-3 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-xl font-semibold transition-all duration-200"
            >
              Annulla
            </ActionButton>
            <ActionButton
              onClick={handleCreateGame}
              disabled={isCreating || !roomName.trim()}
              className="flex-1 py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-xl font-bold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isCreating ? "Creando..." : "Crea Stanza"}
            </ActionButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreatePrivateGameModal;
