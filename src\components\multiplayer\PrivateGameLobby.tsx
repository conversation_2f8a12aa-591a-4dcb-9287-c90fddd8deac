import React, { useEffect, useState, useCallback } from "react";
import { <PERSON>, Users, Trophy, Clock, ArrowLeft, Play } from "lucide-react";
import ActionButton from "@/components/ui/ActionButton";
import { useToast } from "@/hooks/use-toast";
import { useAudio } from "@/hooks/useAudio";
import { GameSession, GamePlayer, RealtimeGameEvent } from "@/types/game";
import {
  subscribeToGameSession,
  updatePlayerTeam,
  startPrivateGame,
  leaveGameSession,
} from "@/services/onlineGameService";
import { RealtimeChannel } from "@supabase/supabase-js";

interface PrivateGameLobbyProps {
  session: GameSession;
  currentUserId: string;
  isHost: boolean;
  roomName: string;
  onSessionUpdate: (session: GameSession) => void;
  onGameStart: () => void;
  onLeaveLobby: () => void;
}

const PrivateGameLobby: React.FC<PrivateGameLobbyProps> = ({
  session,
  currentUserId,
  isHost,
  roomName,
  onSessionUpdate,
  onGameStart,
  onLeaveLobby,
}) => {
  const { toast } = useToast();
  const { playSound } = useAudio();

  const [countdown, setCountdown] = useState<number | null>(null);
  const [isStarting, setIsStarting] = useState(false);
  const [realtimeChannel, setRealtimeChannel] =
    useState<RealtimeChannel | null>(null);

  // Get current player
  const currentPlayer = session.players.find(
    (p) => p.user_id === currentUserId
  );

  // Check if game can start (4 players, 2 per team)
  const team0Players = session.players.filter((p) => p.team === 0);
  const team1Players = session.players.filter((p) => p.team === 1);
  const canStart =
    session.players.length === 4 &&
    team0Players.length === 2 &&
    team1Players.length === 2;

  // Handle realtime updates
  const handleRealtimeUpdate = useCallback(
    (event: RealtimeGameEvent) => {
      console.log("🔄 Realtime update:", event);

      switch (event.type) {
        case "player_joined":
          toast({
            title: "Nuovo giocatore",
            description: `${event.payload.username} si è unito alla partita`,
          });
          playSound("playerJoin");
          break;

        case "player_left":
          toast({
            title: "Giocatore uscito",
            description: `Un giocatore ha lasciato la partita`,
          });
          playSound("playerLeave");
          break;

        case "game_started":
          toast({
            title: "Partita iniziata!",
            description: "La partita sta per iniziare...",
          });
          playSound("gameStart");
          onGameStart();
          break;
      }

      // Reload session data after any update
      // This would typically be handled by the parent component
    },
    [toast, playSound, onGameStart]
  );

  // Subscribe to realtime updates
  useEffect(() => {
    if (session.id) {
      const channel = subscribeToGameSession(session.id, handleRealtimeUpdate);
      setRealtimeChannel(channel);

      return () => {
        if (channel) {
          channel.unsubscribe();
        }
      };
    }
  }, [session.id, handleRealtimeUpdate]);

  // Handle team change
  const handleTeamChange = async (newTeam: 0 | 1) => {
    if (!currentPlayer) return;

    try {
      await updatePlayerTeam(session.id, currentUserId, newTeam);
      playSound("buttonClick");

      // Update local state optimistically
      const updatedPlayers = session.players.map((p) =>
        p.user_id === currentUserId ? { ...p, team: newTeam } : p
      );
      onSessionUpdate({ ...session, players: updatedPlayers });
    } catch (error) {
      console.error("Error updating team:", error);
      toast({
        title: "Errore",
        description: "Impossibile cambiare squadra",
        variant: "destructive",
      });
    }
  };

  // Handle game start
  const handleStartGame = async () => {
    if (!isHost || !canStart) return;

    setIsStarting(true);

    try {
      // Start countdown
      setCountdown(5);
      const countdownInterval = setInterval(() => {
        setCountdown((prev) => {
          if (prev === null || prev <= 1) {
            clearInterval(countdownInterval);
            return null;
          }
          return prev - 1;
        });
      }, 1000);

      // Wait for countdown
      await new Promise((resolve) => setTimeout(resolve, 5000));

      // Start the game
      await startPrivateGame(session.id, currentUserId);
    } catch (error) {
      console.error("Error starting game:", error);
      toast({
        title: "Errore",
        description:
          error instanceof Error
            ? error.message
            : "Impossibile avviare la partita",
        variant: "destructive",
      });
      setCountdown(null);
    } finally {
      setIsStarting(false);
    }
  };

  // Handle leave lobby
  const handleLeaveLobby = async () => {
    try {
      await leaveGameSession(session.id, currentUserId);
      playSound("buttonClick");
      onLeaveLobby();
    } catch (error) {
      console.error("Error leaving lobby:", error);
      // Still navigate away even if there's an error
      onLeaveLobby();
    }
  };

  // Render compact player card
  const renderPlayerCard = (player: GamePlayer, index: number) => {
    const isCurrentUser = player.user_id === currentUserId;
    const teamColor =
      player.team === 0
        ? "bg-yellow-50 border-yellow-400"
        : "bg-red-50 border-red-400";
    const teamDot = player.team === 0 ? "bg-yellow-500" : "bg-red-500";

    return (
      <div
        key={player.id}
        className={`p-3 rounded-lg border ${teamColor} ${
          isCurrentUser ? "ring-1 ring-blue-400" : ""
        } mb-2`}
      >
        <div className="flex items-center gap-2">
          {/* Team Dot */}
          <div className={`w-3 h-3 rounded-full ${teamDot}`}></div>

          {/* Player Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-1">
              <span className="font-semibold text-gray-800 text-sm truncate">
                {player.username}
              </span>
              {player.user_id === session.host_id && (
                <Crown className="h-3 w-3 text-yellow-600 flex-shrink-0" />
              )}
              {isCurrentUser && (
                <span className="text-xs bg-blue-500 text-white px-1 py-0.5 rounded flex-shrink-0">
                  Tu
                </span>
              )}
            </div>
          </div>

          {/* Team Switch (only for current user) */}
          {isCurrentUser && (
            <div className="flex gap-1 flex-shrink-0">
              <button
                onClick={() => handleTeamChange(0)}
                disabled={isStarting}
                className={`w-6 h-6 rounded-full border transition-all ${
                  player.team === 0
                    ? "bg-yellow-400 border-yellow-600"
                    : "bg-yellow-100 border-yellow-300 hover:bg-yellow-200"
                }`}
                title="Gialla"
              />
              <button
                onClick={() => handleTeamChange(1)}
                disabled={isStarting}
                className={`w-6 h-6 rounded-full border transition-all ${
                  player.team === 1
                    ? "bg-red-400 border-red-600"
                    : "bg-red-100 border-red-300 hover:bg-red-200"
                }`}
                title="Rossa"
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-romagna-wood to-romagna-darkWood p-3">
      {/* Compact Header */}
      <div className="bg-white/95 backdrop-blur-sm rounded-xl p-4 mb-4 shadow-lg">
        <div className="flex items-center justify-between mb-3">
          <button
            onClick={handleLeaveLobby}
            disabled={isStarting}
            className="flex items-center gap-1 text-gray-600 hover:text-gray-800 transition-colors text-sm"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Esci</span>
          </button>

          <div className="text-center flex-1 mx-3">
            <h1
              className="text-lg font-bold text-romagna-darkWood truncate"
              style={{ fontFamily: "'DynaPuff', cursive" }}
            >
              🎮 {roomName}
            </h1>
          </div>

          <div className="text-xs text-gray-600 text-right">
            <div>{session.victory_points || 31}pt</div>
          </div>
        </div>

        {/* Compact Status */}
        {countdown !== null ? (
          <div className="text-center py-4">
            <div className="text-4xl font-bold text-green-600 mb-1">
              {countdown}
            </div>
            <p className="text-sm font-semibold text-gray-800">Si gioca!</p>
          </div>
        ) : (
          <div className="text-center">
            {canStart ? (
              <div className="text-green-600 font-semibold text-sm">
                ✅ Pronti per iniziare!
              </div>
            ) : (
              <div className="text-amber-600 font-semibold text-sm">
                ⏳ Aspettando {4 - session.players.length} giocatori
                {session.players.length === 4 && (
                  <div className="text-xs mt-1">Serve 2 per squadra</div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Compact Players List */}
      <div className="bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg">
        <div className="flex items-end justify-between mb-3">
          <h2 className="text-base font-bold text-gray-800">Giocatori</h2>
          <div className="text-gray-700 text-sm">
            {session.players.length}/4
          </div>
        </div>

        <div className="space-y-0">
          {session.players.map((player, index) =>
            renderPlayerCard(player, index)
          )}

          {/* Empty slots */}
          {Array.from({ length: 4 - session.players.length }).map(
            (_, index) => (
              <div
                key={`empty-${index}`}
                className="p-3 rounded-lg border border-dashed border-gray-300 bg-gray-50 mb-2"
              >
                <div className="flex items-center justify-center text-gray-400 text-sm">
                  <Users className="h-4 w-4 mr-2" />
                  <span>Aspettando giocatore...</span>
                </div>
              </div>
            )
          )}
        </div>

        {/* Host Controls */}
        {isHost && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <ActionButton
              onClick={handleStartGame}
              disabled={!canStart || isStarting || countdown !== null}
              className={`w-full py-3 text-lg font-bold rounded-xl shadow-lg transition-all duration-200 ${
                canStart && !isStarting
                  ? "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white hover:shadow-xl"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              <Play className="h-5 w-5 mr-2 inline" />
              {isStarting ? "Avvio..." : "Avvia Partita"}
            </ActionButton>

            {!canStart && (
              <p className="text-center text-xs text-gray-500 mt-2">
                Servono 4 giocatori (2 per squadra)
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PrivateGameLobby;
