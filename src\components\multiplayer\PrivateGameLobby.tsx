import React, { useEffect, useState, useCallback } from 'react';
import { Crown, Users, Trophy, Clock, ArrowLeft, Play } from 'lucide-react';
import ActionButton from '@/components/ui/ActionButton';
import { useToast } from '@/hooks/use-toast';
import { useAudio } from '@/hooks/useAudio';
import { 
  GameSession, 
  GamePlayer, 
  RealtimeGameEvent 
} from '@/types/game';
import { 
  subscribeToGameSession, 
  updatePlayerTeam, 
  startPrivateGame,
  leaveGameSession 
} from '@/services/onlineGameService';
import { RealtimeChannel } from '@supabase/supabase-js';

interface PrivateGameLobbyProps {
  session: GameSession;
  currentUserId: string;
  isHost: boolean;
  roomName: string;
  onSessionUpdate: (session: GameSession) => void;
  onGameStart: () => void;
  onLeaveLobby: () => void;
}

const PrivateGameLobby: React.FC<PrivateGameLobbyProps> = ({
  session,
  currentUserId,
  isHost,
  roomName,
  onSessionUpdate,
  onGameStart,
  onLeaveLobby,
}) => {
  const { toast } = useToast();
  const { playSound } = useAudio();

  const [countdown, setCountdown] = useState<number | null>(null);
  const [isStarting, setIsStarting] = useState(false);
  const [realtimeChannel, setRealtimeChannel] = useState<RealtimeChannel | null>(null);

  // Get current player
  const currentPlayer = session.players.find(p => p.user_id === currentUserId);
  
  // Check if game can start (4 players, 2 per team)
  const team0Players = session.players.filter(p => p.team === 0);
  const team1Players = session.players.filter(p => p.team === 1);
  const canStart = session.players.length === 4 && team0Players.length === 2 && team1Players.length === 2;

  // Handle realtime updates
  const handleRealtimeUpdate = useCallback((event: RealtimeGameEvent) => {
    console.log('🔄 Realtime update:', event);
    
    switch (event.type) {
      case 'player_joined':
        toast({
          title: "Nuovo giocatore",
          description: `${event.payload.username} si è unito alla partita`,
        });
        playSound('playerJoin');
        break;
        
      case 'player_left':
        toast({
          title: "Giocatore uscito",
          description: `Un giocatore ha lasciato la partita`,
        });
        playSound('playerLeave');
        break;
        
      case 'game_started':
        toast({
          title: "Partita iniziata!",
          description: "La partita sta per iniziare...",
        });
        playSound('gameStart');
        onGameStart();
        break;
    }
    
    // Reload session data after any update
    // This would typically be handled by the parent component
  }, [toast, playSound, onGameStart]);

  // Subscribe to realtime updates
  useEffect(() => {
    if (session.id) {
      const channel = subscribeToGameSession(session.id, handleRealtimeUpdate);
      setRealtimeChannel(channel);

      return () => {
        if (channel) {
          channel.unsubscribe();
        }
      };
    }
  }, [session.id, handleRealtimeUpdate]);

  // Handle team change
  const handleTeamChange = async (newTeam: 0 | 1) => {
    if (!currentPlayer) return;

    try {
      await updatePlayerTeam(session.id, currentUserId, newTeam);
      playSound('buttonClick');
      
      // Update local state optimistically
      const updatedPlayers = session.players.map(p => 
        p.user_id === currentUserId ? { ...p, team: newTeam } : p
      );
      onSessionUpdate({ ...session, players: updatedPlayers });
      
    } catch (error) {
      console.error('Error updating team:', error);
      toast({
        title: "Errore",
        description: "Impossibile cambiare squadra",
        variant: "destructive",
      });
    }
  };

  // Handle game start
  const handleStartGame = async () => {
    if (!isHost || !canStart) return;

    setIsStarting(true);
    
    try {
      // Start countdown
      setCountdown(5);
      const countdownInterval = setInterval(() => {
        setCountdown(prev => {
          if (prev === null || prev <= 1) {
            clearInterval(countdownInterval);
            return null;
          }
          return prev - 1;
        });
      }, 1000);

      // Wait for countdown
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Start the game
      await startPrivateGame(session.id, currentUserId);
      
    } catch (error) {
      console.error('Error starting game:', error);
      toast({
        title: "Errore",
        description: error instanceof Error ? error.message : "Impossibile avviare la partita",
        variant: "destructive",
      });
      setCountdown(null);
    } finally {
      setIsStarting(false);
    }
  };

  // Handle leave lobby
  const handleLeaveLobby = async () => {
    try {
      await leaveGameSession(session.id, currentUserId);
      playSound('buttonClick');
      onLeaveLobby();
    } catch (error) {
      console.error('Error leaving lobby:', error);
      // Still navigate away even if there's an error
      onLeaveLobby();
    }
  };

  // Render player card
  const renderPlayerCard = (player: GamePlayer, index: number) => {
    const isCurrentUser = player.user_id === currentUserId;
    const teamColor = player.team === 0 ? 'bg-yellow-100 border-yellow-300' : 'bg-red-100 border-red-300';
    const teamName = player.team === 0 ? 'Squadra Gialla' : 'Squadra Rossa';

    return (
      <div key={player.id} className={`p-4 rounded-xl border-2 ${teamColor} ${isCurrentUser ? 'ring-2 ring-blue-500' : ''}`}>
        <div className="flex items-center gap-3">
          {/* Player Avatar */}
          <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
            <Users className="h-6 w-6 text-gray-600" />
          </div>
          
          {/* Player Info */}
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span className="font-bold text-gray-800">{player.username}</span>
              {player.user_id === session.host_id && (
                <Crown className="h-4 w-4 text-yellow-600" />
              )}
              {isCurrentUser && (
                <span className="text-xs bg-blue-500 text-white px-2 py-1 rounded-full">Tu</span>
              )}
            </div>
            <p className="text-sm text-gray-600">{teamName}</p>
            {player.level && (
              <p className="text-xs text-gray-500">Livello {player.level}</p>
            )}
          </div>

          {/* Team Switch (only for current user) */}
          {isCurrentUser && (
            <div className="flex gap-1">
              <button
                onClick={() => handleTeamChange(0)}
                disabled={isStarting}
                className={`w-8 h-8 rounded-full border-2 transition-all ${
                  player.team === 0 
                    ? 'bg-yellow-400 border-yellow-600' 
                    : 'bg-yellow-100 border-yellow-300 hover:bg-yellow-200'
                }`}
                title="Squadra Gialla"
              />
              <button
                onClick={() => handleTeamChange(1)}
                disabled={isStarting}
                className={`w-8 h-8 rounded-full border-2 transition-all ${
                  player.team === 1 
                    ? 'bg-red-400 border-red-600' 
                    : 'bg-red-100 border-red-300 hover:bg-red-200'
                }`}
                title="Squadra Rossa"
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen p-4 flex flex-col">
      {/* Header */}
      <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 mb-6 shadow-xl">
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={handleLeaveLobby}
            disabled={isStarting}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Esci</span>
          </button>
          
          <div className="text-center">
            <h1 
              className="text-2xl font-bold text-romagna-darkWood"
              style={{ fontFamily: "'DynaPuff', cursive" }}
            >
              🎮 {roomName}
            </h1>
            <p className="text-sm text-gray-600">
              {session.players.length}/4 giocatori
            </p>
          </div>

          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Trophy className="h-4 w-4" />
              <span>{session.victory_points || 31} punti</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>{session.game_timer || 60}s</span>
            </div>
          </div>
        </div>

        {/* Game Status */}
        {countdown !== null ? (
          <div className="text-center py-8">
            <div className="text-6xl font-bold text-green-600 mb-2">{countdown}</div>
            <p className="text-xl font-semibold text-gray-800">Partita pronta! Si gioca!</p>
          </div>
        ) : (
          <div className="text-center">
            {canStart ? (
              <div className="text-green-600 font-semibold">
                ✅ Tutti i giocatori sono pronti!
              </div>
            ) : (
              <div className="text-amber-600 font-semibold">
                ⏳ In attesa di {4 - session.players.length} giocatori...
                {session.players.length === 4 && (
                  <div className="text-sm mt-1">
                    Assicurati che ci siano 2 giocatori per squadra
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Players List */}
      <div className="flex-1 bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-xl">
        <h2 className="text-xl font-bold text-gray-800 mb-4">Giocatori</h2>
        
        <div className="grid gap-4">
          {session.players.map((player, index) => renderPlayerCard(player, index))}
          
          {/* Empty slots */}
          {Array.from({ length: 4 - session.players.length }).map((_, index) => (
            <div key={`empty-${index}`} className="p-4 rounded-xl border-2 border-dashed border-gray-300 bg-gray-50">
              <div className="flex items-center justify-center h-16 text-gray-400">
                <Users className="h-6 w-6 mr-2" />
                <span>In attesa di un giocatore...</span>
              </div>
            </div>
          ))}
        </div>

        {/* Host Controls */}
        {isHost && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <ActionButton
              onClick={handleStartGame}
              disabled={!canStart || isStarting || countdown !== null}
              className={`w-full py-4 text-xl font-bold rounded-xl shadow-lg transition-all duration-200 ${
                canStart && !isStarting
                  ? 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white hover:shadow-xl'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              <Play className="h-6 w-6 mr-2 inline" />
              {isStarting ? 'Avvio in corso...' : 'Avvia Partita'}
            </ActionButton>
            
            {!canStart && (
              <p className="text-center text-sm text-gray-500 mt-2">
                Servono 4 giocatori con 2 per squadra per iniziare
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PrivateGameLobby;
