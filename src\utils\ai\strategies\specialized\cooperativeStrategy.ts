/**
 * 🤝 STRATEGIA COLLABORATIVA AVANZATA - Team-First AI
 *
 * PRINCIPI FONDAMENTALI PER MASSIMIZZARE I PUNTI DEL TEAM:
 * 1. OGNI PUNTO CONTA: Se puoi prendere con carte di punti, FALL<PERSON> sempre (tranne briscole strategiche)
 * 2. ESEMPIO: Ultimo in coppe con Asso di coppe → PRENDI SEMPRE (1 punto assicurato al team)
 * 3. Compagno vincente → VALORIZZA con tutte le carte di punti che hai
 * 4. Avversari vincenti con 1+ punti → RECUPERA SEMPRE, mai regalare punti
 * 5. Avversari vincenti con 0 punti → Proteggi con carte da 0 punti
 * 6. MAI dare punti al team avversario, sempre massimizzare punti del proprio team
 */

import { GameState } from "../../../game/gameLogic";
import { Card } from "../../../game/cardUtils";
import { Card<PERSON><PERSON>ory, TeammateAnalysis, AIStrategy } from "../../types";
import {
  getCardStrengthScore,
  isGoodForTeammate,
  getTeammateCards,
  isTeammateCardUnbeatable,
} from "../../utils/cardUtils";
import { evaluateCardStrategicValue } from "../../utils/evaluators";
import { CardEvaluator } from "../../core/cardEvaluator";

/**
 * 🎯 Analizza la posizione del compagno nella presa corrente
 */
export const analyzeTeammatePosition = (
  state: GameState,
  playerIndex: number
): TeammateAnalysis => {
  if (!state.currentTrick || state.currentTrick.length === 0) {
    return {
      teammateIsWinning: false,
      teammatePosition: -1,
      winningCard: null,
      shouldSupport: false,
    };
  }

  const playerTeam = state.players[playerIndex].team;
  const leadPlayerIndex = state.leadPlayer ?? 0;
  let teammatePosition = -1;

  // Trova la posizione del compagno nella presa attuale
  for (let i = 0; i < state.currentTrick.length; i++) {
    const cardPlayerIndex = (leadPlayerIndex + i) % 4;
    if (
      state.players[cardPlayerIndex].team === playerTeam &&
      cardPlayerIndex !== playerIndex
    ) {
      teammatePosition = i;
      break;
    }
  }

  // 🎯 Determina chi sta attualmente vincendo con logica corretta
  let winnerIndex = 0;
  let winningCard = state.currentTrick[0];

  // Inizializza con il valore corretto della prima carta
  let highestValue = 0;
  if (winningCard.suit === state.trumpSuit) {
    highestValue = 1000 + getCardStrengthScore(winningCard);
  } else if (winningCard.suit === state.leadSuit) {
    highestValue = getCardStrengthScore(winningCard);
  }

  // Controlla le carte rimanenti (inizia da i=1)
  for (let i = 1; i < state.currentTrick.length; i++) {
    const card = state.currentTrick[i];

    let cardWinValue = 0;
    if (card.suit === state.trumpSuit) {
      cardWinValue = 1000 + getCardStrengthScore(card);
    } else if (card.suit === state.leadSuit) {
      cardWinValue = getCardStrengthScore(card);
    }

    if (cardWinValue > highestValue) {
      highestValue = cardWinValue;
      winnerIndex = i;
      winningCard = card;
    }
  }

  // Determina il giocatore vincente
  const currentWinner = (leadPlayerIndex + winnerIndex) % 4;
  const winnerTeam =
    currentWinner >= 0 ? state.players[currentWinner].team : -1;
  const teammateIsWinning =
    winnerTeam === playerTeam && currentWinner !== playerIndex;
  return {
    teammateIsWinning,
    teammatePosition,
    winningCard,
    shouldSupport: teammateIsWinning && teammatePosition >= 0,
  };
};

/**
 * 🎯 VERIFICA SE UNA CARTA PUÒ ESSERE GIOCATA SICURAMENTE COME PRIMA CARTA
 * Regole per ASSO:
 * - Se ho la maraffa (2 e 3 dello stesso seme) → SEMPRE sicuro
 * - Se 2 e 3 dello stesso seme sono già usciti → SEMPRE sicuro
 * - Altrimenti → NON sicuro
 * Regole per RE:
 * - Se ho 3, 2 e Asso dello stesso seme → SEMPRE sicuro
 * - Se 3, 2 e Asso dello stesso seme sono già usciti → SEMPRE sicuro
 * - Altrimenti → NON sicuro
 */
const canPlayCardSafely = (
  card: Card,
  availableCards: Card[],
  playedCards: Card[]
): boolean => {
  const sameSuit = card.suit;

  if (card.rank === "A") {
    // Verifica se ho la maraffa (2 e 3 dello stesso seme)
    const hasTwo = availableCards.some(
      (card) => card.suit === sameSuit && card.rank === "2"
    );
    const hasThree = availableCards.some(
      (card) => card.suit === sameSuit && card.rank === "3"
    );

    if (hasTwo && hasThree) {
      console.log(
        `[ASSO SICURO] 🎯 Ho la maraffa in ${sameSuit} - Asso sicuro!`
      );
      return true;
    }
  } else if (card.rank === "K") {
    // Per il Re: deve avere 3, 2 e Asso in mano o già usciti
    const hasAce = availableCards.some(
      (card) => card.suit === sameSuit && card.rank === "A"
    );
    const hasTwo = availableCards.some(
      (card) => card.suit === sameSuit && card.rank === "2"
    );
    const hasThree = availableCards.some(
      (card) => card.suit === sameSuit && card.rank === "3"
    );

    if (hasAce && hasTwo && hasThree) {
      console.log(
        `[RE SICURO] 🎯 Ho tutte le carte superiori in ${sameSuit} - Re sicuro!`
      );
      return true;
    }
  }

  if (card.rank === "A") {
    // Verifica se 2 e 3 dello stesso seme sono già usciti
    const twoPlayed = playedCards.some(
      (card) => card.suit === sameSuit && card.rank === "2"
    );
    const threePlayed = playedCards.some(
      (card) => card.suit === sameSuit && card.rank === "3"
    );

    if (twoPlayed && threePlayed) {
      console.log(
        `[ASSO SICURO] 🎯 2 e 3 di ${sameSuit} già usciti - Asso sicuro!`
      );
      return true;
    }

    console.log(
      `[ASSO NON SICURO] ⚠️ Asso di ${sameSuit} non sicuro - 2 e 3 ancora in gioco`
    );
    return false;
  } else if (card.rank === "K") {
    // Verifica se 3, 2 e Asso dello stesso seme sono già usciti
    const acePlayed = playedCards.some(
      (card) => card.suit === sameSuit && card.rank === "A"
    );
    const twoPlayed = playedCards.some(
      (card) => card.suit === sameSuit && card.rank === "2"
    );
    const threePlayed = playedCards.some(
      (card) => card.suit === sameSuit && card.rank === "3"
    );

    if (acePlayed && twoPlayed && threePlayed) {
      console.log(
        `[RE SICURO] 🎯 A, 2 e 3 di ${sameSuit} già usciti - Re sicuro!`
      );
      return true;
    }

    console.log(
      `[RE NON SICURO] ⚠️ Re di ${sameSuit} non sicuro - A, 2 o 3 ancora in gioco`
    );
    return false;
  }

  return false;
};

/**
 * 🎯 FUNZIONE CHIAVE: Identifica quando possiamo vincere sicuramente con carte di punti
 * Esempio: Ultimo in coppe con Asso di coppe → PRENDI SEMPRE
 * AGGIORNATO: Verifica sicurezza asso come prima carta
 */
const canSecurePointsForTeam = (
  availableCards: Card[],
  state: GameState,
  evaluator: CardEvaluator,
  playedCards: Card[] = []
): { canWin: boolean; bestCard: Card | null; pointsSecured: number } => {
  // 🚨 REGOLA ASSOLUTA: Se ci sono punti sul tavolo e posso vincere, VINCI SEMPRE!
  const currentTrickValue =
    state.currentTrick?.reduce(
      (sum, card) => sum + evaluator.getCardValue(card),
      0
    ) || 0;

  if (currentTrickValue >= 1) {
    const winningCards = availableCards.filter((card) =>
      evaluator.canWinCurrentTrick(
        card,
        state.currentTrick || [],
        state.currentTrick?.[0]?.suit || null,
        state.trumpSuit
      )
    );

    if (winningCards.length > 0) {
      // Con punti sul tavolo, trova la carta vincente più debole possibile
      winningCards.sort((a, b) => {
        const valueA = evaluator.getCardValue(a);
        const valueB = evaluator.getCardValue(b);

        // Prima: carte senza valore (4,5,6,7)
        if (valueA === 0 && valueB > 0) return -1;
        if (valueB === 0 && valueA > 0) return 1;

        // Poi: per forza (più debole prima)
        return evaluator.getCardOrder(a) - evaluator.getCardOrder(b);
      });

      const bestCard = winningCards[0];
      const pointsSecured =
        currentTrickValue + evaluator.getCardValue(bestCard);

      console.log(
        `[COOPERATIVA] 🚨 REGOLA ASSOLUTA: ${currentTrickValue.toFixed(
          1
        )} punti sul tavolo - PRENDO con ${bestCard.rank} di ${bestCard.suit}!`
      );

      return { canWin: true, bestCard, pointsSecured };
    }
  }

  const winningCards = availableCards.filter((card) =>
    evaluator.canWinCurrentTrick(
      card,
      state.currentTrick || [],
      state.currentTrick?.[0]?.suit || null,
      state.trumpSuit
    )
  );

  if (winningCards.length === 0) {
    return { canWin: false, bestCard: null, pointsSecured: 0 };
  }

  // Priorità alle carte NON-briscola con punti (esempio: Asso di coppe)
  const nonTrumpWinners = winningCards.filter(
    (card) => card.suit !== state.trumpSuit && evaluator.getCardValue(card) > 0
  );

  if (nonTrumpWinners.length > 0) {
    // 🎯 VERIFICA SICUREZZA ASSO COME PRIMA CARTA
    const isFirstOfTrick =
      !state.currentTrick || state.currentTrick.length === 0;

    if (isFirstOfTrick) {
      // Se siamo primi del turno, verifica sicurezza delle carte di punti
      const safeCards = nonTrumpWinners.filter((card) => {
        if (card.rank === "A" || card.rank === "K") {
          return canPlayCardSafely(card, availableCards, playedCards);
        }
        return true; // Altre carte di punti sono sempre OK
      });

      if (safeCards.length > 0) {
        // Ordina per valore decrescente - usa la carta di maggior valore sicura
        safeCards.sort(
          (a, b) => evaluator.getCardValue(b) - evaluator.getCardValue(a)
        );
        const bestCard = safeCards[0];
        const pointsSecured =
          currentTrickValue + evaluator.getCardValue(bestCard);

        console.log(
          `[CARTA VERIFICATA] ✅ Gioco ${bestCard.rank} di ${bestCard.suit} come prima carta - SICURO!`
        );
        return { canWin: true, bestCard, pointsSecured };
      } else {
        console.log(
          `[CARTE BLOCCATE] ❌ Nessuna carta di punti sicura come prima carta - salto strategia punti`
        );
        return { canWin: false, bestCard: null, pointsSecured: 0 };
      }
    } else {
      // Se non siamo primi, la logica originale è OK
      nonTrumpWinners.sort(
        (a, b) => evaluator.getCardValue(b) - evaluator.getCardValue(a)
      );
      const bestCard = nonTrumpWinners[0];
      const pointsSecured =
        currentTrickValue + evaluator.getCardValue(bestCard);

      return { canWin: true, bestCard, pointsSecured };
    }
  }

  // Se solo briscole possono vincere, valuta strategicamente
  const trumpWinners = winningCards.filter(
    (card) => card.suit === state.trumpSuit
  );

  if (trumpWinners.length > 0) {
    // Per le briscole, usa quella di minor valore strategico che può vincere
    trumpWinners.sort(
      (a, b) => evaluator.getCardValue(a) - evaluator.getCardValue(b)
    );
    const bestCard = trumpWinners[0];
    const pointsSecured = currentTrickValue + evaluator.getCardValue(bestCard);

    // Solo se vale la pena (abbastanza punti in gioco)
    if (currentTrickValue >= 2 || state.trickNumber === 10) {
      return { canWin: true, bestCard, pointsSecured };
    }
  }

  return { canWin: false, bestCard: null, pointsSecured: 0 };
};

/**
 * 🔥 STRATEGIA COLLABORATIVA AVANZATA - TEAM-FIRST
 */
export const getCooperativeStrategy = (
  state: GameState,
  playerIndex: number,
  availableCards: Card[],
  memory: CardMemory
): AIStrategy => {
  console.log(
    `[COLLABORATIVA AVANZATA] 🤝 Avvio strategia TEAM-FIRST per giocatore ${playerIndex}`
  );

  const evaluator = new CardEvaluator();
  const teammateAnalysis = analyzeTeammatePosition(state, playerIndex);
  const isLastPlayer = state.currentTrick?.length === 3;
  const trickValue =
    state.currentTrick?.reduce(
      (sum, card) => sum + evaluator.getCardValue(card),
      0
    ) || 0;
  const trickNumber = state.trickNumber ?? 1;
  const isEndGame = trickNumber >= 8;
  console.log(
    `[COLLABORATIVA AVANZATA] 📊 Posizione: ${
      isLastPlayer ? "ULTIMO" : "NON ULTIMO"
    }, Presa: ${trickValue.toFixed(1)} punti`
  );
  // 🚨 CONTROLLO ANTI-SPRECO BRISCOLE PRIORITARIO: Se compagno sta vincendo, VIETATO sprecare briscole
  if (
    teammateAnalysis.teammateIsWinning &&
    state.currentTrick &&
    state.currentTrick.length > 0
  ) {
    const leadSuit = state.currentTrick[0]?.suit;
    const canFollowSuit = leadSuit
      ? availableCards.some((card) => card.suit === leadSuit)
      : false;

    console.log(
      `[COLLABORATIVA AVANZATA] 🚨 RILEVATO COMPAGNO VINCENTE: ${teammateAnalysis.winningCard?.rank} di ${teammateAnalysis.winningCard?.suit}`
    );
    console.log(
      `[COLLABORATIVA AVANZATA] 📊 Presa attuale: ${trickValue.toFixed(
        1
      )} punti, Posso seguire seme: ${canFollowSuit ? "SÌ" : "NO"}`
    );

    // REGOLA FERREA: Se compagno sta vincendo, priorità assoluta a carte non-briscola
    const trumpCards = availableCards.filter(
      (card) => card.suit === state.trumpSuit
    );
    const nonTrumpCards = availableCards.filter(
      (card) => card.suit !== state.trumpSuit
    );

    // Se devo seguire il seme, usa carte del seme richiesto (mai briscole se non necessario)
    if (canFollowSuit) {
      const leadSuitCards = availableCards.filter(
        (card) => card.suit === leadSuit
      );

      if (leadSuitCards.length > 0) {
        console.log(
          `[COLLABORATIVA AVANZATA] ✅ SEGUO SEME: ${leadSuitCards.length} carte di ${leadSuit} disponibili`
        );

        // Dai punti al compagno con le migliori carte del seme (solo Asso, Re, Cavallo, Fante)
        const pointCardsOfSuit = leadSuitCards.filter((card) =>
          isGoodForTeammate(card)
        );

        if (pointCardsOfSuit.length > 0) {
          // Ordina per dare i punti migliori: Asso > Re > Cavallo > Fante (esclude 2 e 3)
          pointCardsOfSuit.sort((a, b) => {
            const valueOrder: Record<string, number> = {
              A: 1,
              K: 2,
              H: 3,
              J: 4,
            };
            return (valueOrder[a.rank] || 999) - (valueOrder[b.rank] || 999);
          });

          const bestPointCard = pointCardsOfSuit[0];
          console.log(
            `[COLLABORATIVA AVANZATA] 🎯 REGALO PUNTI SEGUENDO SEME: ${
              bestPointCard.rank
            } di ${bestPointCard.suit} (${evaluator.getCardValue(
              bestPointCard
            )} punti)`
          );

          return {
            strategy: "support",
            recommendedCard: bestPointCard,
            reason: `🎁 SUPPORTO OTTIMALE: Compagno vincente → regalo ${
              bestPointCard.rank
            } del seme richiesto (${evaluator.getCardValue(
              bestPointCard
            )} punti)`,
          };
        }

        // Se non ho punti nel seme, usa la carta più bassa del seme
        leadSuitCards.sort(
          (a, b) =>
            evaluator.getDiscardOrderScore(a) -
            evaluator.getDiscardOrderScore(b)
        );

        console.log(
          `[COLLABORATIVA AVANZATA] 🎯 SCARTO SEGUENDO SEME: ${leadSuitCards[0].rank} di ${leadSuitCards[0].suit}`
        );

        return {
          strategy: "support",
          recommendedCard: leadSuitCards[0],
          reason: `🎯 SUPPORTO PULITO: Compagno vincente → scarto ${leadSuitCards[0].rank} seguendo il seme`,
        };
      }
    }

    // Se NON posso seguire il seme, VIETATO assolutamente usare briscole se ho alternative
    if (!canFollowSuit && nonTrumpCards.length > 0) {
      console.log(
        `[COLLABORATIVA AVANZATA] 🛡️ DIVIETO BRISCOLE: ${nonTrumpCards.length} carte non-briscola disponibili, ${trumpCards.length} briscole VIETATE`
      );

      // Dai punti al compagno con carte non-briscola se possibile (solo Asso, Re, Cavallo, Fante)
      const pointCards = nonTrumpCards.filter((card) =>
        isGoodForTeammate(card)
      );

      if (pointCards.length > 0) {
        // Ordina per dare i punti migliori: Asso > Re > Cavallo > Fante (esclude 2 e 3)
        pointCards.sort((a, b) => {
          const valueOrder: Record<string, number> = {
            A: 1,
            K: 2,
            H: 3,
            J: 4,
          };
          return (valueOrder[a.rank] || 999) - (valueOrder[b.rank] || 999);
        });

        const bestPointCard = pointCards[0];
        console.log(
          `[COLLABORATIVA AVANZATA] 🎯 ANTI-SPRECO PUNTI: ${
            bestPointCard.rank
          } di ${bestPointCard.suit} (${evaluator.getCardValue(
            bestPointCard
          )} punti) invece di sprecare briscole`
        );

        return {
          strategy: "support",
          recommendedCard: bestPointCard,
          reason: `🛡️ ANTI-SPRECO CRITICO: Compagno vincente → regalo ${
            bestPointCard.rank
          } non-briscola (${evaluator.getCardValue(
            bestPointCard
          )} punti) invece di sprecare briscole`,
        };
      }

      // Se non ho punti, usa carte senza valore non-briscola
      const lowCards = nonTrumpCards.filter(
        (card) => evaluator.getCardValue(card) === 0
      );
      if (lowCards.length > 0) {
        lowCards.sort(
          (a, b) =>
            evaluator.getDiscardOrderScore(a) -
            evaluator.getDiscardOrderScore(b)
        );

        console.log(
          `[COLLABORATIVA AVANZATA] 🎯 ANTI-SPRECO SCARTO: ${lowCards[0].rank} di ${lowCards[0].suit} invece di sprecare briscole`
        );

        return {
          strategy: "support",
          recommendedCard: lowCards[0],
          reason: `🛡️ ANTI-SPRECO TOTALE: Compagno vincente → scarto ${lowCards[0].rank} non-briscola invece di sprecare briscole`,
        };
      }

      // Se ho solo carte non-briscola con punti, usa quella meno preziosa
      if (nonTrumpCards.length > 0) {
        nonTrumpCards.sort(
          (a, b) =>
            evaluator.getDiscardOrderScore(a) -
            evaluator.getDiscardOrderScore(b)
        );

        console.log(
          `[COLLABORATIVA AVANZATA] ⚠️ SACRIFICIO MINIMO: ${nonTrumpCards[0].rank} di ${nonTrumpCards[0].suit} per evitare briscole`
        );

        return {
          strategy: "support",
          recommendedCard: nonTrumpCards[0],
          reason: `⚠️ SACRIFICIO MINIMO: Compagno vincente → sacrifico ${nonTrumpCards[0].rank} non-briscola per conservare le briscole`,
        };
      }
    }

    // CASO ESTREMO: Solo briscole disponibili - usa quella più debole SOLO se assolutamente necessario
    if (trumpCards.length > 0 && nonTrumpCards.length === 0) {
      console.log(
        `[COLLABORATIVA AVANZATA] ⚠️ CASO ESTREMO: Solo briscole disponibili, compagno sta vincendo`
      );

      // Verifica se almeno c'è valore nella presa che giustifica l'uso di una briscola
      if (trickValue >= 1) {
        // Con punti sul tavolo, usa la briscola più debole senza punti se possibile
        const weakTrumps = trumpCards.filter(
          (card) => evaluator.getCardValue(card) === 0
        );

        if (weakTrumps.length > 0) {
          weakTrumps.sort(
            (a, b) =>
              evaluator.getCardStrengthScore(a) -
              evaluator.getCardStrengthScore(b)
          );

          console.log(
            `[COLLABORATIVA AVANZATA] 🎯 BRISCOLA DEBOLE GIUSTIFICATA: ${
              weakTrumps[0].rank
            } di ${weakTrumps[0].suit} per ${trickValue.toFixed(1)} punti`
          );

          return {
            strategy: "support",
            recommendedCard: weakTrumps[0],
            reason: `🎯 BRISCOLA GIUSTIFICATA: Solo briscole disponibili, uso la più debole ${
              weakTrumps[0].rank
            } per ${trickValue.toFixed(1)} punti in presa`,
          };
        }
      }

      // Senza punti sufficienti, usa comunque la briscola più debole ma con riluttanza
      trumpCards.sort(
        (a, b) =>
          evaluator.getDiscardOrderScore(a) - evaluator.getDiscardOrderScore(b)
      );

      console.log(
        `[COLLABORATIVA AVANZATA] 😞 BRISCOLA FORZATA: ${trumpCards[0].rank} di ${trumpCards[0].suit} (nessuna alternativa)`
      );

      return {
        strategy: "support",
        recommendedCard: trumpCards[0],
        reason: `😞 BRISCOLA FORZATA: Compagno vincente ma nessuna alternativa disponibile, uso la briscola più debole ${trumpCards[0].rank}`,
      };
    }
  }

  // 🎯 PRIORITÀ ASSOLUTA 0: POSSO VINCERE CON CARTE DI PUNTI = SEMPRE FALLO!
  // Esempio cruciale: Ultimo in coppe con Asso di coppe → PRENDI SEMPRE
  // AGGIORNATO: Verifica sicurezza asso come prima carta
  const securePoints = canSecurePointsForTeam(
    availableCards,
    state,
    evaluator,
    memory.playedCards
  );
  if (securePoints.canWin && securePoints.bestCard) {
    console.log(
      `[COLLABORATIVA AVANZATA] 🎯 PRIORITÀ ASSOLUTA 0: POSSO VINCERE CON CARTE DI PUNTI!`
    );
    console.log(
      `[COLLABORATIVA AVANZATA] 💰 PUNTI ASSICURATI AL TEAM: ${securePoints.pointsSecured.toFixed(
        1
      )} punti con ${securePoints.bestCard.rank} di ${
        securePoints.bestCard.suit
      }`
    );

    return {
      strategy: "compete",
      recommendedCard: securePoints.bestCard,
      reason: `🎯 PUNTI ASSICURATI: ${securePoints.pointsSecured.toFixed(
        1
      )} punti per il team con ${securePoints.bestCard.rank} di ${
        securePoints.bestCard.suit
      }! ${
        securePoints.bestCard.suit === state.trumpSuit
          ? "Briscola strategica"
          : "Carta di punti perfetta"
      }`,
    };
  }
  // 🚨 REGOLA ASSOLUTA SUPREMA: COMPAGNO VINCENTE CON CARTA FORTE = SEMPRE DARE PUNTI!
  // Se il compagno sta vincendo con 3, 2, o Asso (anche senza punti sul tavolo)
  // e non posso seguire il seme, DEVO SEMPRE dare punti (Asso, Re, Cavallo, Fante)
  if (teammateAnalysis.teammateIsWinning && teammateAnalysis.winningCard) {
    const teammateCard = teammateAnalysis.winningCard;
    const leadSuit = state.currentTrick?.[0]?.suit;

    // Verifica se il compagno ha una carta forte (3, 2, Asso)
    const isTeammateCardStrong =
      teammateCard.rank === "3" ||
      teammateCard.rank === "2" ||
      teammateCard.rank === "A";

    // Verifica se posso seguire il seme
    const canFollowSuit = leadSuit
      ? availableCards.some((card) => card.suit === leadSuit)
      : true;

    if (isTeammateCardStrong && !canFollowSuit) {
      console.log(
        `[COLLABORATIVA AVANZATA] 🚨 REGOLA SUPREMA: Compagno vince con ${teammateCard.rank} forte e non posso seguire ${leadSuit} - DEVO DARE PUNTI!`
      );

      // Trova carte di punti disponibili (Asso, Re, Cavallo, Fante)
      const pointCards = availableCards.filter(
        (card) =>
          card.rank === "A" ||
          card.rank === "K" ||
          card.rank === "H" ||
          card.rank === "J"
      );

      if (pointCards.length > 0) {
        // Ordina per valore: Asso > Re > Cavallo > Fante
        pointCards.sort((a, b) => {
          const order = { A: 4, K: 3, H: 2, J: 1 };
          return (
            (order[b.rank as keyof typeof order] || 0) -
            (order[a.rank as keyof typeof order] || 0)
          );
        });

        const bestPointCard = pointCards[0];
        console.log(
          `[COLLABORATIVA AVANZATA] 🎯 PUNTI AL COMPAGNO: ${
            bestPointCard.rank
          } di ${bestPointCard.suit} (valore: ${evaluator.getCardValue(
            bestPointCard
          )}) per carta forte ${teammateCard.rank}`
        );

        return {
          strategy: "support",
          recommendedCard: bestPointCard,
          reason: `🚨 REGOLA SUPREMA: Compagno vince con ${teammateCard.rank} forte, non posso seguire ${leadSuit} → SEMPRE dare punti! (${bestPointCard.rank})`,
        };
      }
    }
  }

  // 🚨 REGOLA ASSOLUTA BIS: Se il compagno sta vincendo con punti, AIUTO SEMPRE!
  if (teammateAnalysis.teammateIsWinning && trickValue >= 1) {
    console.log(
      `[COLLABORATIVA AVANZATA] 🚨 REGOLA ASSOLUTA BIS: Compagno vince ${trickValue.toFixed(
        1
      )} punti - AIUTO SEMPRE!`
    );

    // Trova la migliore carta di valore per aiutare (solo Asso, Re, Cavallo, Fante)
    const valueCards = availableCards.filter((card) => isGoodForTeammate(card));

    if (valueCards.length > 0) {
      // Ordina carte di valore per aiutare: Asso > Re > Cavallo > Fante (esclude 2 e 3)
      valueCards.sort((a, b) => {
        // Prima: Assi
        if (a.rank === "A" && b.rank !== "A") return -1;
        if (b.rank === "A" && a.rank !== "A") return 1;

        // Poi per valore decrescente
        return evaluator.getCardValue(b) - evaluator.getCardValue(a);
      });

      const bestHelpCard = valueCards[0];
      console.log(
        `[COLLABORATIVA AVANZATA] 🤝 AIUTO COMPAGNO: ${bestHelpCard.rank} di ${
          bestHelpCard.suit
        } (valore: ${evaluator.getCardValue(bestHelpCard)})`
      );
      return {
        strategy: "support",
        recommendedCard: bestHelpCard,
        reason: `REGOLA ASSOLUTA: Aiuto compagno che vince ${trickValue.toFixed(
          1
        )} punti con ${bestHelpCard.rank}`,
      };
    }
  }

  // 🔥 PRIORITÀ ASSOLUTA 1: ULTIMO GIOCATORE + TEAM VINCENTE = SEMPRE VALORIZZARE
  if (isLastPlayer && teammateAnalysis.teammateIsWinning) {
    const isEarlyGame = state.trickNumber <= 3; // Prime 3 mani
    console.log(
      `[COLLABORATIVA AVANZATA] 🎯 PRIORITÀ ASSOLUTA 1 ATTIVATA: Ultimo giocatore + Team vincente! ${
        isEarlyGame ? "(PRIME MANI - COOPERAZIONE CRITICA!)" : ""
      }`
    );

    // 🔥 REGOLA SPECIALE POTENZIATA: Se il compagno ha giocato un 3 non-briscola, PRIORITÀ ASSOLUTA ALL'ASSO del seme!
    // Nelle prime mani questa strategia è FONDAMENTALE per massimizzare i punti
    if (
      teammateAnalysis.winningCard &&
      teammateAnalysis.winningCard.rank === "3" &&
      teammateAnalysis.winningCard.suit !== state.trumpSuit
    ) {
      const aceOfSameSuit = availableCards.find(
        (card) =>
          card.rank === "A" && card.suit === teammateAnalysis.winningCard!.suit
      );

      if (aceOfSameSuit) {
        console.log(
          `[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO ULTIMO: Compagno ha giocato 3 di ${
            teammateAnalysis.winningCard.suit
          }, gioco Asso di ${aceOfSameSuit.suit}! ${
            isEarlyGame ? "(STRATEGIA PRIME MANI PERFETTA!)" : ""
          }`
        );

        return {
          strategy: "support",
          recommendedCard: aceOfSameSuit,
          reason: `🏆 RICONOSCIMENTO 3 (ULTIMO): Compagno ha giocato 3 di ${
            teammateAnalysis.winningCard.suit
          } per prendere - gioco Asso per massimizzare punti! ${
            isEarlyGame ? "(STRATEGIA PRIME MANI!)" : ""
          }`,
        };
      }
    }

    // 🔥 REGOLA CRITICA SEMPLIFICATA: ULTIMO GIOCATORE + COMPAGNO VINCENTE CON PUNTI = SEMPRE ASSO!
    // Verifica se il compagno sta prendendo con carte di punti (Asso, Re, Cavallo, Fante, 2, 3)
    const teammateHasPointCard =
      teammateAnalysis.winningCard &&
      evaluator.getCardValue(teammateAnalysis.winningCard) > 0;

    if (teammateHasPointCard) {
      console.log(
        `[COLLABORATIVA AVANZATA] 🔥 COMPAGNO PRENDE CON PUNTI: ${
          teammateAnalysis.winningCard!.rank
        } (${evaluator.getCardValue(teammateAnalysis.winningCard!)} punti)`
      );

      // PRIORITÀ ASSOLUTA 1: ASSO se disponibile
      const aces = availableCards.filter((card) => card.rank === "A");
      if (aces.length > 0) {
        const bestAce = aces[0]; // Prendi il primo asso disponibile
        console.log(
          `[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO: Compagno prende con punti, gioco Asso di ${bestAce.suit}!`
        );

        return {
          strategy: "support",
          recommendedCard: bestAce,
          reason: `🏆 ASSO PRIORITARIO: Compagno prende con carta di punti (${
            teammateAnalysis.winningCard!.rank
          }) = SEMPRE giocare l'Asso! (${evaluator.getCardValue(
            bestAce
          )} punto)`,
        };
      }

      // PRIORITÀ 2: Se non hai asso, vai con ordine Fante > Cavallo > Re
      const priorityOrder = ["J", "H", "K"];
      for (const rank of priorityOrder) {
        const card = availableCards.find((c) => c.rank === rank);
        if (card) {
          console.log(
            `[COLLABORATIVA AVANZATA] 💎 CARTA ALTERNATIVA: Nessun asso, gioco ${card.rank} di ${card.suit}`
          );
          return {
            strategy: "support",
            recommendedCard: card,
            reason: `💎 SUPPORTO PRIORITARIO: Compagno prende con punti, gioco ${
              card.rank
            } (${evaluator.getCardValue(card)} punti)`,
          };
        }
      }
    }

    // Trova TUTTE le carte adatte per il compagno (Asso, Re, Cavallo, Fante), ordinate per valore decrescente
    const valueCards = getTeammateCards(availableCards).sort(
      (a, b) => evaluator.getCardValue(b) - evaluator.getCardValue(a)
    );

    if (valueCards.length > 0) {
      const selectedCard = valueCards[0]; // Carta con più punti disponibile
      console.log(
        `[COLLABORATIVA AVANZATA] 🔥 VALORIZZAZIONE MASSIMA: ${
          selectedCard.rank
        } di ${selectedCard.suit} (${evaluator.getCardValue(
          selectedCard
        )} punti)`
      );

      return {
        strategy: "support",
        recommendedCard: selectedCard,
        reason: `🎯 TEAM-FIRST P1: Ultimo giocatore + team vincente = SEMPRE valorizzare! Gioco ${
          selectedCard.rank
        } (${evaluator.getCardValue(selectedCard)} punti)`,
      };
    }

    // 🔥 REGOLA CRITICA: ULTIMO GIOCATORE + TEAM VINCENTE = SEMPRE GIOCARE L'ASSO!
    // Se abbiamo l'Asso, ha priorità assoluta per massimizzare i punti del team
    const aces = availableCards.filter((card) => card.rank === "A");
    if (aces.length > 0) {
      // Preferisci l'Asso del seme di uscita se disponibile, altrimenti il più alto
      const aceOfLeadSuit = aces.find(
        (card) => card.suit === state.currentTrick?.[0]?.suit
      );
      const bestAce =
        aceOfLeadSuit ||
        aces.sort(
          (a, b) => evaluator.getCardValue(b) - evaluator.getCardValue(a)
        )[0];

      console.log(
        `[COLLABORATIVA AVANZATA] 🏆 ASSO MASSIMIZZAZIONE: Team vincente + ultimo giocatore = gioco Asso di ${bestAce.suit} per massimizzare punti!`
      );

      return {
        strategy: "support",
        recommendedCard: bestAce,
        reason: `🏆 MASSIMIZZAZIONE TEAM: Team vincente + ultimo giocatore = SEMPRE giocare l'Asso! (${evaluator.getCardValue(
          bestAce
        )} punti)`,
      };
    }

    // Seleziona la carta di maggior valore rimanente (non briscola) per massimizzare i punti
    const highestValueCard = availableCards.sort(
      (a, b) => evaluator.getCardValue(b) - evaluator.getCardValue(a)
    )[0];

    return {
      strategy: "support",
      recommendedCard: highestValueCard,
      reason: `🎯 MASSIMIZZAZIONE PUNTI: Ultimo giocatore + team vincente = gioco carta di maggior valore (${evaluator.getCardValue(
        highestValueCard
      )} punti)`,
    };
  }
  // 🚀 PRIORITÀ ASSOLUTA 2: TERZO GIOCATORE + COMPAGNO CON CARTA IMBATTIBILE = SEMPRE VALORIZZARE
  const isThirdPlayer = state.currentTrick?.length === 2;
  if (
    isThirdPlayer &&
    teammateAnalysis.teammateIsWinning &&
    teammateAnalysis.winningCard
  ) {
    const teammateCard = teammateAnalysis.winningCard;

    // Verifica se la carta del compagno è quasi imbattibile (3 o 2 non-briscola, o carte briscola forti)
    const isTeammateCardSuperStrong =
      isTeammateCardUnbeatable(teammateCard, memory, state) ||
      evaluator.isObviousWinSituation(
        teammateCard,
        state.currentTrick || [],
        state.currentTrick?.[0]?.suit || null,
        state.trumpSuit,
        trickValue
      );
    if (isTeammateCardSuperStrong) {
      const isEarlyGame = state.trickNumber <= 3; // Prime 3 mani
      console.log(
        `[COLLABORATIVA AVANZATA] 🚀 PRIORITÀ ASSOLUTA 2 ATTIVATA: Terzo giocatore + compagno imbattibile! ${
          isEarlyGame ? "(PRIME MANI - COOPERAZIONE CRITICA!)" : ""
        }`
      );

      // 🔥 REGOLA SPECIALE POTENZIATA: Se il compagno ha giocato un 3 non-briscola, PRIORITÀ ASSOLUTA ALL'ASSO del seme!
      if (teammateCard.rank === "3" && teammateCard.suit !== state.trumpSuit) {
        const aceOfSameSuit = availableCards.find(
          (card) => card.rank === "A" && card.suit === teammateCard.suit
        );

        if (aceOfSameSuit) {
          console.log(
            `[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO TERZO: Compagno ha giocato 3 di ${
              teammateCard.suit
            }, gioco Asso di ${aceOfSameSuit.suit}! ${
              isEarlyGame ? "(STRATEGIA PRIME MANI PERFETTA!)" : ""
            }`
          );

          return {
            strategy: "support",
            recommendedCard: aceOfSameSuit,
            reason: `🏆 RICONOSCIMENTO 3 (TERZO): Compagno ha giocato 3 di ${
              teammateCard.suit
            } per prendere - gioco Asso per massimizzare punti! ${
              isEarlyGame ? "(STRATEGIA PRIME MANI!)" : ""
            }`,
          };
        }
      }

      // Trova TUTTE le carte adatte per il compagno (Asso, Re, Cavallo, Fante), ordinate per valore decrescente
      const valueCards = getTeammateCards(availableCards).sort(
        (a, b) => evaluator.getCardValue(b) - evaluator.getCardValue(a)
      );

      if (valueCards.length > 0) {
        const selectedCard = valueCards[0]; // Carta con più punti disponibile
        console.log(
          `[COLLABORATIVA AVANZATA] 🚀 VALORIZZAZIONE TERZO GIOCATORE: ${
            selectedCard.rank
          } di ${selectedCard.suit} (${evaluator.getCardValue(
            selectedCard
          )} punti) - Compagno ha ${teammateCard.rank} imbattibile!`
        );

        return {
          strategy: "support",
          recommendedCard: selectedCard,
          reason: `🚀 TEAM-FIRST P2: Terzo giocatore + compagno imbattibile (${
            teammateCard.rank
          }) = SEMPRE valorizzare! Gioco ${
            selectedCard.rank
          } (${evaluator.getCardValue(selectedCard)} punti)`,
        };
      }
    }
  }

  // ⚡ PRIORITÀ ASSOLUTA 3: SECONDO GIOCATORE + COMPAGNO PRIMO CON CARTA SUPER FORTE = VALORIZZARE
  const isSecondPlayer = state.currentTrick?.length === 1;
  if (
    isSecondPlayer &&
    teammateAnalysis.teammateIsWinning &&
    teammateAnalysis.winningCard
  ) {
    const teammateCard = teammateAnalysis.winningCard; // Verifica se il compagno ha giocato per primo una carta estremamente forte
    const isTeammateCardSuperStrong =
      (teammateCard.rank === "3" && teammateCard.suit !== state.trumpSuit) ||
      (teammateCard.rank === "2" && teammateCard.suit !== state.trumpSuit) ||
      (teammateCard.suit === state.trumpSuit &&
        (teammateCard.rank === "3" || teammateCard.rank === "2"));
    if (isTeammateCardSuperStrong) {
      const isEarlyGame = state.trickNumber <= 3; // Prime 3 mani
      console.log(
        `[COLLABORATIVA AVANZATA] ⚡ PRIORITÀ ASSOLUTA 3 ATTIVATA: Secondo giocatore + compagno primo con carta super forte! ${
          isEarlyGame ? "(PRIME MANI - COOPERAZIONE CRITICA!)" : ""
        }`
      );

      // 🔥 REGOLA SPECIALE POTENZIATA: Se il compagno ha giocato un 3 non-briscola, PRIORITÀ ASSOLUTA ALL'ASSO del seme!
      if (teammateCard.rank === "3" && teammateCard.suit !== state.trumpSuit) {
        const aceOfSameSuit = availableCards.find(
          (card) => card.rank === "A" && card.suit === teammateCard.suit
        );

        if (aceOfSameSuit) {
          console.log(
            `[COLLABORATIVA AVANZATA] 🏆 ASSO PRIORITARIO: Compagno ha giocato 3 di ${
              teammateCard.suit
            }, gioco Asso di ${aceOfSameSuit.suit}! ${
              isEarlyGame ? "(STRATEGIA PRIME MANI PERFETTA!)" : ""
            }`
          );
          return {
            strategy: "support",
            recommendedCard: aceOfSameSuit,
            reason: `🏆 RICONOSCIMENTO 3: Compagno ha giocato 3 di ${
              teammateCard.suit
            } per prendere - gioco Asso per massimizzare punti! ${
              isEarlyGame ? "(STRATEGIA PRIME MANI!)" : ""
            }`,
          };
        }
      }

      // Trova TUTTE le carte adatte per il compagno (Asso, Re, Cavallo, Fante), NON competendo con il compagno
      const valueCards = getTeammateCards(availableCards)
        .filter(
          (card) =>
            !evaluator.canWinCurrentTrick(
              card,
              state.currentTrick || [],
              state.currentTrick?.[0]?.suit || null,
              state.trumpSuit
            )
        )
        .sort((a, b) => evaluator.getCardValue(b) - evaluator.getCardValue(a));

      if (valueCards.length > 0) {
        const selectedCard = valueCards[0]; // Carta con più punti che non supera il compagno
        console.log(
          `[COLLABORATIVA AVANZATA] ⚡ VALORIZZAZIONE SECONDO GIOCATORE: ${
            selectedCard.rank
          } di ${selectedCard.suit} (${evaluator.getCardValue(
            selectedCard
          )} punti) - Compagno primo con ${teammateCard.rank} super forte!`
        );

        return {
          strategy: "support",
          recommendedCard: selectedCard,
          reason: `⚡ TEAM-FIRST P3: Secondo giocatore + compagno primo con carta super forte (${
            teammateCard.rank
          }) = valorizzare senza competere! Gioco ${
            selectedCard.rank
          } (${evaluator.getCardValue(selectedCard)} punti)`,
        };
      }
    }
  } // 🛡️ PROTEZIONE: Se gli avversari stanno vincendo, NON regalare punti
  if (!teammateAnalysis.teammateIsWinning && trickValue > 0) {
    console.log(
      `[COLLABORATIVA AVANZATA] 🛡️ AVVERSARI VINCENTI: ${trickValue.toFixed(
        1
      )} punti in gioco!`
    );

    // 🔥 REGOLA CRITICA RIBALTAMENTO: Se sono ultimo giocatore e posso ribaltare con Asso, SEMPRE farlo!
    if (isLastPlayer) {
      const isEarlyGame = state.trickNumber <= 3; // Prime 3 mani
      console.log(
        `[COLLABORATIVA AVANZATA] 🔥 ULTIMO GIOCATORE + AVVERSARI VINCENTI! ${
          isEarlyGame ? "(PRIME MANI - RIBALTAMENTO CRITICO!)" : ""
        }`
      );

      // Priorità assoluta all'Asso per ribaltare la presa
      const aces = availableCards.filter((card) => card.rank === "A");

      if (aces.length > 0) {
        // Trova l'Asso che può vincere la presa
        const winningAces = aces.filter((ace) =>
          evaluator.canWinCurrentTrick(
            ace,
            state.currentTrick || [],
            state.currentTrick?.[0]?.suit || null,
            state.trumpSuit
          )
        );

        if (winningAces.length > 0) {
          // Preferisci l'Asso del seme di uscita se può vincere, altrimenti il migliore
          const aceOfLeadSuit = winningAces.find(
            (ace) => ace.suit === state.currentTrick?.[0]?.suit
          );
          const bestAce =
            aceOfLeadSuit ||
            winningAces.sort(
              (a, b) => evaluator.getCardValue(b) - evaluator.getCardValue(a)
            )[0];

          console.log(
            `[COLLABORATIVA AVANZATA] 🏆 RIBALTAMENTO ASSO: ${
              bestAce.rank
            } di ${bestAce.suit} ribalta ${trickValue.toFixed(1)} punti! ${
              isEarlyGame ? "(STRATEGIA PRIME MANI PERFETTA!)" : ""
            }`
          );

          return {
            strategy: "compete",
            recommendedCard: bestAce,
            reason: `🏆 RIBALTAMENTO CRITICO: Ultimo giocatore con Asso di ${
              bestAce.suit
            } ribalta ${trickValue.toFixed(1)} punti agli avversari! ${
              isEarlyGame ? "(PRIME MANI!)" : ""
            }`,
          };
        }
      }
    } // 🚨 PRIORITÀ CRITICA: Se ci sono 1+ punti, SEMPRE tentare di prendere!
    if (trickValue >= 1.0) {
      console.log(
        `[COLLABORATIVA AVANZATA] 🚨 RECUPERO AGGRESSIVO: ${trickValue.toFixed(
          1
        )} punti da recuperare!`
      );

      // 🔥 CONTROLLO INTELLIGENTE: Se l'avversario ha tagliato con briscola debole, valuta se vale la pena ritagliare
      const currentTrick = state.currentTrick || [];
      const opponentTrumpsInTrick = currentTrick.filter(
        (card) => card.suit === state.trumpSuit
      );

      if (opponentTrumpsInTrick.length > 0) {
        // L'avversario ha tagliato - verifica se vale la pena ritagliare
        const highestOpponentTrump = opponentTrumpsInTrick.reduce(
          (highest, card) =>
            evaluator.getCardOrder(card) > evaluator.getCardOrder(highest)
              ? card
              : highest
        );

        // Se l'avversario ha tagliato con una briscola debole (4,5,6,7) e ci sono pochi punti (< 2),
        // non sprecare briscole strategiche
        const isOpponentTrumpWeak = ["4", "5", "6", "7"].includes(
          highestOpponentTrump.rank
        );
        if (isOpponentTrumpWeak && trickValue < 2) {
          console.log(
            `[COLLABORATIVA AVANZATA] 🛡️ ANTI-SPRECO: Avversario ha tagliato con ${
              highestOpponentTrump.rank
            } per solo ${trickValue.toFixed(
              1
            )} punti - non ritaglio con briscole strategiche`
          );

          // Trova carte non-briscola per scartare invece di sprecare briscole
          const nonTrumpCards = availableCards.filter(
            (card) => card.suit !== state.trumpSuit
          );

          if (nonTrumpCards.length > 0) {
            // Scarta la carta non-briscola meno preziosa
            nonTrumpCards.sort(
              (a, b) =>
                evaluator.getDiscardOrderScore(a) -
                evaluator.getDiscardOrderScore(b)
            );

            const discardCard = nonTrumpCards[0];
            console.log(
              `[COLLABORATIVA AVANZATA] 🗑️ SCARTO INTELLIGENTE: ${discardCard.rank} di ${discardCard.suit} invece di sprecare briscole`
            );

            return {
              strategy: "support",
              recommendedCard: discardCard,
              reason: `🛡️ ANTI-SPRECO: Avversario ha tagliato con briscola debole per pochi punti - scarto ${discardCard.rank} invece di sprecare briscole strategiche`,
            };
          }
        }
      }

      // Trova carte che possono vincere la presa corrente
      const winningCards = availableCards.filter((card) =>
        evaluator.canWinCurrentTrick(
          card,
          state.currentTrick || [],
          state.currentTrick?.[0]?.suit || null,
          state.trumpSuit
        )
      );

      if (winningCards.length > 0) {
        // 🛡️ CONTROLLO ANTI-SPRECO: Priorità assoluta alle carte non-briscola vincenti
        const nonTrumpWinners = winningCards.filter(
          (card) => card.suit !== state.trumpSuit
        );

        if (nonTrumpWinners.length > 0) {
          // Usa carte non-briscola per recuperare se possibile
          nonTrumpWinners.sort((a, b) => {
            const costA = evaluateCardStrategicValue(
              a,
              memory,
              state,
              playerIndex
            );
            const costB = evaluateCardStrategicValue(
              b,
              memory,
              state,
              playerIndex
            );
            return costA - costB;
          });

          const selectedCard = nonTrumpWinners[0];
          console.log(
            `[COLLABORATIVA AVANZATA] 🎯 RECUPERO NON-BRISCOLA: ${
              selectedCard.rank
            } di ${selectedCard.suit} per ${trickValue.toFixed(1)} punti`
          );

          return {
            strategy: "compete",
            recommendedCard: selectedCard,
            reason: `🎯 RECUPERO OTTIMALE: Non-briscola ${
              selectedCard.rank
            } recupera ${trickValue.toFixed(1)} punti senza sprecare briscole!`,
          };
        }

        // Solo se NON ci sono alternative non-briscola, considera le briscole
        // 🔥 CORREZIONE CRITICA: Priorità alle briscole basse quando ci sono abbastanza punti
        if (trickValue >= 1) {
          const lowTrumps = winningCards.filter(
            (card) =>
              card.suit === state.trumpSuit &&
              (card.rank === "7" ||
                card.rank === "6" ||
                card.rank === "5" ||
                card.rank === "4")
          );

          if (lowTrumps.length > 0) {
            // Usa la briscola più bassa disponibile
            lowTrumps.sort(
              (a, b) =>
                evaluator.getCardStrengthScore(a) -
                evaluator.getCardStrengthScore(b)
            );
            const lowTrump = lowTrumps[0];

            console.log(
              `[COLLABORATIVA AVANZATA] 🎯 RECUPERO OTTIMALE: Briscola bassa ${
                lowTrump.rank
              } di ${lowTrump.suit} per ${trickValue.toFixed(1)} punti!`
            );

            return {
              strategy: "compete",
              recommendedCard: lowTrump,
              reason: `🎯 RECUPERO OTTIMALE: Briscola bassa ${
                lowTrump.rank
              } recupera ${trickValue.toFixed(1)} punti dagli avversari!`,
            };
          }
        }

        // Ordina per "costo" - usa la carta meno preziosa che può vincere
        const sortedWinningCards = winningCards.sort((a, b) => {
          const costA = evaluateCardStrategicValue(
            a,
            memory,
            state,
            playerIndex
          );
          const costB = evaluateCardStrategicValue(
            b,
            memory,
            state,
            playerIndex
          );
          return costA - costB; // Carta meno costosa strategicamente
        });

        const selectedCard = sortedWinningCards[0];
        console.log(
          `[COLLABORATIVA AVANZATA] 🚨 RECUPERO CON: ${selectedCard.rank} di ${
            selectedCard.suit
          } - Salvo ${trickValue.toFixed(1)} punti!`
        );

        return {
          strategy: "compete",
          recommendedCard: selectedCard,
          reason: `🚨 RECUPERO AGGRESSIVO: Avversari vincenti con ${trickValue.toFixed(
            1
          )} punti, recupero con ${selectedCard.rank}!`,
        };
      }
    } // 🛡️ PROTEZIONE SPECIALE: Presa da 0 punti - MAI usare briscole a meno che non sia l'unica opzione
    if (trickValue === 0) {
      console.log(
        `[COLLABORATIVA AVANZATA] 🛡️ PRESA SENZA VALORE: 0 punti - evito briscole a tutti i costi`
      );

      // Trova tutte le carte non-briscola disponibili
      const nonTrumpCards = availableCards.filter(
        (card) => card.suit !== state.trumpSuit
      );

      if (nonTrumpCards.length > 0) {
        // Verifica se devo seguire il seme
        const leadSuit = state.currentTrick?.[0]?.suit;
        const canFollowSuit = leadSuit
          ? nonTrumpCards.some((card) => card.suit === leadSuit)
          : false;

        if (canFollowSuit) {
          // Segui il seme con carte non-briscola
          const leadSuitCards = nonTrumpCards.filter(
            (card) => card.suit === leadSuit
          );

          // Usa la carta più bassa del seme per non sprecare valore
          leadSuitCards.sort(
            (a, b) =>
              evaluator.getDiscardOrderScore(a) -
              evaluator.getDiscardOrderScore(b)
          );

          console.log(
            `[COLLABORATIVA AVANZATA] 🎯 PRESA 0 PUNTI - SEGUO SEME: ${leadSuitCards[0].rank} di ${leadSuitCards[0].suit}`
          );

          return {
            strategy: "support",
            recommendedCard: leadSuitCards[0],
            reason: `🛡️ CONSERVAZIONE: Presa da 0 punti → seguo seme con ${leadSuitCards[0].rank} per non sprecare briscole`,
          };
        } else {
          // Non posso seguire il seme, usa qualsiasi carta non-briscola
          // Preferisci carte senza valore o quelle meno preziose
          const zeroValueNonTrumps = nonTrumpCards.filter(
            (card) => evaluator.getCardValue(card) === 0
          );

          if (zeroValueNonTrumps.length > 0) {
            zeroValueNonTrumps.sort(
              (a, b) =>
                evaluator.getDiscardOrderScore(a) -
                evaluator.getDiscardOrderScore(b)
            );

            console.log(
              `[COLLABORATIVA AVANZATA] 🎯 PRESA 0 PUNTI - SCARTO PULITO: ${zeroValueNonTrumps[0].rank} di ${zeroValueNonTrumps[0].suit}`
            );

            return {
              strategy: "support",
              recommendedCard: zeroValueNonTrumps[0],
              reason: `🛡️ SCARTO OTTIMALE: Presa da 0 punti → scarto ${zeroValueNonTrumps[0].rank} non-briscola senza valore`,
            };
          } else {
            // Solo carte non-briscola con valore - usa quella meno preziosa
            nonTrumpCards.sort(
              (a, b) =>
                evaluator.getDiscardOrderScore(a) -
                evaluator.getDiscardOrderScore(b)
            );

            console.log(
              `[COLLABORATIVA AVANZATA] ⚠️ PRESA 0 PUNTI - SACRIFICIO MINIMO: ${nonTrumpCards[0].rank} di ${nonTrumpCards[0].suit}`
            );

            return {
              strategy: "support",
              recommendedCard: nonTrumpCards[0],
              reason: `⚠️ SACRIFICIO MINIMO: Presa da 0 punti → sacrifico ${nonTrumpCards[0].rank} non-briscola per conservare briscole`,
            };
          }
        }
      } else {
        // CASO ESTREMO: Solo briscole disponibili per presa da 0 punti
        console.log(
          `[COLLABORATIVA AVANZATA] 😞 CASO ESTREMO: Solo briscole per presa da 0 punti - uso la più debole`
        );

        const trumpCards = availableCards.filter(
          (card) => card.suit === state.trumpSuit
        );

        // Usa la briscola più debole senza valore se possibile
        const weakTrumps = trumpCards.filter(
          (card) => evaluator.getCardValue(card) === 0
        );

        if (weakTrumps.length > 0) {
          weakTrumps.sort(
            (a, b) =>
              evaluator.getCardStrengthScore(a) -
              evaluator.getCardStrengthScore(b)
          );

          console.log(
            `[COLLABORATIVA AVANZATA] 😞 BRISCOLA DEBOLE FORZATA: ${weakTrumps[0].rank} di ${weakTrumps[0].suit}`
          );

          return {
            strategy: "support",
            recommendedCard: weakTrumps[0],
            reason: `😞 FORZATURA: Presa da 0 punti ma solo briscole disponibili → uso la più debole ${weakTrumps[0].rank}`,
          };
        } else {
          // Tutte le briscole hanno valore - usa quella con meno valore
          trumpCards.sort(
            (a, b) =>
              evaluator.getDiscardOrderScore(a) -
              evaluator.getDiscardOrderScore(b)
          );

          console.log(
            `[COLLABORATIVA AVANZATA] 😢 SPRECO FORZATO: ${trumpCards[0].rank} di ${trumpCards[0].suit} per presa da 0 punti`
          );

          return {
            strategy: "support",
            recommendedCard: trumpCards[0],
            reason: `😢 SPRECO INEVITABILE: Presa da 0 punti, solo briscole con valore disponibili → uso ${trumpCards[0].rank}`,
          };
        }
      }
    }

    // Se non posso vincere o pochi punti (< 1), protezione normale con carte da 0 punti
    const zeroPointCards = availableCards.filter(
      (card) => evaluator.getCardValue(card) === 0
    );

    if (zeroPointCards.length > 0) {
      // Gioca la carta più debole tra quelle senza punti
      const weakestCard = zeroPointCards.reduce((weakest, card) =>
        evaluator.getCardOrder(card) < evaluator.getCardOrder(weakest)
          ? card
          : weakest
      );

      return {
        strategy: "support",
        recommendedCard: weakestCard,
        reason: `🛡️ PROTEZIONE TEAM: Avversari vincenti con ${trickValue.toFixed(
          1
        )} punti, gioco carta senza punti (${weakestCard.rank})`,
      };
    }
  }

  // 🤝 SUPPORTO STANDARD: Analisi situazione del compagno
  if (!teammateAnalysis.shouldSupport) {
    console.log(
      `[COLLABORATIVA AVANZATA] ⚡ Nessun supporto necessario - strategia neutra`
    );
    return { strategy: "neutral" };
  }

  // 🏆 SUPPORTO ATTIVO: Il compagno sta vincendo ma non siamo ultimi
  if (teammateAnalysis.teammateIsWinning) {
    console.log(
      `[COLLABORATIVA AVANZATA] 🏆 COMPAGNO STA VINCENDO - Modalità supporto attivo!`
    );

    const winningCard = teammateAnalysis.winningCard;
    if (!winningCard) {
      return { strategy: "support" };
    }

    // Verifica se la carta del compagno è imbattibile
    const isTeammateCardSureWin = isTeammateCardUnbeatable(
      winningCard,
      memory,
      state
    );

    if (isTeammateCardSureWin) {
      console.log(
        `[COLLABORATIVA AVANZATA] 🔥 COMPAGNO IMBATTIBILE - Preparazione valorizzazione!`
      );

      // Se non siamo ultimi, giochiamo conservativo per non interferire
      const safeCards = availableCards.filter(
        (card) =>
          !evaluator.canWinCurrentTrick(
            card,
            state.currentTrick || [],
            state.currentTrick?.[0]?.suit || null,
            state.trumpSuit
          )
      );

      if (safeCards.length > 0) {
        const selectedCard = safeCards[0];
        return {
          strategy: "support",
          recommendedCard: selectedCard,
          reason: `🤝 SUPPORTO SICURO: Compagno imbattibile, non interferisco con ${selectedCard.rank}`,
        };
      }
    }

    // Filtra le carte che NON supererebbero il compagno
    const nonCompetingCards = availableCards.filter((card) => {
      // Se il compagno ha giocato una briscola
      if (winningCard.suit === state.trumpSuit) {
        if (card.suit === state.trumpSuit) {
          // Non giocare briscole più forti del compagno
          return (
            evaluator.getCardOrder(card) <= evaluator.getCardOrder(winningCard)
          );
        } else {
          // Carte non-briscola non possono superare una briscola
          return true;
        }
      } else {
        // Il compagno ha giocato seme normale
        if (card.suit === state.trumpSuit) {
          // Non giocare mai briscole se il compagno sta vincendo con seme normale
          return false;
        } else if (card.suit === winningCard.suit) {
          // Non giocare carte più forti dello stesso seme
          return (
            evaluator.getCardOrder(card) <= evaluator.getCardOrder(winningCard)
          );
        } else {
          // Carte di semi diversi vanno bene
          return true;
        }
      }
    });

    if (nonCompetingCards.length === 0) {
      // Situazione critica: tutte le carte supererebbero il compagno
      // Scegli la carta meno dannosa (quella che lo supera di meno)
      const leastCompetitive = availableCards.reduce((least, card) => {
        const cardStrength =
          card.suit === state.trumpSuit
            ? 1000 + evaluator.getCardOrder(card)
            : evaluator.getCardOrder(card);
        const leastStrength =
          least.suit === state.trumpSuit
            ? 1000 + evaluator.getCardOrder(least)
            : evaluator.getCardOrder(least);

        return cardStrength < leastStrength ? card : least;
      });

      return {
        strategy: "support",
        recommendedCard: leastCompetitive,
        reason:
          "Giocando la carta meno competitiva per non danneggiare il compagno",
      };
    } // 🔥 CORREZIONE CRITICA: Se è l'ultimo giocatore e la presa ha valore (o siamo in endgame)
    if (isLastPlayer && (trickValue >= 1 || isEndGame)) {
      console.log(
        `[COLLABORATIVA AVANZATA] 🔥 ULTIMO GIOCATORE + COMPAGNO VINCENTE + PRESA PREZIOSA (${trickValue.toFixed(
          1
        )} punti)`
      );

      // PRIORITÀ 1: Carte adatte per il compagno (Asso, Re, Cavallo, Fante) - le migliori
      const teammateCards = getTeammateCards(nonCompetingCards);

      if (teammateCards.length > 0) {
        // Scegli la carta migliore per il compagno (esclude automaticamente 2 e 3)
        const bestValueCard = teammateCards.reduce((best, card) =>
          evaluator.getCardValue(card) > evaluator.getCardValue(best)
            ? card
            : best
        );

        console.log(
          `[COLLABORATIVA AVANZATA] 🏆 VALORIZZAZIONE FIGURE: ${
            bestValueCard.rank
          } di ${bestValueCard.suit} (${evaluator.getCardValue(
            bestValueCard
          )} punti)`
        );

        return {
          strategy: "support",
          recommendedCard: bestValueCard,
          reason: `🏆 VALORIZZAZIONE FIGURE: Ultimo giocatore + team vincente, gioco ${
            bestValueCard.rank
          } (${evaluator.getCardValue(bestValueCard)} punti)`,
        };
      }

      // PRIORITÀ 2: CORREZIONE CRITICA - Se ho solo briscole basse ma ci sono ≥1 punti, DEVO prenderli!
      const lowTrumps = nonCompetingCards.filter(
        (card) =>
          card.suit === state.trumpSuit &&
          (card.rank === "7" ||
            card.rank === "6" ||
            card.rank === "5" ||
            card.rank === "4")
      );

      if (lowTrumps.length > 0 && trickValue >= 1) {
        // Usa la briscola più bassa che può ancora vincere
        const winningLowTrumps = lowTrumps.filter((card) =>
          evaluator.canWinCurrentTrick(
            card,
            state.currentTrick || [],
            state.currentTrick?.[0]?.suit || null,
            state.trumpSuit
          )
        );

        if (winningLowTrumps.length > 0) {
          // Ordina per valore crescente - usa la più bassa
          winningLowTrumps.sort(
            (a, b) =>
              evaluator.getCardStrengthScore(a) -
              evaluator.getCardStrengthScore(b)
          );
          const lowTrump = winningLowTrumps[0];

          console.log(
            `[COLLABORATIVA AVANZATA] 🚨 CORREZIONE CRITICA: Prendo con briscola bassa ${
              lowTrump.rank
            } di ${lowTrump.suit} per salvare ${trickValue.toFixed(1)} punti!`
          );

          return {
            strategy: "support",
            recommendedCard: lowTrump,
            reason: `🚨 SALVATAGGIO PUNTI: Ultimo giocatore, uso briscola bassa ${
              lowTrump.rank
            } per salvare ${trickValue.toFixed(1)} punti del team!`,
          };
        }
      }

      // PRIORITÀ 3: Qualsiasi carta adatta per il compagno (Asso, Re, Cavallo, Fante), anche se non "ideale"
      const anyValueCards = nonCompetingCards.filter((card) =>
        isGoodForTeammate(card)
      );

      if (anyValueCards.length > 0) {
        anyValueCards.sort(
          (a, b) => evaluator.getCardValue(b) - evaluator.getCardValue(a)
        );
        const fallbackCard = anyValueCards[0];

        console.log(
          `[COLLABORATIVA AVANZATA] 💰 FALLBACK PUNTI: ${
            fallbackCard.rank
          } di ${fallbackCard.suit} (${evaluator.getCardValue(
            fallbackCard
          )} punti)`
        );

        return {
          strategy: "support",
          recommendedCard: fallbackCard,
          reason: `💰 PUNTI FALLBACK: Ultimo giocatore, valorizzo con ${
            fallbackCard.rank
          } (${evaluator.getCardValue(fallbackCard)} punti)`,
        };
      }
    } // 🔥 CORREZIONE CRITICA: VALORIZZAZIONE ANCHE PER NON-ULTIMI quando c'è valore in presa
    if (trickValue >= 1) {
      console.log(
        `[COLLABORATIVA AVANZATA] 🎯 NON-ULTIMO + COMPAGNO VINCENTE + PRESA PREZIOSA (${trickValue.toFixed(
          1
        )} punti)`
      );

      // Trova le figure che posso giocare senza superare il compagno
      const valuableSupport = nonCompetingCards.filter(
        (card) =>
          evaluator.getCardValue(card) > 0 &&
          (card.rank === "A" ||
            card.rank === "K" ||
            card.rank === "H" ||
            card.rank === "J")
      );

      if (valuableSupport.length > 0) {
        // Ordina per valore decrescente
        valuableSupport.sort(
          (a, b) => evaluator.getCardValue(b) - evaluator.getCardValue(a)
        );
        const bestSupportCard = valuableSupport[0];

        console.log(
          `[COLLABORATIVA AVANZATA] 🏆 VALORIZZAZIONE NON-ULTIMO: ${
            bestSupportCard.rank
          } di ${bestSupportCard.suit} (${evaluator.getCardValue(
            bestSupportCard
          )} punti)`
        );

        return {
          strategy: "support",
          recommendedCard: bestSupportCard,
          reason: `🏆 VALORIZZAZIONE FIGURE: Compagno vincente + presa preziosa, gioco ${
            bestSupportCard.rank
          } (${evaluator.getCardValue(bestSupportCard)} punti)`,
        };
      }
    }

    // Strategia di scarto: carta di minor valore strategico
    const supportCard = nonCompetingCards.reduce((best, card) => {
      const valueA = evaluateCardStrategicValue(
        card,
        memory,
        state,
        playerIndex
      );
      const valueB = evaluateCardStrategicValue(
        best,
        memory,
        state,
        playerIndex
      );
      return valueA < valueB ? card : best;
    });

    return {
      strategy: "support",
      recommendedCard: supportCard,
      reason: "Supportando il compagno con carta di scarto",
    };
  }

  // Il compagno non sta vincendo: strategia competitiva normale
  return { strategy: "compete" };
};
