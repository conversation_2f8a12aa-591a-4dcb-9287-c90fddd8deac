import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';

interface ConnectionState {
  isOnline: boolean;
  lastSeen: string;
  sessionId?: string;
  userId?: string;
}

class ConnectionService {
  private static instance: ConnectionService;
  private connectionState: ConnectionState = {
    isOnline: navigator.onLine,
    lastSeen: new Date().toISOString(),
  };
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private presenceChannel: RealtimeChannel | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second

  private constructor() {
    this.setupNetworkListeners();
  }

  static getInstance(): ConnectionService {
    if (!ConnectionService.instance) {
      ConnectionService.instance = new ConnectionService();
    }
    return ConnectionService.instance;
  }

  private setupNetworkListeners() {
    window.addEventListener('online', () => {
      console.log('🌐 Network connection restored');
      this.connectionState.isOnline = true;
      this.handleReconnection();
    });

    window.addEventListener('offline', () => {
      console.log('🌐 Network connection lost');
      this.connectionState.isOnline = false;
      this.handleDisconnection();
    });

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        this.handleReconnection();
      } else {
        this.updateLastSeen();
      }
    });

    // Handle beforeunload to update connection status
    window.addEventListener('beforeunload', () => {
      this.handleDisconnection();
    });
  }

  /**
   * Start monitoring connection for a game session
   */
  async startSessionMonitoring(sessionId: string, userId: string): Promise<void> {
    this.connectionState.sessionId = sessionId;
    this.connectionState.userId = userId;

    try {
      // Update player connection status to true
      await this.updatePlayerConnection(sessionId, userId, true);

      // Start heartbeat
      this.startHeartbeat();

      // Setup presence channel
      this.setupPresenceChannel(sessionId, userId);

      console.log('✅ Session monitoring started for:', sessionId);
    } catch (error) {
      console.error('❌ Error starting session monitoring:', error);
    }
  }

  /**
   * Stop monitoring connection
   */
  async stopSessionMonitoring(): Promise<void> {
    const { sessionId, userId } = this.connectionState;

    if (sessionId && userId) {
      try {
        // Update player connection status to false
        await this.updatePlayerConnection(sessionId, userId, false);
      } catch (error) {
        console.error('❌ Error updating connection status on stop:', error);
      }
    }

    this.stopHeartbeat();
    this.cleanupPresenceChannel();

    this.connectionState.sessionId = undefined;
    this.connectionState.userId = undefined;

    console.log('🛑 Session monitoring stopped');
  }

  private async updatePlayerConnection(sessionId: string, userId: string, connected: boolean): Promise<void> {
    try {
      const { error } = await supabase
        .from('game_players')
        .update({ 
          connected,
          ...(connected ? {} : { disconnected_at: new Date().toISOString() })
        })
        .eq('session_id', sessionId)
        .eq('user_id', userId);

      if (error) {
        throw error;
      }

      console.log(`🔄 Player connection updated: ${connected ? 'connected' : 'disconnected'}`);
    } catch (error) {
      console.error('❌ Error updating player connection:', error);
      throw error;
    }
  }

  private startHeartbeat(): void {
    this.stopHeartbeat(); // Clear any existing heartbeat

    this.heartbeatInterval = setInterval(async () => {
      if (this.connectionState.sessionId && this.connectionState.userId) {
        try {
          await this.updateLastSeen();
        } catch (error) {
          console.error('❌ Heartbeat failed:', error);
          this.handleDisconnection();
        }
      }
    }, 30000); // Send heartbeat every 30 seconds
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private async updateLastSeen(): Promise<void> {
    const { sessionId, userId } = this.connectionState;
    if (!sessionId || !userId) return;

    this.connectionState.lastSeen = new Date().toISOString();

    try {
      const { error } = await supabase
        .from('game_players')
        .update({ last_seen: this.connectionState.lastSeen })
        .eq('session_id', sessionId)
        .eq('user_id', userId);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('❌ Error updating last seen:', error);
      throw error;
    }
  }

  private setupPresenceChannel(sessionId: string, userId: string): void {
    this.cleanupPresenceChannel();

    this.presenceChannel = supabase
      .channel(`presence_${sessionId}`)
      .on('presence', { event: 'sync' }, () => {
        console.log('🔄 Presence sync');
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        console.log('👋 User joined:', key, newPresences);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        console.log('👋 User left:', key, leftPresences);
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          // Track presence
          await this.presenceChannel?.track({
            user_id: userId,
            session_id: sessionId,
            online_at: new Date().toISOString(),
          });
        }
      });
  }

  private cleanupPresenceChannel(): void {
    if (this.presenceChannel) {
      this.presenceChannel.unsubscribe();
      this.presenceChannel = null;
    }
  }

  private async handleReconnection(): Promise<void> {
    const { sessionId, userId } = this.connectionState;
    if (!sessionId || !userId) return;

    console.log('🔄 Attempting to reconnect...');

    try {
      // Update connection status
      await this.updatePlayerConnection(sessionId, userId, true);
      
      // Reset reconnect attempts on successful reconnection
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;

      // Restart heartbeat
      this.startHeartbeat();

      // Restart presence channel
      this.setupPresenceChannel(sessionId, userId);

      console.log('✅ Reconnection successful');
    } catch (error) {
      console.error('❌ Reconnection failed:', error);
      this.scheduleReconnect();
    }
  }

  private async handleDisconnection(): Promise<void> {
    const { sessionId, userId } = this.connectionState;
    if (!sessionId || !userId) return;

    console.log('🔌 Handling disconnection...');

    try {
      // Update connection status
      await this.updatePlayerConnection(sessionId, userId, false);
    } catch (error) {
      console.error('❌ Error handling disconnection:', error);
    }

    this.stopHeartbeat();
    this.cleanupPresenceChannel();
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`⏳ Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);

    setTimeout(() => {
      if (this.connectionState.isOnline) {
        this.handleReconnection();
      }
    }, delay);
  }

  /**
   * Check if a player is considered disconnected
   */
  static isPlayerDisconnected(lastSeen: string, threshold: number = 60000): boolean {
    const lastSeenTime = new Date(lastSeen).getTime();
    const now = Date.now();
    return (now - lastSeenTime) > threshold;
  }

  /**
   * Get connection state
   */
  getConnectionState(): ConnectionState {
    return { ...this.connectionState };
  }
}

export const connectionService = ConnectionService.getInstance();
export { ConnectionService };
