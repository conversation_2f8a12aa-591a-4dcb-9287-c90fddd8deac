-- FINAL FIX for Multiplayer <PERSON>LS Policies
-- This script completely removes and recreates all policies to fix recursion

-- 1. DISABLE RLS temporarily to clean up
ALTER TABLE game_sessions DISABLE ROW LEVEL SECURITY;
ALTER TABLE game_players DISABLE ROW LEVEL SECURITY;

-- 2. DROP ALL existing policies
DROP POLICY IF EXISTS "Users can read their game sessions" ON game_sessions;
DROP POLICY IF EXISTS "Users can create game sessions" ON game_sessions;
DROP POLICY IF EXISTS "Host can update game sessions" ON game_sessions;
DROP POLICY IF EXISTS "Host can delete game sessions" ON game_sessions;

DROP POLICY IF EXISTS "Users can read game players" ON game_players;
DROP POLICY IF EXISTS "Users can join game sessions" ON game_players;
DROP POLICY IF EXISTS "Users can update their player data" ON game_players;
DROP POLICY IF EXISTS "Users can leave or host can remove players" ON game_players;

-- 3. CREATE SIMPLE, NON-RECURSIVE POLICIES

-- Game Sessions Policies
CREATE POLICY "Enable read for session participants" ON game_sessions
    FOR SELECT USING (
        auth.uid() = created_by OR 
        auth.uid() = host_id
    );

CREATE POLICY "Enable insert for authenticated users" ON game_sessions
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Enable update for host" ON game_sessions
    FOR UPDATE USING (auth.uid() = host_id);

CREATE POLICY "Enable delete for host" ON game_sessions
    FOR DELETE USING (auth.uid() = host_id);

-- Game Players Policies (SIMPLIFIED - NO RECURSION)
CREATE POLICY "Enable read for own records" ON game_players
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Enable insert for authenticated users" ON game_players
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable update for own records" ON game_players
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Enable delete for own records" ON game_players
    FOR DELETE USING (auth.uid() = user_id);

-- 4. RE-ENABLE RLS
ALTER TABLE game_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_players ENABLE ROW LEVEL SECURITY;

-- 5. GRANT necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON game_sessions TO authenticated;
GRANT ALL ON game_players TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- 6. Test the setup
SELECT 'RLS policies recreated successfully' as status;
