import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/auth-context';
import { useToast } from '@/hooks/use-toast';
import PrivateGameLobby from '@/components/multiplayer/PrivateGameLobby';
import { GameSession } from '@/types/game';
import { getSessionData } from '@/services/onlineGameService';

const Lobby: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, isLoggedIn } = useAuth();
  const { toast } = useToast();

  const [session, setSession] = useState<GameSession | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get state from navigation
  const { sessionId, isHost, roomName } = location.state || {};

  useEffect(() => {
    // Redirect if not logged in
    if (!isLoggedIn || !user) {
      toast({
        title: "Accesso richiesto",
        description: "Devi essere loggato per accedere alla lobby",
        variant: "destructive",
      });
      navigate('/');
      return;
    }

    // Redirect if no session data
    if (!sessionId) {
      toast({
        title: "Sessione non trovata",
        description: "Dati della sessione mancanti",
        variant: "destructive",
      });
      navigate('/');
      return;
    }

    // Load session data
    loadSessionData();
  }, [sessionId, isLoggedIn, user, navigate, toast]);

  const loadSessionData = async () => {
    if (!sessionId) return;

    try {
      setLoading(true);
      setError(null);

      const sessionData = await getSessionData(sessionId);
      setSession(sessionData);

      // Check if user is actually in this session
      const userInSession = sessionData.players.some(p => p.user_id === user?.id);
      if (!userInSession) {
        throw new Error('Non sei autorizzato ad accedere a questa sessione');
      }

    } catch (err) {
      console.error('Error loading session data:', err);
      const errorMessage = err instanceof Error ? err.message : 'Errore nel caricamento della sessione';
      setError(errorMessage);
      
      toast({
        title: "Errore",
        description: errorMessage,
        variant: "destructive",
      });

      // Redirect to home after error
      setTimeout(() => navigate('/'), 2000);
    } finally {
      setLoading(false);
    }
  };

  const handleSessionUpdate = (updatedSession: GameSession) => {
    setSession(updatedSession);
  };

  const handleGameStart = () => {
    // Navigate to game page
    navigate('/game', {
      state: {
        sessionId: session?.id,
        isOnline: true,
        isPrivate: true,
        victoryPoints: session?.victory_points || 31,
      },
    });
  };

  const handleLeaveLobby = () => {
    navigate('/');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-romagna-wood to-romagna-darkWood flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white text-lg font-semibold">Caricamento lobby...</p>
        </div>
      </div>
    );
  }

  if (error || !session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-romagna-wood to-romagna-darkWood flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h2 className="text-white text-2xl font-bold mb-2">Errore</h2>
          <p className="text-white/80 mb-4">{error || 'Sessione non trovata'}</p>
          <p className="text-white/60 text-sm">Reindirizzamento alla home...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-romagna-wood to-romagna-darkWood">
      <PrivateGameLobby
        session={session}
        currentUserId={user?.id || ''}
        isHost={isHost || false}
        roomName={roomName || session.room_name || ''}
        onSessionUpdate={handleSessionUpdate}
        onGameStart={handleGameStart}
        onLeaveLobby={handleLeaveLobby}
      />
    </div>
  );
};

export default Lobby;
