import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/context/auth-context';
import { useToast } from '@/hooks/use-toast';
import { connectionService } from '@/services/connectionService';
import { subscribeToGameSession, getSessionData } from '@/services/onlineGameService';
import { GameSession, RealtimeGameEvent } from '@/types/game';
import { RealtimeChannel } from '@supabase/supabase-js';

interface UseConnectionManagerProps {
  sessionId?: string;
  isOnline: boolean;
  isPrivate: boolean;
  onSessionUpdate?: (session: GameSession) => void;
  onPlayerDisconnected?: (playerId: string) => void;
  onPlayerReconnected?: (playerId: string) => void;
}

interface UseConnectionManagerReturn {
  isConnected: boolean;
  disconnectedPlayers: string[];
  reconnectAttempts: number;
  startMonitoring: () => Promise<void>;
  stopMonitoring: () => Promise<void>;
}

export const useConnectionManager = ({
  sessionId,
  isOnline,
  isPrivate,
  onSessionUpdate,
  onPlayerDisconnected,
  onPlayerReconnected,
}: UseConnectionManagerProps): UseConnectionManagerReturn => {
  const { user } = useAuth();
  const { toast } = useToast();

  const [isConnected, setIsConnected] = useState(navigator.onLine);
  const [disconnectedPlayers, setDisconnectedPlayers] = useState<string[]>([]);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [realtimeChannel, setRealtimeChannel] = useState<RealtimeChannel | null>(null);

  // Only manage connections for online private games
  const shouldManageConnection = isOnline && isPrivate && sessionId && user;

  // Handle realtime events
  const handleRealtimeEvent = useCallback((event: RealtimeGameEvent) => {
    console.log('🔄 Connection event:', event);

    switch (event.type) {
      case 'player_disconnected':
        const disconnectedPlayerId = event.payload.user_id;
        setDisconnectedPlayers(prev => {
          if (!prev.includes(disconnectedPlayerId)) {
            return [...prev, disconnectedPlayerId];
          }
          return prev;
        });
        
        if (onPlayerDisconnected) {
          onPlayerDisconnected(disconnectedPlayerId);
        }

        toast({
          title: "Giocatore disconnesso",
          description: `${event.payload.username || 'Un giocatore'} si è disconnesso`,
          variant: "destructive",
        });
        break;

      case 'player_joined':
        const reconnectedPlayerId = event.payload.user_id;
        setDisconnectedPlayers(prev => prev.filter(id => id !== reconnectedPlayerId));
        
        if (onPlayerReconnected) {
          onPlayerReconnected(reconnectedPlayerId);
        }

        toast({
          title: "Giocatore riconnesso",
          description: `${event.payload.username || 'Un giocatore'} si è riconnesso`,
        });
        break;
    }

    // Update session data
    if (onSessionUpdate && sessionId) {
      getSessionData(sessionId)
        .then(onSessionUpdate)
        .catch(console.error);
    }
  }, [onSessionUpdate, onPlayerDisconnected, onPlayerReconnected, sessionId, toast]);

  // Start connection monitoring
  const startMonitoring = useCallback(async () => {
    if (!shouldManageConnection) return;

    try {
      console.log('🔄 Starting connection monitoring for session:', sessionId);

      // Start connection service monitoring
      await connectionService.startSessionMonitoring(sessionId!, user!.id);

      // Subscribe to realtime events
      if (sessionId) {
        const channel = subscribeToGameSession(sessionId, handleRealtimeEvent);
        setRealtimeChannel(channel);
      }

      setIsConnected(true);
      setReconnectAttempts(0);

      console.log('✅ Connection monitoring started');
    } catch (error) {
      console.error('❌ Error starting connection monitoring:', error);
      setIsConnected(false);
    }
  }, [shouldManageConnection, sessionId, user, handleRealtimeEvent]);

  // Stop connection monitoring
  const stopMonitoring = useCallback(async () => {
    try {
      console.log('🛑 Stopping connection monitoring');

      // Stop connection service monitoring
      await connectionService.stopSessionMonitoring();

      // Unsubscribe from realtime events
      if (realtimeChannel) {
        realtimeChannel.unsubscribe();
        setRealtimeChannel(null);
      }

      setDisconnectedPlayers([]);
      setReconnectAttempts(0);

      console.log('✅ Connection monitoring stopped');
    } catch (error) {
      console.error('❌ Error stopping connection monitoring:', error);
    }
  }, [realtimeChannel]);

  // Handle network status changes
  useEffect(() => {
    const handleOnline = () => {
      console.log('🌐 Network back online');
      setIsConnected(true);
      if (shouldManageConnection) {
        startMonitoring();
      }
    };

    const handleOffline = () => {
      console.log('🌐 Network offline');
      setIsConnected(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [shouldManageConnection, startMonitoring]);

  // Auto-start monitoring when conditions are met
  useEffect(() => {
    if (shouldManageConnection && isConnected) {
      startMonitoring();
    }

    return () => {
      if (shouldManageConnection) {
        stopMonitoring();
      }
    };
  }, [shouldManageConnection, isConnected, startMonitoring, stopMonitoring]);

  // Monitor connection state from service
  useEffect(() => {
    const checkConnectionState = () => {
      const state = connectionService.getConnectionState();
      setIsConnected(state.isOnline);
    };

    const interval = setInterval(checkConnectionState, 5000); // Check every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return {
    isConnected,
    disconnectedPlayers,
    reconnectAttempts,
    startMonitoring,
    stopMonitoring,
  };
};
