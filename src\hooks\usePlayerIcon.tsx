import React from "react";
import { User, Cpu } from "lucide-react";
import { getPlayerTitle } from "@/services/playerTitlesService";
import { useUserState } from "./useUserState";

/**
 * Hook personalizzato per gestire le icone dinamiche dei giocatori
 * Restituisce l'icona appropriata basata su tipo di giocatore ed esperienza
 */
export const usePlayerIcon = () => {
  const { userState } = useUserState(); // 🚀 NUOVO: Usa il sistema di stato globale

  const getPlayerIcon = (
    isHuman: boolean,
    teamId: number,
    playerName?: string,
    isCurrentPlayer?: boolean
  ) => {
    // Stili migliorati per i team con maggiore contrasto e distinzione visiva
    const teamStyles = {
      0: {
        iconColor: "text-amber-700",
        bgColor: "bg-gradient-to-br from-amber-100 to-yellow-200",
        borderColor: "border-amber-600",
        shadowColor: "shadow-amber-400/30",
        textColor: "text-amber-800",
      },
      1: {
        iconColor: "text-red-700",
        bgColor: "bg-gradient-to-br from-red-100 to-rose-200",
        borderColor: "border-red-600",
        shadowColor: "shadow-red-400/30",
        textColor: "text-red-800",
      },
    };

    // Rimosse variabili non utilizzate dopo la semplificazione delle icone // Se è il giocatore umano "Tu", usa icona dinamica basata sull'esperienza
    if (isHuman && playerName === "Tu") {
      const playerTitle = getPlayerTitle(userState.level);
      return (
        <img
          src={playerTitle.rankImage}
          alt={playerTitle.title}
          className={`${
            isCurrentPlayer ? "h-10 w-10" : "h-8 w-8"
          } object-contain drop-shadow-sm`}
          title={`${playerTitle.title} (Livello ${userState.level})`}
        />
      );
    }

    // Per altri giocatori umani, usa l'icona User senza cerchi
    if (isHuman) {
      return (
        <img
          src="/images/icons/userpollo.png"
          alt="User"
          className={`${
            isCurrentPlayer ? "h-10 w-10" : "h-8 w-8"
          } object-contain drop-shadow-sm`}
        />
      );
    }

    // Per AI: se è del team 0 (partner), usa userpollo; se è del team 1 (avversario), usa cpupollo
    const aiIconSrc =
      teamId === 0
        ? "/images/icons/userpollo.png"
        : "/images/icons/cpupollo.png";
    const aiAltText = teamId === 0 ? "AI Partner" : "AI Opponent";

    return (
      <img
        src={aiIconSrc}
        alt={aiAltText}
        className={`${
          isCurrentPlayer ? "h-10 w-10" : "h-8 w-8"
        } object-contain drop-shadow-sm`}
      />
    );
  };
  // Funzione per ottenere informazioni dettagliate del rank (usata nei layout estesi)
  const getPlayerRankInfo = (playerName?: string) => {
    if (playerName === "Tu") {
      const playerTitle = getPlayerTitle(userState.level);
      return {
        title: playerTitle.title,
        level: userState.level,
        rankImage: playerTitle.rankImage,
        description: playerTitle.description,
      };
    }
    return null;
  };

  return { getPlayerIcon, getPlayerRankInfo };
};
