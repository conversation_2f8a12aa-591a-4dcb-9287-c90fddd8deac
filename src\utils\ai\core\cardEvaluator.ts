/**
 * 🎯 Card Evaluator - Sistema Unificato di Valutazione Carte
 *
 * Centralizza tutte le logiche di valutazione delle carte per eliminare duplicazioni
 */

import { Card, Suit, Rank } from "../../game/cardUtils";
import { GameState } from "../../game/gameLogic";
import { CardMemory } from "../types";
import { createSafeCardMemory } from "../memory/analyzer";

/**
 * Valutatore centralizzato per tutte le carte
 */
export class CardEvaluator {
  // Cache rimossi per semplificazione

  /**
   * 🎯 Valore in punti di una carta (sistema unificato)
   */
  getCardValue(card: Card): number {
    switch (card.rank) {
      case Rank.Ace:
        return 1.0; // Asso vale 1 punto
      case Rank.Three:
      case Rank.Two:
      case Rank.King:
      case Rank.Horse:
      case Rank.Jack:
        return 0.3; // Figure valgono 0.3 punti
      default:
        return 0; // Carte dal 4 al 7 non valgono nulla
    }
  }

  /**
   * 🎯 Ordine di forza di una carta (sistema unificato)
   */
  getCardOrder(card: Card): number {
    const rankOrder: Record<string, number> = {
      "3": 10, // Highest
      "2": 9,
      A: 8,
      K: 7,
      H: 6,
      J: 5,
      "7": 4,
      "6": 3,
      "5": 2,
      "4": 1, // Lowest
    };

    return rankOrder[card.rank as string] || 0;
  }

  /**
   * 🎯 Score complessivo di forza (ordine + valore + contesto)
   */
  getCardStrengthScore(card: Card, isTrump = false): number {
    const order = this.getCardOrder(card);
    const value = this.getCardValue(card);
    const trumpBonus = isTrump ? 5 : 0;

    return order + value * 2 + trumpBonus;
  }
  /**
   * 🎯 NUOVA REGOLA: Verifica se una carta è strategica (da conservare)
   * Solo i 2 sono sempre strategici da conservare
   * I 3 non di briscola sono per aprire e prendere punti!
   */
  isStrategicCard(card: Card): boolean {
    return card.rank === Rank.Two; // Solo i 2 sono strategici da conservare
  }

  /**
   * 🎯 NUOVA FUNZIONE: Verifica se un 3 è buono per apertura e controllo
   */
  isThreeForOpening(card: Card, trumpSuit: Suit | null): boolean {
    return card.rank === Rank.Three && card.suit !== trumpSuit;
  }

  /**
   * 🎯 Verifica se una carta è buona per dare punti al compagno
   */
  isGoodForTeammate(card: Card): boolean {
    return (
      card.rank === Rank.Ace || // Asso: ottimo (1 punto)
      card.rank === Rank.King || // Re: buono (0.3 punti)
      card.rank === Rank.Horse || // Cavallo: buono (0.3 punti)
      card.rank === Rank.Jack // Fante: buono (0.3 punti)
    );
    // Esclude 2 e 3 che sono strategiche
  }

  /**
   * 🎯 Verifica se una carta può vincere la presa corrente
   */
  canWinCurrentTrick(
    card: Card,
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null
  ): boolean {
    if (!currentTrick || currentTrick.length === 0) return true;

    const isTrump = card.suit === trumpSuit;

    for (const trickCard of currentTrick) {
      const trickIsTrump = trickCard.suit === trumpSuit;

      if (isTrump && !trickIsTrump) {
        // Nostra carta è briscola, quella in presa no: vince sempre
        continue;
      } else if (!isTrump && trickIsTrump) {
        // Nostra carta non è briscola, quella in presa sì: non può vincere
        return false;
      } else if (isTrump && trickIsTrump) {
        // Entrambe briscole: confronta ordini
        if (this.getCardOrder(card) <= this.getCardOrder(trickCard)) {
          return false;
        }
      } else {
        // Nessuna delle due è briscola
        if (card.suit === leadSuit && trickCard.suit === leadSuit) {
          if (this.getCardOrder(card) <= this.getCardOrder(trickCard)) {
            return false;
          }
        } else if (card.suit !== leadSuit && trickCard.suit === leadSuit) {
          return false;
        }
      }
    }

    return true;
  }
  /**
   * 🎯 NUOVO: Verifica se è una situazione di vincita ovvia che non dovrebbe essere bloccata dai controlli anti-spreco
   */
  isObviousWinSituation(
    card: Card,
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    trickValue: number = 0
  ): boolean {
    if (!this.canWinCurrentTrick(card, currentTrick, leadSuit, trumpSuit)) {
      return false;
    }

    // Se abbiamo il 3 (carta più forte) del seme di uscita e non è briscola
    if (
      card.rank === "3" &&
      card.suit === leadSuit &&
      card.suit !== trumpSuit
    ) {
      // Il 3 del seme di uscita vince sempre quando non è briscola
      return true;
    }

    // Se abbiamo il 2 del seme di uscita e non ci sono 3 sul tavolo
    if (
      card.rank === "2" &&
      card.suit === leadSuit &&
      card.suit !== trumpSuit
    ) {
      const hasThreeInTrick = currentTrick.some(
        (c) => c.rank === "3" && c.suit === leadSuit
      );
      if (!hasThreeInTrick) {
        return true;
      }
    }

    // 🎯 NUOVA LOGICA STRATEGICA: Posizione nel turno e probabilità avversari
    const isTrump = card.suit === trumpSuit;
    const isNonTrumpStrategic =
      !isTrump && (card.rank === "3" || card.rank === "2");

    if (isNonTrumpStrategic) {
      // Siamo primi o secondi a giocare? (posizione favorevole)
      const isEarlyPosition = currentTrick.length <= 1;

      // Logica più permissiva per carte strategiche non-briscola:
      // 1. Se siamo primi/secondi nel turno
      // 2. Se c'è già almeno 1 punto sul tavolo
      // 3. Se il rischio che gli avversari abbiano l'asso è basso
      if (isEarlyPosition || trickValue >= 1) {
        return true;
      }

      // Anche in posizione più tardiva, considera che l'avversario
      // potrebbe avere l'asso e dobbiamo anticiparlo
      if (currentTrick.length >= 2) {
        // Se vediamo che gli altri hanno giocato carte deboli,
        // è probabile che qualcuno abbia ancora l'asso
        const hasHighCardsInTrick = currentTrick.some(
          (c) => c.rank === "A" || c.rank === "3" || c.rank === "2"
        );

        if (!hasHighCardsInTrick) {
          // Nessuna carta forte giocata finora = probabile che qualcuno abbia l'asso
          return true;
        }
      }
    }

    // Logica per briscole strategiche (più restrittiva)
    if (isTrump && (card.rank === "3" || card.rank === "2")) {
      // Per briscole strategiche: richiede ≥2 punti sul tavolo
      if (trickValue >= 2) {
        const trumpsInTrick = currentTrick.filter((c) => c.suit === trumpSuit);
        if (trumpsInTrick.length === 0) {
          return true; // Prima briscola giocata con buoni punti
        }

        const strongerTrumpsInTrick = trumpsInTrick.filter(
          (c) => this.getCardOrder(c) > this.getCardOrder(card)
        );
        return strongerTrumpsInTrick.length === 0;
      }
    }

    // Se abbiamo una briscola alta e non ci sono briscole più forti nel trick
    if (card.suit === trumpSuit && (card.rank === "3" || card.rank === "2")) {
      const trumpsInTrick = currentTrick.filter((c) => c.suit === trumpSuit);
      if (trumpsInTrick.length === 0) {
        return true; // Prima briscola giocata
      }

      const strongerTrumpsInTrick = trumpsInTrick.filter(
        (c) => this.getCardOrder(c) > this.getCardOrder(card)
      );
      return strongerTrumpsInTrick.length === 0;
    }

    return false;
  }
  /**
   * 🎯 Trova la carta di minor valore che può seguire le regole
   * AGGIORNATO: Usa il nuovo ordinamento per scartare (4,5,6,7,J,H,K,2,3,A)
   */
  findLowestValidCard(
    cards: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null
  ): Card {
    return this.findBestDiscardCard(cards, leadSuit, trumpSuit);
  }

  /**
   * 🎯 Selezione seme briscola ottimale
   */
  selectOptimalTrumpSuit(hand: Card[]): Suit {
    // 1. Verifica se ha una "maraffa" (A + 2 + 3 dello stesso seme)
    const maraffa = this.findMaraffa(hand);
    if (maraffa) return maraffa;

    // 2. Conta carte per seme
    const suitCounts: Record<string, number> = {};
    Object.values(Suit).forEach((suit) => (suitCounts[suit] = 0));

    hand.forEach((card) => suitCounts[card.suit]++);

    // 3. Cerca seme con 4+ carte
    const dominantSuit = Object.entries(suitCounts).find(
      ([_, count]) => count >= 4
    );
    if (dominantSuit) return dominantSuit[0] as Suit;

    // 4. Seme con carte più forti
    return this.chooseSuitWithHighestCards(hand);
  }

  /**
   * 🎯 Trova maraffa (A + 2 + 3 dello stesso seme)
   */
  private findMaraffa(hand: Card[]): Suit | null {
    const suitCards: Record<string, Card[]> = {};
    Object.values(Suit).forEach((suit) => (suitCards[suit] = []));

    hand.forEach((card) => suitCards[card.suit].push(card));

    for (const [suit, cards] of Object.entries(suitCards)) {
      const ranks = cards.map((c) => c.rank);
      if (
        ranks.includes(Rank.Ace) &&
        ranks.includes(Rank.Two) &&
        ranks.includes(Rank.Three)
      ) {
        return suit as Suit;
      }
    }

    return null;
  }

  /**
   * 🎯 Scegli seme con carte più forti
   */
  private chooseSuitWithHighestCards(hand: Card[]): Suit {
    const suitStrengths: Record<string, number> = {};
    Object.values(Suit).forEach((suit) => (suitStrengths[suit] = 0));

    hand.forEach((card) => {
      suitStrengths[card.suit] += this.getCardStrengthScore(card);
    });

    return Object.entries(suitStrengths).reduce(
      (best, [suit, strength]) =>
        strength > suitStrengths[best] ? suit : best,
      Object.keys(suitStrengths)[0]
    ) as Suit;
  }

  // Cache cleanup rimosso - non più necessario

  /**
   * 🎯 NUOVO: Strategia di caricamento del trucco (trick loading)
   * Quando il compagno sta vincendo e siamo l'ultimo a giocare,
   * dovremmo giocare le carte di maggior valore per massimizzare i punti della squadra
   */
  shouldLoadTrick(
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    leadPlayer: number,
    playerIndex: number,
    state: GameState
  ): {
    shouldLoad: boolean;
    reason: string;
    loadingCards: Card[];
  } {
    // 🎯 CORREZIONE: Verifica corretta se siamo l'ultimo a giocare
    const isLastPlayer = currentTrick.length === 3;
    if (!isLastPlayer) {
      return {
        shouldLoad: false,
        reason: "Non ultimo giocatore",
        loadingCards: [],
      };
    }

    // Verifica se il compagno sta vincendo
    const winnerAnalysis = this.analyzeCurrentWinner(
      currentTrick,
      leadSuit,
      trumpSuit,
      leadPlayer,
      state
    );

    const myTeam = state.players[playerIndex].team;
    const teammateIsWinning =
      winnerAnalysis.winnerTeam === myTeam &&
      winnerAnalysis.winnerIndex !== (playerIndex - leadPlayer + 4) % 4; // 🎯 CORREZIONE: Calcolo corretto

    if (!teammateIsWinning) {
      return {
        shouldLoad: false,
        reason: "Compagno non sta vincendo",
        loadingCards: [],
      };
    }

    // Calcola il valore attuale del trucco
    const currentTrickValue = currentTrick.reduce(
      (sum, card) => sum + this.getCardValue(card),
      0
    );

    // Determina le condizioni per il caricamento
    const trickNumber = state.trickNumber ?? 1;
    const isLastTrick = trickNumber === 10;
    const isEndGame = trickNumber >= 8;
    const isValuableTrick = currentTrickValue >= 1;

    const shouldLoad =
      isValuableTrick || isEndGame || isLastTrick || currentTrickValue >= 0.5;

    if (!shouldLoad) {
      return {
        shouldLoad: false,
        reason: `Trucco non abbastanza prezioso (${currentTrickValue} punti)`,
        loadingCards: [],
      };
    }

    // Ottieni le carte di caricamento appropriate
    const playerHand = state.players[playerIndex].hand;
    const loadingCards = this.getLoadingCards(playerHand);

    return {
      shouldLoad: true,
      reason: `Caricamento trucco: compagno vincente, ${currentTrickValue} punti attuali`,
      loadingCards,
    };
  }

  /**
   * 🎯 NUOVO: Strategia cooperativa avanzata - Valuta come giocare in base a chi prende
   */
  getCooperativePlayStrategy(
    availableCards: Card[],
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    leadPlayer: number,
    playerIndex: number,
    state: GameState
  ): {
    strategy: "give_points" | "discard_low" | "compete" | "support_passive";
    recommendedCard: Card;
    reason: string;
  } {
    // 🎯 PRIMO A GIOCARE: STRATEGIA INTELLIGENTE (NON A CASO!)
    if (currentTrick.length === 0) {
      console.log(`[STRATEGIA] 🎯 PRIMO A GIOCARE - Strategia intelligente`);

      const trickNumber = state.trickNumber ?? 1;
      const isEarlyGame = trickNumber <= 3;

      // 🎯 CASO SPECIFICO: 3 non-briscola SOLO nelle prime 3 mani come primo giocatore
      // Per controllare il gioco e avere buone probabilità di prendere
      if (isEarlyGame) {
        const opening3s = availableCards.filter(
          (card) => card.rank === "3" && card.suit !== trumpSuit
        );

        if (opening3s.length > 0) {
          const best3 = opening3s[0];
          console.log(
            `[STRATEGIA] 🎯 PRIMO (prime mani): Controllo con 3 di ${best3.suit}!`
          );

          return {
            strategy: "compete",
            recommendedCard: best3,
            reason: `🎯 CONTROLLO PRIME MANI: 3 di ${best3.suit} come primo giocatore per controllare il tavolo`,
          };
        }
      }

      // 🎯 STRATEGIA NORMALE: Carte senza valore per aprire
      // NON sprecare Assi, Figure, o 2 per aprire a caso!
      const safeOpeningCards = availableCards.filter(
        (card) =>
          this.getCardValue(card) === 0 && // Solo carte senza punti
          card.suit !== trumpSuit && // Evita briscole
          card.rank !== "2" // Evita i 2 strategici
      );

      if (safeOpeningCards.length > 0) {
        // Usa la carta più alta tra quelle senza valore (7 > 6 > 5 > 4)
        safeOpeningCards.sort(
          (a, b) => this.getCardOrder(b) - this.getCardOrder(a)
        );
        const selectedCard = safeOpeningCards[0];

        return {
          strategy: "compete",
          recommendedCard: selectedCard,
          reason: `🎯 APERTURA SICURA: ${selectedCard.rank} di ${selectedCard.suit} - conservo carte di valore per momenti giusti`,
        };
      }

      // 🎯 SE NON HAI CARTE SICURE: Prendi la meno preziosa
      const sortedByValue = [...availableCards].sort(
        (a, b) => this.getCardValue(a) - this.getCardValue(b)
      );

      // Evita il 3 di briscola se possibile
      const nonTrump3 = sortedByValue.filter(
        (card) => !(card.rank === "3" && card.suit === trumpSuit)
      );

      const cardToPlay = nonTrump3.length > 0 ? nonTrump3[0] : sortedByValue[0];

      return {
        strategy: "compete",
        recommendedCard: cardToPlay,
        reason: `🎯 APERTURA ULTIMA RISORSA: ${cardToPlay.rank} di ${cardToPlay.suit} - evito 3 di briscola`,
      };
    }

    // 🎯 CONTROLLO PRIORITARIO: Assi sicuri da giocare
    const safeAcePlay = this.selectOptimalCardWithSafetyCheck(
      availableCards,
      currentTrick,
      leadSuit,
      trumpSuit,
      state,
      playerIndex
    );

    if (safeAcePlay) {
      console.log(`[SAFE ACE PRIORITY] ${safeAcePlay.reason}`);
      return {
        strategy: safeAcePlay.strategy as
          | "compete"
          | "give_points"
          | "discard_low"
          | "support_passive",
        recommendedCard: safeAcePlay.recommendedCard,
        reason: safeAcePlay.reason,
      };
    }

    // 🚨 PRIORITÀ ASSOLUTA: ASSO SUL TAVOLO = PRENDI SEMPRE CON QUALSIASI CARTA CHE PUÒ!
    const hasAceOnTable = currentTrick.some((card) => card.rank === "A");
    if (hasAceOnTable) {
      // Cerca QUALSIASI carta che può vincere, inclusi 2 e 3!
      const winningCards = availableCards.filter((card) => {
        return this.canWinCurrentTrick(card, currentTrick, leadSuit, trumpSuit);
      });

      if (winningCards.length > 0) {
        // 🎯 OTTIMIZZAZIONE: Logica più chiara per la priorità delle carte
        winningCards.sort((a, b) => {
          const aIsStrategic = this.isStrategicCard(a);
          const bIsStrategic = this.isStrategicCard(b);

          // Prima priorità: carte non strategiche
          if (!aIsStrategic && bIsStrategic) return -1;
          if (aIsStrategic && !bIsStrategic) return 1;

          // Se entrambe strategiche: 2 prima di 3 (conserva il 3 se possibile)
          if (aIsStrategic && bIsStrategic) {
            if (a.rank === "2" && b.rank === "3") return -1;
            if (a.rank === "3" && b.rank === "2") return 1;
          }

          // Altrimenti: carta più debole che può ancora vincere
          return this.getCardStrengthScore(a) - this.getCardStrengthScore(b);
        });

        const bestCard = winningCards[0];
        return {
          strategy: "compete",
          recommendedCard: bestCard,
          reason: `🔥 ASSO SUL TAVOLO (1 punto)! Prendo con ${bestCard.rank} - 1 punto vale QUALSIASI carta!`,
        };
      }
    }

    // 🎯 PRIORITÀ ALTA: SE C'È ALMENO 1 PUNTO SUL TAVOLO = PRENDI SEMPRE!
    const trickValue = currentTrick.reduce(
      (sum, card) => sum + this.getCardValue(card),
      0
    );

    if (trickValue >= 1.0) {
      const winningCards = availableCards.filter((card) =>
        this.canWinCurrentTrick(card, currentTrick, leadSuit, trumpSuit)
      );

      if (winningCards.length > 0) {
        // 🎯 OTTIMIZZAZIONE: Per 1+ punti, usa la carta più debole che può vincere
        winningCards.sort(
          (a, b) => this.getCardStrengthScore(a) - this.getCardStrengthScore(b)
        );

        return {
          strategy: "compete",
          recommendedCard: winningCards[0],
          reason: `💰 ${trickValue.toFixed(
            1
          )} PUNTI SUL TAVOLO - Vale qualsiasi carta per vincere!`,
        };
      }
    } // 🎯 PRIORITÀ MEDIA: CON 0.6+ PUNTI, VALUTA L'USO DI CARTE STRATEGICHE
    if (trickValue >= 0.6) {
      // Crea memoria dalle informazioni di stato disponibili
      const memory = createSafeCardMemory(state);

      const strategicEvaluation = this.shouldUseStrategicCardsForPoints(
        availableCards,
        currentTrick,
        leadSuit,
        trumpSuit,
        trickValue,
        memory, // Usa memoria reale invece di null
        currentTrick.length === 3 // isLastPlayer
      );

      if (
        strategicEvaluation.shouldUse &&
        strategicEvaluation.recommendedCard
      ) {
        console.log(`[STRATEGIA] ${strategicEvaluation.reason}`);

        return {
          strategy: "compete",
          recommendedCard: strategicEvaluation.recommendedCard,
          reason: strategicEvaluation.reason,
        };
      }

      // Prima cerca carte NON strategiche che possono vincere
      const winningCards = availableCards.filter((card) =>
        this.canWinCurrentTrick(card, currentTrick, leadSuit, trumpSuit)
      );

      if (winningCards.length > 0) {
        const nonStrategicWinning = winningCards.filter(
          (card) => !this.isStrategicCard(card)
        );

        if (nonStrategicWinning.length > 0) {
          nonStrategicWinning.sort(
            (a, b) =>
              this.getCardStrengthScore(a) - this.getCardStrengthScore(b)
          );
          return {
            strategy: "compete",
            recommendedCard: nonStrategicWinning[0],
            reason: `💰 ${trickValue.toFixed(1)} punti - Prendo con ${
              nonStrategicWinning[0].rank
            }, conservo 2 e 3 per situazioni migliori!`,
          };
        }
      }

      console.log(`[STRATEGIA] ${strategicEvaluation.reason}`);
    }

    // 🤝 STRATEGIA COLLABORATIVA: Determina chi sta vincendo
    const winnerAnalysis = this.analyzeCurrentWinner(
      currentTrick,
      leadSuit,
      trumpSuit,
      leadPlayer,
      state
    );

    const myTeam = state.players[playerIndex].team;
    const isMyTeamWinning = winnerAnalysis.winnerTeam === myTeam;
    const isOpponentWinning =
      winnerAnalysis.winnerTeam !== myTeam && winnerAnalysis.winnerTeam !== -1; // 🔥 NUOVA PRIORITÀ CRITICA: ULTIMO GIOCATORE CON CARTE DI VALORE
    const isLastPlayer = currentTrick.length === 3;

    if (isLastPlayer) {
      console.log(
        `[STRATEGIA] 🎯 ULTIMO GIOCATORE - Analisi priorità assoluta`
      );

      // 🎯 PRIORITÀ ASSOLUTA: Se posso prendere con carte di valore (Asso, Figure), prendo SEMPRE
      // anche se supero il compagno, purché non sprechi briscole strategiche
      const valueWinningCards = availableCards.filter((card) => {
        return (
          this.canWinCurrentTrick(card, currentTrick, leadSuit, trumpSuit) &&
          this.getCardValue(card) > 0 &&
          // Evita solo briscole strategiche (2,3 di briscola), non tutte le strategiche
          !(card.suit === trumpSuit && this.isStrategicCard(card))
        );
      });

      if (valueWinningCards.length > 0) {
        // Ordina per valore: Asso prima, poi figure
        valueWinningCards.sort((a, b) => {
          const aValue = this.getCardValue(a);
          const bValue = this.getCardValue(b);

          // Priorità al valore più alto
          if (aValue !== bValue) return bValue - aValue;

          // A parità di valore, carta più debole
          return this.getCardOrder(a) - this.getCardOrder(b);
        });

        const selectedCard = valueWinningCards[0];
        console.log(
          `[STRATEGIA] 🔥 ULTIMO GIOCATORE - Prendo con ${
            selectedCard.rank
          } (${this.getCardValue(selectedCard)} punti)`
        );

        return {
          strategy: "compete",
          recommendedCard: selectedCard,
          reason: `🔥 ULTIMO GIOCATORE: Prendo sempre con carte di valore! ${
            selectedCard.rank
          } = ${this.getCardValue(selectedCard)} punti garantiti`,
        };
      }

      // 🎯 PRIORITÀ ALTA: Se ci sono anche pochi punti (>0), prendi con carte non-briscola
      if (trickValue > 0) {
        const nonTrumpWinning = availableCards.filter((card) => {
          return (
            this.canWinCurrentTrick(card, currentTrick, leadSuit, trumpSuit) &&
            card.suit !== trumpSuit
          );
        });

        if (nonTrumpWinning.length > 0) {
          // Usa la carta più debole che può vincere
          nonTrumpWinning.sort(
            (a, b) =>
              this.getCardStrengthScore(a) - this.getCardStrengthScore(b)
          );

          console.log(
            `[STRATEGIA] 💰 ULTIMO GIOCATORE - Prendo ${trickValue.toFixed(
              1
            )} punti con carta non-briscola`
          );

          return {
            strategy: "compete",
            recommendedCard: nonTrumpWinning[0],
            reason: `💰 ULTIMO GIOCATORE: ${trickValue.toFixed(
              1
            )} punti sul tavolo - Prendo con ${
              nonTrumpWinning[0].rank
            } non-briscola`,
          };
        }
      } // 🎯 SOGLIA RISTRETTA: Valuta l'uso di carte strategiche
      // Crea memoria dalle informazioni di stato disponibili
      const memory = createSafeCardMemory(state);

      const strategicEvaluation = this.shouldUseStrategicCardsForPoints(
        availableCards,
        currentTrick,
        leadSuit,
        trumpSuit,
        trickValue,
        memory, // Usa memoria reale invece di null
        true // isLastPlayer
      );

      if (
        strategicEvaluation.shouldUse &&
        strategicEvaluation.recommendedCard
      ) {
        console.log(`[STRATEGIA] ${strategicEvaluation.reason}`);

        return {
          strategy: "compete",
          recommendedCard: strategicEvaluation.recommendedCard,
          reason: strategicEvaluation.reason,
        };
      }

      console.log(`[STRATEGIA] ${strategicEvaluation.reason}`);
    } // 🤝 SE IL MIO TEAM STA VINCENDO → PRIORITÀ ASSOLUTA: DARE SUBITO I PUNTI!
    if (isMyTeamWinning) {
      console.log(`[STRATEGIA] 🎯 TEAM VINCENTE - DEVE DARE PUNTI SUBITO!`);

      // 🔥 STRATEGIA CORRETTA: Le carte con punti vanno giocate SUBITO quando il team vince!
      // Non accumularle per dopo, ma usarle quando servono per massimizzare i punti della squadra

      const allPointCards = availableCards.filter((card) => {
        return this.getCardValue(card) > 0;
      });

      if (allPointCards.length > 0) {
        // 🎯 PRIORITÀ CORRETTA: ASSO PRIMA DI TUTTO quando il team vince!
        // Non conservare l'asso "per dopo" - usalo SUBITO per massimizzare i punti!
        allPointCards.sort((a, b) => {
          const aValue = this.getCardValue(a);
          const bValue = this.getCardValue(b);

          // 🔥 PRIORITÀ ASSOLUTA: Asso sempre per primo quando team vince
          if (a.rank === "A" && b.rank !== "A") return -1;
          if (b.rank === "A" && a.rank !== "A") return 1;

          // Poi per valore decrescente: dai prima le carte di maggior valore
          if (aValue !== bValue) return bValue - aValue;

          // A parità di valore, carta più forte
          return this.getCardOrder(b) - this.getCardOrder(a);
        });

        const bestPointCard = allPointCards[0];
        console.log(
          `[STRATEGIA] 🔥 TEAM VINCENTE: Gioco SUBITO ${
            bestPointCard.rank
          } (${this.getCardValue(bestPointCard)} punti) - NON accumulo!`
        );

        return {
          strategy: "give_points",
          recommendedCard: bestPointCard,
          reason: `🔥 STRATEGIA CORRETTA: Team vince = gioco SUBITO ${
            bestPointCard.rank
          } (${this.getCardValue(
            bestPointCard
          )} pt)! Non accumulo carte con punti!`,
        };
      }

      // Solo se NON hai carte con punti, allora scarta carte senza valore
      const safeCards = availableCards.filter(
        (card) => this.getCardValue(card) === 0 && !this.isStrategicCard(card)
      );

      if (safeCards.length > 0) {
        safeCards.sort((a, b) => this.getCardOrder(a) - this.getCardOrder(b));
        return {
          strategy: "give_points",
          recommendedCard: safeCards[0],
          reason: `🤝 TEAM vincente: Nessuna carta con punti, scarto sicuro ${safeCards[0].rank}`,
        };
      }

      // Ultimo resort: anche le carte strategiche, ma evita il 3 di briscola se possibile
      const strategicCards = availableCards.filter((card) =>
        this.isStrategicCard(card)
      );
      if (strategicCards.length > 0) {
        // Evita il 3 di briscola se ci sono alternative
        const nonTrump3 = strategicCards.filter(
          (card) => !(card.rank === "3" && card.suit === trumpSuit)
        );

        const cardToPlay =
          nonTrump3.length > 0 ? nonTrump3[0] : strategicCards[0];

        return {
          strategy: "give_points",
          recommendedCard: cardToPlay,
          reason: `🤝 TEAM vincente: Ultima opzione ${cardToPlay.rank} - conservo 3 di briscola se possibile`,
        };
      }

      // Fallback finale
      return {
        strategy: "give_points",
        recommendedCard: availableCards[0],
        reason: `🤝 TEAM vincente: Fallback`,
      };
    }

    // 🤝 SE AVVERSARI STANNO VINCENDO → NON REGALARE PUNTI
    if (isOpponentWinning) {
      const lowValueCards = availableCards.filter(
        (card) => this.getCardValue(card) === 0
      );

      if (lowValueCards.length > 0) {
        lowValueCards.sort(
          (a, b) => this.getCardOrder(a) - this.getCardOrder(b)
        );
        return {
          strategy: "discard_low",
          recommendedCard: lowValueCards[0],
          reason: `🤝 COLLABORATIVO: Avversari vincenti, scarto ${lowValueCards[0].rank} (0 punti)!`,
        };
      }

      const sortedByValue = [...availableCards].sort(
        (a, b) => this.getCardValue(a) - this.getCardValue(b)
      );
      return {
        strategy: "discard_low",
        recommendedCard: sortedByValue[0],
        reason: `🤝 COLLABORATIVO: Costretto a dare ${
          sortedByValue[0].rank
        } (${this.getCardValue(sortedByValue[0])} punti) agli avversari`,
      };
    }

    // 🤝 SITUAZIONE NEUTRA: CONTROLLO INTELLIGENTE ACCUMULO
    // Non giocare carte con punti a caso, ma evita accumuli eccessivi in modo intelligente
    const trickNumber = state.trickNumber ?? 1;
    const isLateGame = trickNumber >= 8; // Solo nelle ultime 3 mani

    // 🎯 CONTROLLO INTELLIGENTE: Se hai MOLTE carte con punti e siamo a metà partita
    const pointCards = availableCards.filter(
      (card) => this.getCardValue(card) > 0
    );
    const hasTooManyPointCards = pointCards.length >= 4; // 4+ carte con punti
    const isMidGame = trickNumber >= 4 && trickNumber <= 6;

    // Solo in situazioni estreme: troppi punti a metà partita
    if (hasTooManyPointCards && isMidGame) {
      console.log(
        `[STRATEGIA] ⚠️ CONTROLLO ACCUMULO: ${pointCards.length} carte con punti a metà partita`
      );

      // Gioca SOLO figure (non Assi!) e solo se non puoi fare altro
      const figures = pointCards.filter(
        (card) => card.rank === "K" || card.rank === "H" || card.rank === "J"
      );

      // E solo se hai davvero troppe figure
      if (figures.length >= 3) {
        figures.sort((a, b) => this.getCardValue(a) - this.getCardValue(b)); // Figure più deboli prima

        return {
          strategy: "support_passive",
          recommendedCard: figures[0],
          reason: `⚠️ CONTROLLO ESTREMO: Troppe figure (${figures.length}) - gioco ${figures[0].rank} per bilanciare mano`,
        };
      }
    }

    // 🤝 SITUAZIONE NEUTRA NORMALE: Valuta se competere CON SENSO
    const winningCards = availableCards.filter((card) =>
      this.canWinCurrentTrick(card, currentTrick, leadSuit, trumpSuit)
    );

    if (winningCards.length > 0 && trickValue >= 1) {
      winningCards.sort(
        (a, b) => this.getCardStrengthScore(a) - this.getCardStrengthScore(b)
      );
      return {
        strategy: "compete",
        recommendedCard: winningCards[0],
        reason: `🤝 COMPETIZIONE LOGICA: Situazione neutra, prendo ${trickValue.toFixed(
          1
        )} punti per il team!`,
      };
    }

    // Fallback
    const safeCards = availableCards.filter(
      (card) => this.getCardValue(card) === 0
    );
    if (safeCards.length > 0) {
      return {
        strategy: "support_passive",
        recommendedCard: safeCards[0],
        reason: `🤝 COLLABORATIVO: Supporto passivo con ${safeCards[0].rank}`,
      };
    }

    return {
      strategy: "support_passive",
      recommendedCard: availableCards[0],
      reason: "🤝 COLLABORATIVO: Fallback",
    };
  }
  /**
   * 🎯 ORDINE DI SCARTO: quando devi perdere e dare carte all'avversario
   * Da quello che PREFERISCI dare a quello che ODI dare di più
   * 4,5,6,7 (carte senza punti) → J,H,K,2,3 (carte da 0.3) → A (carta da 1 punto)
   */
  getDiscardOrderScore(card: Card): number {
    const discardOrder: Record<string, number> = {
      // CARTE SENZA PUNTI (0 punti) - le più felici da dare via
      "4": 1, // Primo da scartare (più felice di darlo)
      "5": 2,
      "6": 3,
      "7": 4, // Ultimo delle carte senza punti

      // CARTE DA 0.3 PUNTI - iniziano a dispiacere
      J: 5, // Fante - primo delle carte con punti da sacrificare
      H: 6, // Cavallo
      K: 7, // Re
      "2": 8, // 2
      "3": 9, // 3

      // ASSO (1 punto) - quello che odi di più dare!
      A: 10, // Asso - il più doloroso da perdere
    };

    return discardOrder[card.rank as string] || 0;
  }

  /**
   * 🎯 NUOVO: Trova la carta migliore da scartare quando non si può vincere
   * Rispetta l'ordine: 4,5,6,7,fante,cavallo,re,2,3,asso
   */
  findBestDiscardCard(
    cards: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null
  ): Card {
    if (!leadSuit) {
      // Nessun seme di uscita: priorità alle carte non-briscola, poi ordine di scarto
      const nonTrumpCards = cards.filter((card) => card.suit !== trumpSuit);

      if (nonTrumpCards.length > 0) {
        // Ordina per ordine di scarto (dal meno prezioso)
        nonTrumpCards.sort(
          (a, b) => this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b)
        );
        return nonTrumpCards[0];
      }

      // Solo briscole: usa ordine di scarto
      const sortedCards = [...cards].sort(
        (a, b) => this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b)
      );
      return sortedCards[0];
    }

    // Deve seguire il seme se possibile
    const leadSuitCards = cards.filter((card) => card.suit === leadSuit);

    if (leadSuitCards.length > 0) {
      // Ordina le carte del seme per ordine di scarto
      leadSuitCards.sort(
        (a, b) => this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b)
      );
      return leadSuitCards[0];
    }

    // Non ha il seme: priorità alle carte non-briscola, poi ordine di scarto
    const nonTrumpCards = cards.filter((card) => card.suit !== trumpSuit);

    if (nonTrumpCards.length > 0) {
      nonTrumpCards.sort(
        (a, b) => this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b)
      );
      return nonTrumpCards[0];
    }

    // Solo briscole rimaste: usa ordine di scarto
    const sortedCards = [...cards].sort(
      (a, b) => this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b)
    );
    return sortedCards[0];
  }

  /**
   * 🎯 Ottieni le carte migliori per il caricamento del trucco
   * PRIORITÀ CORRETTA: Fante/Cavallo/Re/Asso per dare punti, NON 2 e 3!
   */
  private getLoadingCards(hand: Card[]): Card[] {
    // 🎯 CORREZIONE: Separa carte per DARE punti da carte per PRENDERE
    const pointGivingCards = hand.filter((card) => {
      // Solo carte che danno punti MA non sono strategiche (2,3)
      return this.getCardValue(card) > 0 && !this.isStrategicCard(card);
    });

    const strategicCards = hand.filter((card) => this.isStrategicCard(card));

    // Ordina carte da dare per valore decrescente
    pointGivingCards.sort(
      (a, b) => this.getCardValue(b) - this.getCardValue(a)
    );

    // Le carte strategiche vengono per ultime e solo in casi estremi
    strategicCards.sort((a, b) => this.getCardValue(b) - this.getCardValue(a));

    // 🎯 PRIORITÀ CORRETTA: Prima Asso/Re/Cavallo/Fante, poi (solo se necessario) 2 e 3
    return [...pointGivingCards, ...strategicCards];
  }

  /**
   * 🎯 Ottieni la carta migliore per il caricamento del trucco
   * CORREZIONE: Preferisci carte appropriate per dare punti
   */
  getBestLoadingCard(
    availableCards: Card[],
    currentTrick: Card[],
    state: GameState
  ): Card | null {
    const loadingAnalysis = this.shouldLoadTrick(
      currentTrick,
      state.leadSuit,
      state.trumpSuit,
      state.leadPlayer ?? 0,
      state.currentPlayer,
      state
    );

    if (!loadingAnalysis.shouldLoad) {
      return null;
    }

    // 🎯 PRIORITÀ CORRETTA: Carte da DARE punti (non strategiche)
    const appropriateLoadingCards = availableCards.filter((card) => {
      return this.getCardValue(card) > 0 && !this.isStrategicCard(card);
    });

    if (appropriateLoadingCards.length > 0) {
      // Ordina per valore decrescente: dai prima l'asso, poi re, etc.
      appropriateLoadingCards.sort(
        (a, b) => this.getCardValue(b) - this.getCardValue(a)
      );
      return appropriateLoadingCards[0];
    }

    // Se non hai carte appropriate, cerca almeno carte senza valore
    const safeCards = availableCards.filter(
      (card) => this.getCardValue(card) === 0
    );
    if (safeCards.length > 0) {
      return safeCards[0];
    }

    // Solo come ultimo resort, considera carte strategiche
    const strategicCards = availableCards.filter((card) =>
      this.isStrategicCard(card)
    );
    if (strategicCards.length > 0) {
      console.warn(
        "[LOADING] ⚠️ Costretto a caricare con carta strategica - situazione subottimale!"
      );
      return strategicCards[0];
    }

    return null;
  }
  /**
   * 🎯 Verifica se un asso può essere giocato in sicurezza
   */
  canPlayAceSafely(
    ace: Card,
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    state: GameState,
    playerIndex: number
  ): boolean {
    // 🚨 REGOLA DRACONIANA: Se non sono il primo giocatore, l'asso è sicuro SOLO se:
    // 1. Può vincere la presa, O
    // 2. Il mio team sta già vincendo
    if (currentTrick.length > 0) {
      console.log(
        `[SAFE ACE CHECK] 🚨 Controllo draconiano per asso ${ace.suit}`
      );

      // Verifica se l'asso può vincere
      const aceCanWin = this.canWinCurrentTrick(
        ace,
        currentTrick,
        leadSuit,
        trumpSuit
      );

      if (!aceCanWin) {
        // Se l'asso non può vincere, verifica se il team sta vincendo
        const myTeam = state.players[playerIndex].team;

        // Determina il vincitore attuale
        let winnerIndex = 0;
        let highestOrder = -1;
        let isTrumpWinning = false;

        for (let i = 0; i < currentTrick.length; i++) {
          const card = currentTrick[i];
          const cardOrder = this.getCardOrder(card);
          const isCardTrump = card.suit === trumpSuit;

          if (isCardTrump && !isTrumpWinning) {
            winnerIndex = i;
            highestOrder = cardOrder;
            isTrumpWinning = true;
          } else if (
            isCardTrump &&
            isTrumpWinning &&
            cardOrder > highestOrder
          ) {
            winnerIndex = i;
            highestOrder = cardOrder;
          } else if (
            !isTrumpWinning &&
            card.suit === leadSuit &&
            cardOrder > highestOrder
          ) {
            winnerIndex = i;
            highestOrder = cardOrder;
          }
        }

        const actualWinnerPlayerIndex =
          ((state.leadPlayer || 0) + winnerIndex) % 4;
        const winnerTeam = state.players[actualWinnerPlayerIndex].team;
        const myTeamIsWinning = winnerTeam === myTeam;

        if (!myTeamIsWinning) {
          console.log(
            `[SAFE ACE CHECK] 🚫 ASSO ${ace.suit} NON SICURO - non vince e team non sta vincendo!`
          );
          return false;
        }

        console.log(
          `[SAFE ACE CHECK] ✅ ASSO ${ace.suit} sicuro per supporto team`
        );
      } else {
        console.log(
          `[SAFE ACE CHECK] ✅ ASSO ${ace.suit} sicuro perché può vincere`
        );
      }
    }

    // L'asso deve essere non-briscola
    if (ace.suit === trumpSuit) return false;

    // Deve poter vincere la presa corrente OPPURE il team deve vincere (già verificato sopra)
    const aceCanWin = this.canWinCurrentTrick(
      ace,
      currentTrick,
      leadSuit,
      trumpSuit
    );
    if (!aceCanWin && currentTrick.length > 0) {
      // Questo caso è già gestito dalla regola draconiana sopra
      return false;
    }

    // Calcola quanti giocatori devono ancora giocare dopo di me
    const playersAfterMe = 4 - currentTrick.length - 1;

    // Se sono l'ultimo a giocare, è sempre sicuro
    if (playersAfterMe === 0) return true;

    // Se è il primo trucco e l'asso è del seme di uscita, è generalmente sicuro
    // perché è improbabile che qualcuno abbia sia 2 che 3 dello stesso seme
    if ((state.trickNumber ?? 1) === 1 && ace.suit === leadSuit) return true;

    // Per trucchi successivi, l'asso del seme di uscita è sicuro se:
    // 1. È il seme di uscita (gli altri devono seguire)
    // 2. Non ci sono già carte più forti in tavola
    if (ace.suit === leadSuit) {
      const hasStrongerCardsInTrick = currentTrick.some(
        (card) =>
          card.suit === leadSuit && (card.rank === "2" || card.rank === "3")
      );
      return !hasStrongerCardsInTrick;
    }

    // In altri casi, è meno sicuro
    return false;
  }

  /**
   * 🎯 Identifica assi che possono essere giocati in sicurezza
   */
  getSafeAces(
    availableCards: Card[],
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    state: GameState,
    playerIndex: number
  ): Card[] {
    return availableCards.filter((card) => {
      return (
        card.rank === Rank.Ace &&
        this.canPlayAceSafely(
          card,
          currentTrick,
          leadSuit,
          trumpSuit,
          state,
          playerIndex
        )
      );
    });
  }

  /**
   * 🎯 Metodo migliorato per selezionare la carta ottimale considerando assi sicuri
   */
  selectOptimalCardWithSafetyCheck(
    availableCards: Card[],
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    state: GameState,
    playerIndex: number
  ): {
    recommendedCard: Card;
    reason: string;
    strategy: string;
  } | null {
    // 🎯 PRIORITÀ ASSOLUTA: Assi sicuri da giocare
    const safeAces = this.getSafeAces(
      availableCards,
      currentTrick,
      leadSuit,
      trumpSuit,
      state,
      playerIndex
    );

    if (safeAces.length > 0) {
      console.log(`[SAFE ACES] 🔥 Identificati ${safeAces.length} assi sicuri`);

      // Se posso vincere con un asso sicuro, è sempre una buona mossa
      const winningAces = safeAces.filter((ace) =>
        this.canWinCurrentTrick(ace, currentTrick, leadSuit, trumpSuit)
      );

      if (winningAces.length > 0) {
        return {
          recommendedCard: winningAces[0],
          reason: `🔥 ASSO SICURO: ${winningAces[0].rank} di ${winningAces[0].suit} può vincere senza rischi`,
          strategy: "safe_ace_play",
        };
      }

      // Anche se non vince, un asso sicuro può essere giocato strategicamente
      const isLastPlayer = currentTrick.length === 3;
      const trickValue = currentTrick.reduce(
        (sum, card) => sum + this.getCardValue(card),
        0
      );

      if (isLastPlayer && trickValue > 0) {
        return {
          recommendedCard: safeAces[0],
          reason: `🎯 ASSO SICURO ultimo giocatore: 1 punto garantito + ${trickValue.toFixed(
            1
          )} punti sul tavolo`,
          strategy: "safe_ace_loading",
        };
      }
    }

    return null;
  }

  /**
   * 🎯 OTTIMIZZAZIONE: Analisi vincitore più robusta
   */
  private analyzeCurrentWinner(
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    leadPlayer: number,
    state: GameState
  ): {
    winnerIndex: number;
    winnerTeam: number;
    winnerCard: Card | null;
  } {
    if (currentTrick.length === 0) {
      return { winnerIndex: -1, winnerTeam: -1, winnerCard: null };
    }

    let winnerIndex = 0;
    let winnerCard = currentTrick[0];

    // 🎯 OTTIMIZZAZIONE: Calcolo più chiaro del valore
    let highestValue = this.calculateCardTrickValue(
      winnerCard,
      leadSuit,
      trumpSuit
    );

    for (let i = 1; i < currentTrick.length; i++) {
      const card = currentTrick[i];
      const cardValue = this.calculateCardTrickValue(card, leadSuit, trumpSuit);

      if (cardValue > highestValue) {
        highestValue = cardValue;
        winnerIndex = i;
        winnerCard = card;
      }
    }

    const winnerPlayerIndex = (leadPlayer + winnerIndex) % 4;
    const winnerTeam = state.players[winnerPlayerIndex].team;

    return {
      winnerIndex,
      winnerTeam,
      winnerCard,
    };
  }

  /**
   * 🎯 NUOVO: Metodo helper per calcolare il valore di una carta nel contesto del trick
   */
  private calculateCardTrickValue(
    card: Card,
    leadSuit: Suit | null,
    trumpSuit: Suit | null
  ): number {
    if (card.suit === trumpSuit) {
      // Briscola: valore base 1000 + ordine
      return 1000 + this.getCardOrder(card);
    } else if (card.suit === leadSuit) {
      // Seme di mano: solo ordine
      return this.getCardOrder(card);
    }
    // Altri semi: valore 0
    return 0;
  }

  /**
   * 🤝 NUOVO: Analisi collaborativa avanzata del compagno
   * Determina precisamente se il compagno sta vincendo e se possiamo/dobbiamo supportarlo
   */
  analyzeTeammateCollaborativeStatus(
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    leadPlayer: number,
    playerIndex: number,
    state: GameState
  ): {
    teammateExists: boolean;
    teammateIsWinning: boolean;
    teammateCanBeBeaten: boolean;
    teammateCard: Card | null;
    shouldLoadTrick: boolean;
    shouldProtectTeammate: boolean;
    collaborativeAction: "support" | "compete" | "neutral" | "protect";
    reason: string;
  } {
    if (currentTrick.length === 0) {
      return {
        teammateExists: false,
        teammateIsWinning: false,
        teammateCanBeBeaten: false,
        teammateCard: null,
        shouldLoadTrick: false,
        shouldProtectTeammate: false,
        collaborativeAction: "neutral",
        reason: "Primo a giocare - nessun compagno da analizzare",
      };
    }

    const myTeam = state.players[playerIndex].team;
    let teammateCard: Card | null = null;
    let teammatePosition = -1;

    // Trova il compagno nel trick corrente
    for (let i = 0; i < currentTrick.length; i++) {
      const cardPlayerIndex = (leadPlayer + i) % 4;
      if (
        state.players[cardPlayerIndex].team === myTeam &&
        cardPlayerIndex !== playerIndex
      ) {
        teammateCard = currentTrick[i];
        teammatePosition = i;
        break;
      }
    }

    if (!teammateCard) {
      return {
        teammateExists: false,
        teammateIsWinning: false,
        teammateCanBeBeaten: false,
        teammateCard: null,
        shouldLoadTrick: false,
        shouldProtectTeammate: false,
        collaborativeAction: "neutral",
        reason: "Nessun compagno nel trick corrente",
      };
    }

    // Analizza se il compagno sta vincendo
    const winnerAnalysis = this.analyzeCurrentWinner(
      currentTrick,
      leadSuit,
      trumpSuit,
      leadPlayer,
      state
    );

    const teammateIsWinning =
      winnerAnalysis.winnerTeam === myTeam &&
      winnerAnalysis.winnerIndex === teammatePosition;

    // Analizza se il compagno può essere battuto dai giocatori rimanenti
    const remainingPlayers = 4 - currentTrick.length - 1;
    let teammateCanBeBeaten = false;

    if (teammateIsWinning && remainingPlayers > 0) {
      // Stima semplificata: il compagno può essere battuto se non ha una carta imbattibile
      const isTeammateCardUnbeatable = this.isCardUnbeatable(
        teammateCard,
        currentTrick,
        leadSuit,
        trumpSuit,
        state
      );
      teammateCanBeBeaten = !isTeammateCardUnbeatable;
    }

    // Determina l'azione collaborativa appropriata
    let collaborativeAction: "support" | "compete" | "neutral" | "protect";
    let reason: string;
    let shouldLoadTrick = false;
    let shouldProtectTeammate = false;

    if (teammateIsWinning) {
      const trickValue = currentTrick.reduce(
        (sum, card) => sum + this.getCardValue(card),
        0
      );
      const isLastToPlay = currentTrick.length === 3;
      const isValuableTrick = trickValue >= 1;

      if (isLastToPlay && (isValuableTrick || state.trickNumber === 10)) {
        collaborativeAction = "support";
        shouldLoadTrick = true;
        reason = `Compagno vincente - valorizzare presa (${trickValue.toFixed(
          1
        )} punti)`;
      } else if (teammateCanBeBeaten) {
        collaborativeAction = "protect";
        shouldProtectTeammate = true;
        reason = "Compagno vincente ma vulnerabile - proteggere";
      } else {
        collaborativeAction = "support";
        reason = "Compagno sicuramente vincente - supporto passivo";
      }
    } else {
      const canWinTrick = this.canWinCurrentTrick(
        state.players[playerIndex].hand[0], // Placeholder
        currentTrick,
        leadSuit,
        trumpSuit
      );

      if (canWinTrick) {
        collaborativeAction = "compete";
        reason = "Compagno non sta vincendo - posso competere per il team";
      } else {
        collaborativeAction = "neutral";
        reason = "Compagno non vincente e non posso aiutare";
      }
    }

    return {
      teammateExists: true,
      teammateIsWinning,
      teammateCanBeBeaten,
      teammateCard,
      shouldLoadTrick,
      shouldProtectTeammate,
      collaborativeAction,
      reason,
    };
  }

  /**
   * 🤝 Verifica se una carta è praticamente imbattibile nel contesto attuale
   */
  private isCardUnbeatable(
    card: Card,
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    state: GameState
  ): boolean {
    // Briscola 3 è sempre imbattibile
    if (card.suit === trumpSuit && card.rank === "3") return true;

    // Briscola 2 è imbattibile se il 3 di briscola non è più in gioco
    if (card.suit === trumpSuit && card.rank === "2") {
      // Semplificazione: assumiamo che sia imbattibile a meno che non vediamo il 3
      const trump3InTrick = currentTrick.some(
        (c) => c.suit === trumpSuit && c.rank === "3"
      );
      return !trump3InTrick;
    }

    // 3 del seme di uscita è imbattibile se non ci sono briscole nel trick
    if (card.suit === leadSuit && card.rank === "3") {
      const trumpsInTrick = currentTrick.some((c) => c.suit === trumpSuit);
      return !trumpsInTrick;
    }

    // Altre carte possono essere battute
    return false;
  }
  /**
   * 🎯 NUOVA REGOLA STRATEGICA: Valuta se usare carte forti (2,3) per prendere punti
   * - Il 3 e il 2 si conservano per cercare di prendere un Asso
   * - Se l'Asso del seme è già stato giocato, possono essere usati per prese da almeno 0.6 punti
   */
  shouldUseStrategicCardsForPoints(
    availableCards: Card[],
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    trickValue: number,
    memory: CardMemory | null, // CardMemory per verificare se Assi sono già usciti
    isLastPlayer: boolean = false
  ): {
    shouldUse: boolean;
    recommendedCard: Card | null;
    reason: string;
  } {
    const strategicWinningCards = availableCards.filter((card) => {
      return (
        this.isStrategicCard(card) &&
        this.canWinCurrentTrick(card, currentTrick, leadSuit, trumpSuit)
      );
    });

    if (strategicWinningCards.length === 0) {
      return {
        shouldUse: false,
        recommendedCard: null,
        reason: "Nessuna carta strategica può vincere",
      };
    }

    // 🔥 ASSO SUL TAVOLO = SEMPRE PRENDI
    const hasAceOnTable = currentTrick.some((card) => card.rank === "A");
    if (hasAceOnTable) {
      strategicWinningCards.sort((a, b) => {
        // Preferisci 2 prima di 3 per conservare la carta più forte
        if (a.rank === "2" && b.rank === "3") return -1;
        if (a.rank === "3" && b.rank === "2") return 1;
        return this.getCardStrengthScore(a) - this.getCardStrengthScore(b);
      });

      return {
        shouldUse: true,
        recommendedCard: strategicWinningCards[0],
        reason: `🔥 ASSO SUL TAVOLO! Vale qualsiasi carta strategica per 1 punto`,
      };
    }

    // 🎯 NUOVA LOGICA: Controlla se l'Asso del seme della carta strategica è già uscito
    let adjustedThreshold = 0.6; // Soglia base per carte strategiche

    if (isLastPlayer) {
      adjustedThreshold = 0.3; // Più permissivo per ultimo giocatore
    }

    // Verifica per ogni carta strategica se il suo Asso è già uscito
    const cardsWithAceStatus = strategicWinningCards.map((card) => {
      let aceAlreadyPlayed = false;

      if (memory && memory.playedBySuit && memory.playedBySuit[card.suit]) {
        aceAlreadyPlayed = memory.playedBySuit[card.suit].some(
          (playedCard: Card) => playedCard.rank === "A"
        );
      }

      return {
        card,
        aceAlreadyPlayed,
        effectiveThreshold: aceAlreadyPlayed ? 0.6 : Number.MAX_SAFE_INTEGER, // Se Asso uscito, soglia normale; altrimenti solo per Assi
      };
    });

    // Trova la migliore carta da usare se giustificato
    const usableCards = cardsWithAceStatus.filter(
      (cardInfo) => trickValue >= cardInfo.effectiveThreshold
    );

    if (usableCards.length > 0) {
      // Ordina per priorità: prima quelle con Asso già uscito, poi per forza carta
      usableCards.sort((a, b) => {
        // Priorità 1: Se un Asso è uscito e l'altro no
        if (a.aceAlreadyPlayed && !b.aceAlreadyPlayed) return -1;
        if (!a.aceAlreadyPlayed && b.aceAlreadyPlayed) return 1;

        // Priorità 2: Preferisci carte non-briscola
        const aIsTrump = a.card.suit === trumpSuit;
        const bIsTrump = b.card.suit === trumpSuit;
        if (!aIsTrump && bIsTrump) return -1;
        if (aIsTrump && !bIsTrump) return 1;

        // Priorità 3: Preferisci 2 prima di 3 per conservare la carta più forte
        if (a.card.rank === "2" && b.card.rank === "3") return -1;
        if (a.card.rank === "3" && b.card.rank === "2") return 1;

        return (
          this.getCardStrengthScore(a.card) - this.getCardStrengthScore(b.card)
        );
      });

      const bestCardInfo = usableCards[0];
      const reason = bestCardInfo.aceAlreadyPlayed
        ? `🎯 Asso di ${
            bestCardInfo.card.suit
          } già uscito! ${trickValue.toFixed(1)} punti ≥ 0.6 - Uso ${
            bestCardInfo.card.rank
          }`
        : `🔥 ${trickValue.toFixed(1)} punti con Asso sul tavolo - Uso ${
            bestCardInfo.card.rank
          }`;

      return {
        shouldUse: true,
        recommendedCard: bestCardInfo.card,
        reason,
      };
    }

    // Determina il motivo del rifiuto
    const hasAceNotPlayed = cardsWithAceStatus.some(
      (cardInfo) => !cardInfo.aceAlreadyPlayed
    );

    const reason = hasAceNotPlayed
      ? `💎 Conservo ${strategicWinningCards[0].rank} per prendere l'Asso di ${
          strategicWinningCards[0].suit
        } (${trickValue.toFixed(1)} < soglia Asso)`
      : `💡 ${trickValue.toFixed(
          1
        )} punti < 0.6 - Conservo carte strategiche anche se Assi usciti`;

    return {
      shouldUse: false,
      recommendedCard: null,
      reason,
    };
  }

  /**
   * 🚨 NUOVO: Valutazione aggressiva per impedire agli avversari di prendere punti
   * Determina se la CPU deve essere aggressiva nel tentare di prendere quando ci sono punti sul tavolo
   * e l'avversario sta vincendo
   */
  shouldBeAggressiveForOpponentPrevention(
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    leadPlayer: number,
    playerIndex: number,
    state: GameState
  ): {
    shouldBeAggressive: boolean;
    trickValue: number;
    opponentIsWinning: boolean;
    aggressivenessLevel: "none" | "low" | "medium" | "high" | "critical";
    reason: string;
  } {
    // Calcola il valore totale della presa
    const trickValue = currentTrick.reduce(
      (sum, card) => sum + this.getCardValue(card),
      0
    );

    // Se non ci sono punti sul tavolo, non serve essere aggressivi
    if (trickValue < 1) {
      return {
        shouldBeAggressive: false,
        trickValue,
        opponentIsWinning: false,
        aggressivenessLevel: "none",
        reason: "Nessun punto sul tavolo - non serve aggressività",
      };
    }

    // Analizza chi sta vincendo attualmente
    const winnerAnalysis = this.analyzeCurrentWinner(
      currentTrick,
      leadSuit,
      trumpSuit,
      leadPlayer,
      state
    );

    if (winnerAnalysis.winnerIndex === -1) {
      // Primo a giocare
      return {
        shouldBeAggressive: false,
        trickValue,
        opponentIsWinning: false,
        aggressivenessLevel: "none",
        reason: "Primo a giocare - nessun avversario da contrastare",
      };
    }

    const myTeam = state.players[playerIndex].team;
    const opponentIsWinning = winnerAnalysis.winnerTeam !== myTeam;

    if (!opponentIsWinning) {
      return {
        shouldBeAggressive: false,
        trickValue,
        opponentIsWinning: false,
        aggressivenessLevel: "none",
        reason: "Il nostro team sta già vincendo - non serve aggressività",
      };
    } // 🔥 LOGICA AGGRESSIVA: Avversario sta vincendo con punti!
    let aggressivenessLevel: "none" | "low" | "medium" | "high" | "critical";
    let reason: string;

    // 🚨 PRIORITÀ ASSOLUTA: ASSO SUL TAVOLO = SEMPRE CRITICAL!
    const hasAceOnTable = currentTrick.some((card) => card.rank === "A");
    if (hasAceOnTable) {
      aggressivenessLevel = "critical";
      reason = `CRITICO: Avversario sta prendendo un ASSO (1 punto) - IMPEDIRE ASSOLUTAMENTE!`;
    } else if (trickValue >= 3) {
      aggressivenessLevel = "critical";
      reason = `CRITICO: Avversario vince ${trickValue.toFixed(
        1
      )} punti - IMPEDIRE A TUTTI I COSTI!`;
    } else if (trickValue >= 2) {
      aggressivenessLevel = "high";
      reason = `ALTO: Avversario vince ${trickValue.toFixed(
        1
      )} punti - molto importante impedire`;
    } else if (trickValue >= 1.5) {
      aggressivenessLevel = "medium";
      reason = `MEDIO: Avversario vince ${trickValue.toFixed(
        1
      )} punti - importante impedire`;
    } else if (trickValue >= 1) {
      aggressivenessLevel = "medium"; // AUMENTATO da "low" a "medium" per 1 punto
      reason = `MEDIO: Avversario vince ${trickValue.toFixed(
        1
      )} punti - importante impedire`;
    } else {
      aggressivenessLevel = "none";
      reason = `Troppo pochi punti per giustificare aggressività`;
    }

    return {
      shouldBeAggressive: aggressivenessLevel !== "none",
      trickValue,
      opponentIsWinning: true,
      aggressivenessLevel,
      reason,
    };
  }

  /**
   * 🎯 NUOVO: Trova la migliore carta per impedire agli avversari di prendere punti
   * Priorità: 1) Stessa carta più forte, 2) Briscola più debole possibile
   */
  findBestCardToPreventOpponent(
    availableCards: Card[],
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    aggressivenessLevel: "none" | "low" | "medium" | "high" | "critical"
  ): {
    recommendedCard: Card | null;
    strategy: "same_suit" | "trump_cut" | "cannot_win";
    reason: string;
  } {
    // 1. PRIORITÀ ASSOLUTA: Carte dello stesso seme che possono vincere
    if (leadSuit) {
      const leadSuitCards = availableCards.filter(
        (card) => card.suit === leadSuit
      );

      if (leadSuitCards.length > 0) {
        const winningLeadCards = leadSuitCards.filter((card) =>
          this.canWinCurrentTrick(card, currentTrick, leadSuit, trumpSuit)
        );

        if (winningLeadCards.length > 0) {
          // Trova la carta più debole che può ancora vincere
          winningLeadCards.sort(
            (a, b) => this.getCardOrder(a) - this.getCardOrder(b)
          );
          const selectedCard = winningLeadCards[0];

          return {
            recommendedCard: selectedCard,
            strategy: "same_suit",
            reason: `Uso ${selectedCard.rank} del seme per vincere con minimo spreco`,
          };
        }

        // Non può vincere con carte del seme
        return {
          recommendedCard: null,
          strategy: "cannot_win",
          reason: "Nessuna carta del seme può vincere",
        };
      }
    }

    // 2. SECONDA PRIORITÀ: Taglio con briscola (solo se giustificato dal livello di aggressività)
    const trumpCards = availableCards.filter((card) => card.suit === trumpSuit);

    if (trumpCards.length > 0) {
      // Verifica se il taglio è giustificato in base al livello di aggressività
      const trumpJustified =
        aggressivenessLevel === "critical" || // Sempre per livello critico
        (aggressivenessLevel === "high" &&
          trumpCards.some((card) => this.getCardValue(card) === 0)) || // Alto livello + briscole senza valore
        (aggressivenessLevel === "medium" &&
          trumpCards.some(
            (card) =>
              this.getCardValue(card) === 0 && this.getCardOrder(card) <= 6
          )); // Medio + briscole deboli

      if (trumpJustified) {
        // 🔥 CORREZIONE CRITICA: Verifica se il seme di uscita è briscola
        const isLeadSuitTrump = leadSuit === trumpSuit;

        const winningTrumps = trumpCards.filter((card) =>
          this.canWinCurrentTrick(card, currentTrick, leadSuit, trumpSuit)
        );

        // Se il seme di uscita NON è briscola e non abbiamo carte vincenti, non giocare briscole inutilmente
        if (!isLeadSuitTrump && winningTrumps.length === 0) {
          console.log(
            `[AI] ⚠️ Evito briscole non vincenti quando non seguo briscola`
          );
          return { reason: "Evito briscole non vincenti" };
        }

        if (winningTrumps.length > 0) {
          // Trova la briscola più debole che può vincere
          // Priorità: 1) Briscole senza valore, 2) Briscole più deboli
          winningTrumps.sort((a, b) => {
            const valueA = this.getCardValue(a);
            const valueB = this.getCardValue(b);

            // Prima ordina per valore (0 prima di tutto)
            if (valueA !== valueB) {
              return valueA - valueB;
            }

            // Poi per ordine di forza (più debole prima)
            return this.getCardOrder(a) - this.getCardOrder(b);
          });

          const selectedCard = winningTrumps[0];
          const cardValue = this.getCardValue(selectedCard);

          // Evita 3 e 2 di briscola a meno che non sia davvero critico
          if (
            (selectedCard.rank === "3" || selectedCard.rank === "2") &&
            aggressivenessLevel !== "critical"
          ) {
            // Cerca alternative meno preziose
            const nonStrategicTrumps = winningTrumps.filter(
              (card) => card.rank !== "3" && card.rank !== "2"
            );

            if (nonStrategicTrumps.length > 0) {
              const alternativeCard = nonStrategicTrumps[0];
              return {
                recommendedCard: alternativeCard,
                strategy: "trump_cut",
                reason: `Taglio con ${alternativeCard.rank} di briscola (evito carte strategiche)`,
              };
            }
          }

          return {
            recommendedCard: selectedCard,
            strategy: "trump_cut",
            reason: `Taglio con ${selectedCard.rank} di briscola (valore: ${cardValue}, livello: ${aggressivenessLevel})`,
          };
        }
      }
    } // 3. Non può vincere    // 3. Non può vincere
    return {
      recommendedCard: null,
      strategy: "cannot_win",
      reason: "Impossibile vincere la presa con le carte disponibili",
    };
  }
  /**
   * 🎯 NUOVO: Gestione intelligente supporto compagno
   * Implementa la logica: se compagno sta vincendo con carta alta non superabile -> dai punti
   * se carta facilmente superabile -> sii strategico
   */
  getTeammateSupport(
    availableCards: Card[],
    currentTrick: Card[],
    leadSuit: Suit | null,
    trumpSuit: Suit | null,
    state: GameState,
    playerIndex: number
  ): {
    shouldSupport: boolean;
    recommendedCard: Card | null;
    reason: string;
    supportType: "GIVE_POINTS" | "BE_STRATEGIC" | "NO_SUPPORT";
  } {
    if (currentTrick.length === 0) {
      return {
        shouldSupport: false,
        recommendedCard: null,
        reason: "Primo giocatore - nessun compagno da supportare",
        supportType: "NO_SUPPORT",
      };
    }

    // Analizza chi sta vincendo
    const winnerAnalysis = this.analyzeCurrentWinner(
      currentTrick,
      leadSuit,
      trumpSuit,
      0, // leadPlayer
      state
    );

    const myTeam = state.players[playerIndex].team;
    const teammateIsWinning =
      winnerAnalysis.winnerTeam === myTeam &&
      winnerAnalysis.winnerIndex !== playerIndex;

    if (!teammateIsWinning) {
      return {
        shouldSupport: false,
        recommendedCard: null,
        reason: "Compagno non sta vincendo",
        supportType: "NO_SUPPORT",
      };
    }

    // Trova la carta vincente del compagno
    const winningCard = currentTrick[winnerAnalysis.winnerIndex];

    // Verifica se possiedo il seme di uscita
    const hasLeadSuit = leadSuit
      ? availableCards.some((card) => card.suit === leadSuit)
      : false;
    if (hasLeadSuit) {
      // Ho il seme di uscita - devo rispondere al seme
      const leadSuitCards = availableCards.filter(
        (card) => card.suit === leadSuit
      );

      // Verifica se la carta del compagno è "alta e non superabile" (logica inline per evitare errori runtime)
      const highRanks = ["3", "2", "A", "K", "H"];
      const isHighUnsuperableCard =
        highRanks.includes(winningCard.rank) ||
        (winningCard.suit === trumpSuit && winningCard.rank === "J");

      // 🚨 REGOLA CRITICA: NON SUPERARE MAI IL COMPAGNO che sta vincendo!
      // Prima di tutto, filtra le carte che potrebbero superare il compagno
      const safeLeadSuitCards = leadSuitCards.filter((card) => {
        // Non usare carte che potrebbero vincere sopra al compagno
        const wouldWinOverTeammate = this.canWinCurrentTrick(
          card,
          currentTrick,
          leadSuit,
          trumpSuit
        );
        return !wouldWinOverTeammate;
      });

      // 🤝 LOGICA CORRETTA: Se il compagno sta vincendo, dagli SEMPRE punti se li hai
      const pointCards = safeLeadSuitCards.filter(
        (card) => this.getCardValue(card) > 0
      );

      if (pointCards.length > 0) {
        // Ordina per dare punti: A prima di tutto, poi K,H,J, poi 2,3
        pointCards.sort((a, b) => {
          const orderToGive: Record<string, number> = {
            A: 1, // Asso - primo da dare (più prezioso)
            K: 2, // Re
            H: 3, // Cavallo
            J: 4, // Fante
            "2": 5, // Due
            "3": 6, // Tre - ultimo da dare
          };
          return (orderToGive[a.rank] || 999) - (orderToGive[b.rank] || 999);
        });

        const cardValue = this.getCardValue(pointCards[0]);
        const cardDescription = isHighUnsuperableCard
          ? "imbattibile"
          : "vincente";

        return {
          shouldSupport: true,
          recommendedCard: pointCards[0],
          reason: `Compagno ha ${winningCard.rank} (${cardDescription}) - REGALO ${pointCards[0].rank} (${cardValue} punti) SENZA superarlo`,
          supportType: "GIVE_POINTS",
        };
      }

      // Solo se NON ho punti da dare SENZA superare, allora scarto carte senza valore
      // Usa ORDINE DI SCARTO: preferiamo dare via le carte meno dolorose
      const noPointCards = safeLeadSuitCards.filter(
        (card) => this.getCardValue(card) === 0
      );

      if (noPointCards.length > 0) {
        noPointCards.sort(
          (a, b) => this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b)
        );

        return {
          shouldSupport: true,
          recommendedCard: noPointCards[0],
          reason: `Compagno sta vincendo - SCARTO carta senza punti: ${noPointCards[0].rank}`,
          supportType: "BE_STRATEGIC",
        };
      } // Solo carte con punti rimaste - usa ordine scarto: J,H,K,2,3,A (meno doloroso → più doloroso)
      const remainingPointCards = leadSuitCards.filter(
        (card) => this.getCardValue(card) > 0
      );
      remainingPointCards.sort(
        (a, b) => this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b)
      );

      return {
        shouldSupport: true,
        recommendedCard: remainingPointCards[0],
        reason: `Compagno sta vincendo - SCARTO carta meno dolorosa: ${remainingPointCards[0].rank}`,
        supportType: "BE_STRATEGIC",
      };
    } else {
      // NON ho il seme di uscita - posso giocare qualsiasi carta

      // 🚨 REGOLA CRITICA: Se il compagno sta vincendo con carta NON-briscola, MAI tagliare con briscole!
      const winningCardIsTrump = winningCard.suit === trumpSuit;

      if (!winningCardIsTrump) {
        // Compagno vince con carta non-briscola -> NON tagliare con briscole
        console.log(
          `[TEAMMATE SUPPORT] 🚫 Compagno vince con ${winningCard.rank} NON-briscola - NO taglio!`
        );

        const nonTrumpCards = availableCards.filter(
          (card) => card.suit !== trumpSuit
        );

        if (nonTrumpCards.length > 0) {
          // Ho carte non-briscola da usare
          const pointCards = nonTrumpCards.filter(
            (card) => this.getCardValue(card) > 0
          );

          if (pointCards.length > 0) {
            // Ordina per dare punti: A prima di tutto, poi K,H,J, poi 2,3
            pointCards.sort((a, b) => {
              const orderToGive: Record<string, number> = {
                A: 1, // Asso - primo da dare (più prezioso)
                K: 2, // Re
                H: 3, // Cavallo
                J: 4, // Fante
                "2": 5, // Due
                "3": 6, // Tre - ultimo da dare
              };
              return (
                (orderToGive[a.rank] || 999) - (orderToGive[b.rank] || 999)
              );
            });

            return {
              shouldSupport: true,
              recommendedCard: pointCards[0],
              reason: `Compagno ha ${winningCard.rank} NON-briscola - REGALO ${
                pointCards[0].rank
              } (${this.getCardValue(pointCards[0])} punti) SENZA tagliare`,
              supportType: "GIVE_POINTS",
            };
          }

          // Solo se NON ho punti da dare, allora scarto carte senza valore
          const noPointCards = nonTrumpCards.filter(
            (card) => this.getCardValue(card) === 0
          );

          if (noPointCards.length > 0) {
            noPointCards.sort(
              (a, b) =>
                this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b)
            );

            return {
              shouldSupport: true,
              recommendedCard: noPointCards[0],
              reason: `Compagno vince con NON-briscola - NON taglio, uso carta senza punti: ${noPointCards[0].rank}`,
              supportType: "BE_STRATEGIC",
            };
          }

          // Solo carte con punti non-briscola rimaste
          const remainingNonTrumpPointCards = nonTrumpCards.filter(
            (card) => this.getCardValue(card) > 0
          );
          remainingNonTrumpPointCards.sort(
            (a, b) =>
              this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b)
          );

          return {
            shouldSupport: true,
            recommendedCard: remainingNonTrumpPointCards[0],
            reason: `Compagno vince con NON-briscola - sacrifico ${remainingNonTrumpPointCards[0].rank} SENZA tagliare`,
            supportType: "BE_STRATEGIC",
          };
        } else {
          // Solo briscole rimaste - caso estremo
          console.log(
            `[TEAMMATE SUPPORT] ⚠️ Solo briscole disponibili ma compagno vince con NON-briscola!`
          );

          const trumpCards = availableCards.filter(
            (card) => card.suit === trumpSuit
          );

          // Trova la briscola PIÙ DEBOLE possibile
          trumpCards.sort((a, b) => {
            // Prima: carte senza punti
            const aHasPoints = this.getCardValue(a) > 0;
            const bHasPoints = this.getCardValue(b) > 0;

            if (!aHasPoints && bHasPoints) return -1;
            if (aHasPoints && !bHasPoints) return 1;

            // Poi: ordine di forza (più debole prima)
            return this.getCardOrder(a) - this.getCardOrder(b);
          });

          return {
            shouldSupport: true,
            recommendedCard: trumpCards[0],
            reason: `⚠️ SOLO briscole disponibili - uso la PIÙ DEBOLE: ${trumpCards[0].rank} (compagno ha ${winningCard.rank} NON-briscola)`,
            supportType: "BE_STRATEGIC",
          };
        }
      }

      // REGOLA: Non sprecare briscole se compagno sta già vincendo
      const nonTrumpCards = availableCards.filter(
        (card) => card.suit !== trumpSuit
      );
      if (nonTrumpCards.length > 0) {
        // Verifica se la carta del compagno è "alta e non superabile" (logica inline)
        const highRanks = ["3", "2", "A", "K", "H"];
        const isHighUnsuperableCard =
          highRanks.includes(winningCard.rank) ||
          (winningCard.suit === trumpSuit && winningCard.rank === "J");

        // 🤝 LOGICA CORRETTA: Se compagno sta vincendo, dagli SEMPRE punti se li hai (non-briscola)
        const pointCards = nonTrumpCards.filter(
          (card) => this.getCardValue(card) > 0
        );

        if (pointCards.length > 0) {
          // Ordina per dare punti: A prima di tutto, poi K,H,J, poi 2,3
          pointCards.sort((a, b) => {
            const orderToGive: Record<string, number> = {
              A: 1, // Asso - primo da dare (più prezioso)
              K: 2, // Re
              H: 3, // Cavallo
              J: 4, // Fante
              "2": 5, // Due
              "3": 6, // Tre - ultimo da dare
            };
            return (orderToGive[a.rank] || 999) - (orderToGive[b.rank] || 999);
          });

          const cardDescription = isHighUnsuperableCard
            ? "imbattibile"
            : "vincente";

          return {
            shouldSupport: true,
            recommendedCard: pointCards[0],
            reason: `Compagno ha ${
              winningCard.rank
            } (${cardDescription}) - REGALO ${
              pointCards[0].rank
            } non-briscola (${this.getCardValue(
              pointCards[0]
            )} punti) SENZA tagliare`,
            supportType: "GIVE_POINTS",
          };
        }

        // Solo se NON ho punti da dare, allora scarto carte senza valore
        const noPointCards = nonTrumpCards.filter(
          (card) => this.getCardValue(card) === 0
        );

        if (noPointCards.length > 0) {
          noPointCards.sort(
            (a, b) =>
              this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b)
          );

          return {
            shouldSupport: true,
            recommendedCard: noPointCards[0],
            reason: `Compagno sta vincendo - NON taglio, uso carta senza punti: ${noPointCards[0].rank}`,
            supportType: "BE_STRATEGIC",
          };
        } // Solo carte con punti non-briscola rimaste
        const remainingNonTrumpPointCards = nonTrumpCards.filter(
          (card) => this.getCardValue(card) > 0
        );
        remainingNonTrumpPointCards.sort(
          (a, b) => this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b)
        );

        return {
          shouldSupport: true,
          recommendedCard: remainingNonTrumpPointCards[0],
          reason: `Compagno sta vincendo - non spreco briscole, sacrifico ${remainingNonTrumpPointCards[0].rank}`,
          supportType: "BE_STRATEGIC",
        };
      } else {
        // Solo briscole rimaste - MA il compagno sta già vincendo!
        // 🚨 REGOLA CRITICA: NON superare il compagno neanche con briscole!

        const trumpCards = availableCards.filter(
          (card) => card.suit === trumpSuit
        );

        // Filtra le briscole che NON supererebbero il compagno
        const safeTrumpCards = trumpCards.filter((card) => {
          const wouldWinOverTeammate = this.canWinCurrentTrick(
            card,
            currentTrick,
            leadSuit,
            trumpSuit
          );
          return !wouldWinOverTeammate;
        });

        if (safeTrumpCards.length > 0) {
          // Usa briscole sicure che non superano il compagno
          safeTrumpCards.sort((a, b) => {
            // Prima ordine: carte senza punti prima di quelle con punti
            const aHasPoints = this.getCardValue(a) > 0;
            const bHasPoints = this.getCardValue(b) > 0;

            if (!aHasPoints && bHasPoints) return -1; // a senza punti vince
            if (aHasPoints && !bHasPoints) return 1; // b senza punti vince

            // Se entrambe hanno/non hanno punti, usa ordine di scarto (più debole prima)
            return this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b);
          });

          return {
            shouldSupport: true,
            recommendedCard: safeTrumpCards[0],
            reason: `Compagno già vincente - uso briscola DEBOLE SENZA superarlo: ${safeTrumpCards[0].rank}`,
            supportType: "BE_STRATEGIC",
          };
        } else {
          // Tutte le briscole supererebbero il compagno - usa la più debole senza punti possibile
          trumpCards.sort((a, b) => {
            // Prima: carte senza punti
            const aHasPoints = this.getCardValue(a) > 0;
            const bHasPoints = this.getCardValue(b) > 0;

            if (!aHasPoints && bHasPoints) return -1;
            if (aHasPoints && !bHasPoints) return 1;

            // Poi: ordine di scarto (più debole prima)
            return this.getDiscardOrderScore(a) - this.getDiscardOrderScore(b);
          });

          return {
            shouldSupport: true,
            recommendedCard: trumpCards[0],
            reason: `⚠️ SOLO briscole che superano compagno - uso la PIÙ DEBOLE: ${trumpCards[0].rank} (male minore)`,
            supportType: "BE_STRATEGIC",
          };
        }
      }
    }
  }

  /**
   * 🎯 HELPER: Verifica se una carta è "alta e non facilmente superabile"
   * ORDINE CORRETTO DI FORZA: 3,2,A,K,H,J,7,6,5,4 (dal più forte al più scarso)
   * Carte difficili da superare: 3, 2, A, K, H
   */
  private isHighUnsuperableCard(card: Card, trumpSuit: Suit | null): boolean {
    // Carte considerate "alte e difficili da superare"
    // 3 = quasi imbattibile, 2 = molto forte, A = forte, K,H = abbastanza forti
    const highRanks = ["3", "2", "A", "K", "H"];

    if (highRanks.includes(card.rank)) {
      return true;
    }

    // Se è briscola, anche J può essere difficile da superare
    if (card.suit === trumpSuit && card.rank === "J") {
      return true;
    }

    return false;
  }

  /**
   * 🎯 REGOLA 1: NON buttare asso se non prende e ha alternative che non regalano punti
   */
  shouldNotWasteAce(
    aceCard: Card,
    cards: Card[],
    currentTrick: Card[],
    state: GameState,
    playerIndex: number
  ): { shouldAvoid: boolean; reason: string; alternative?: Card } {
    if (aceCard.rank !== "A") {
      return { shouldAvoid: false, reason: "Non è un asso" };
    }

    // Se l'asso può prendere, ok usarlo
    if (
      this.canWinCurrentTrick(
        aceCard,
        currentTrick,
        state.leadSuit,
        state.trumpSuit
      )
    ) {
      return { shouldAvoid: false, reason: "L'asso può prendere" };
    }

    // Se il team prende, ok dare l'asso
    const teammatePosition = this.analyzeTeammatePosition(state, playerIndex);
    if (teammatePosition.teammateIsWinning) {
      return { shouldAvoid: false, reason: "Il team sta prendendo" };
    }

    // Verifica se ha alternative che non regalano punti
    const alternatives = cards.filter(
      (card) => card !== aceCard && this.getCardValue(card) === 0 // Carte che non valgono punti
    );

    if (alternatives.length > 0) {
      return {
        shouldAvoid: true,
        reason: "Asso sprecato: non prende e regala punti agli avversari",
        alternative: alternatives[0],
      };
    }

    return { shouldAvoid: false, reason: "Nessuna alternativa disponibile" };
  }

  /**
   * 🎯 REGOLA 2: Gestione quando non hai il seme di uscita
   */
  handleNoLeadSuit(
    cards: Card[],
    currentTrick: Card[],
    state: GameState,
    playerIndex: number
  ): { recommendedCard?: Card; reason: string } {
    const hasLeadSuit = cards.some((card) => card.suit === state.leadSuit);
    if (hasLeadSuit) {
      return { reason: "Ha il seme di uscita" };
    }

    const teammatePosition = this.analyzeTeammatePosition(state, playerIndex);
    const trickValue = currentTrick.reduce(
      (sum, card) => sum + this.getCardValue(card),
      0
    );

    if (teammatePosition.teammateIsWinning) {
      // Compagno prende: dai punti in ordine Asso > Re > Cavallo > Fante
      const valueCards = cards.filter((card) =>
        ["A", "K", "Q", "J"].includes(card.rank)
      );
      if (valueCards.length > 0) {
        valueCards.sort((a, b) => {
          const order = { A: 4, K: 3, Q: 2, J: 1 };
          return (
            (order[b.rank as keyof typeof order] || 0) -
            (order[a.rank as keyof typeof order] || 0)
          );
        });
        return {
          recommendedCard: valueCards[0],
          reason: `Compagno prende: do ${valueCards[0].rank} di ${valueCards[0].suit}`,
        };
      }
    } else {
      // Compagno NON prende
      if (trickValue >= 1) {
        // Ci sono punti: prova con briscole SOLO se non stiamo seguendo il seme di briscola
        const trumps = cards.filter((card) => card.suit === state.trumpSuit);

        // 🔥 CORREZIONE CRITICA: Non giocare briscole se il seme di uscita è già briscola
        // e la nostra briscola non può vincere
        const isLeadSuitTrump = state.leadSuit === state.trumpSuit;

        if (!isLeadSuitTrump) {
          // Solo se NON stiamo seguendo briscole, considera di tagliare
          const winningTrumps = trumps.filter((card) =>
            this.canWinCurrentTrick(
              card,
              currentTrick,
              state.leadSuit,
              state.trumpSuit
            )
          );

          if (winningTrumps.length > 0) {
            // Usa la briscola più bassa che può vincere
            winningTrumps.sort(
              (a, b) => this.getCardOrder(a) - this.getCardOrder(b)
            );
            return {
              recommendedCard: winningTrumps[0],
              reason: `Taglio per ${trickValue} punti con ${winningTrumps[0].rank} di ${winningTrumps[0].suit}`,
            };
          }
        } else {
          // Se stiamo seguendo briscole, gioca solo se puoi vincere
          const winningTrumps = trumps.filter((card) =>
            this.canWinCurrentTrick(
              card,
              currentTrick,
              state.leadSuit,
              state.trumpSuit
            )
          );

          if (winningTrumps.length > 0) {
            // Usa la briscola più bassa che può vincere
            winningTrumps.sort(
              (a, b) => this.getCardOrder(a) - this.getCardOrder(b)
            );
            return {
              recommendedCard: winningTrumps[0],
              reason: `Seguo briscola vincente per ${trickValue} punti con ${winningTrumps[0].rank}`,
            };
          } else {
            // Se non posso vincere con briscole, non giocare briscole inutilmente
            console.log(
              `[AI] ⚠️ Non posso vincere con briscole, evito di sprecarle`
            );
          }
        }
      }

      // Non conviene tagliare: scarta 4-7 (NON 2 e 3!)
      const discardCards = cards.filter((card) =>
        ["4", "5", "6", "7"].includes(card.rank)
      );
      if (discardCards.length > 0) {
        return {
          recommendedCard: discardCards[0],
          reason: `Scarto ${discardCards[0].rank} (non spreco 2 e 3)`,
        };
      }
    }

    return { reason: "Nessuna strategia applicabile" };
  }

  /**
   * 🎯 REGOLA 3: Ultimo giocatore prende sempre con Asso, Re, Cavallo, Fante
   */
  handleLastPlayer(
    cards: Card[],
    currentTrick: Card[],
    state: GameState,
    playerIndex: number
  ): { recommendedCard?: Card; reason: string } {
    if (!currentTrick || currentTrick.length !== 3) {
      return { reason: "Non è l'ultimo giocatore" };
    }

    const winningCards = cards.filter((card) =>
      this.canWinCurrentTrick(
        card,
        currentTrick,
        state.leadSuit,
        state.trumpSuit
      )
    );

    if (winningCards.length > 0) {
      // Filtra solo Asso, Re, Cavallo, Fante
      const valueWinningCards = winningCards.filter((card) =>
        ["A", "K", "Q", "J"].includes(card.rank)
      );

      if (valueWinningCards.length > 0) {
        // Ordina: Asso > Re > Cavallo > Fante
        valueWinningCards.sort((a, b) => {
          const order = { A: 4, K: 3, Q: 2, J: 1 };
          return (
            (order[b.rank as keyof typeof order] || 0) -
            (order[a.rank as keyof typeof order] || 0)
          );
        });

        return {
          recommendedCard: valueWinningCards[0],
          reason: `Ultimo giocatore: prendo con ${valueWinningCards[0].rank} di ${valueWinningCards[0].suit}`,
        };
      }
    }

    return { reason: "Non posso prendere con carte di valore" };
  }

  /**
   * 🎯 REGOLA 4: Gestione asso basata su chi prende + prendi sempre con carte non-briscola di valore
   */
  handleAceByTeamStatus(
    cards: Card[],
    currentTrick: Card[],
    state: GameState,
    playerIndex: number
  ): { recommendedCard?: Card; filteredCards?: Card[]; reason: string } {
    if (!currentTrick || currentTrick.length === 0) {
      return { reason: "Nessuna presa in corso" };
    }

    // Prima verifica: prendi sempre con carte di valore non-briscola
    const nonTrumpValueCards = cards.filter(
      (card) =>
        ["A", "K", "Q", "J"].includes(card.rank) &&
        card.suit !== state.trumpSuit
    );

    const winningNonTrumpValueCards = nonTrumpValueCards.filter((card) =>
      this.canWinCurrentTrick(
        card,
        currentTrick,
        state.leadSuit,
        state.trumpSuit
      )
    );

    if (winningNonTrumpValueCards.length > 0) {
      winningNonTrumpValueCards.sort((a, b) => {
        const order = { A: 4, K: 3, Q: 2, J: 1 };
        return (
          (order[b.rank as keyof typeof order] || 0) -
          (order[a.rank as keyof typeof order] || 0)
        );
      });

      return {
        recommendedCard: winningNonTrumpValueCards[0],
        reason: `Prendo sempre con carte di valore non-briscola: ${winningNonTrumpValueCards[0].rank} di ${winningNonTrumpValueCards[0].suit}`,
      };
    }

    // Gestione asso basata su chi prende
    const teammatePosition = this.analyzeTeammatePosition(state, playerIndex);
    const aces = cards.filter((card) => card.rank === "A");

    if (teammatePosition.teammateIsWinning) {
      // Il team prende: butta SEMPRE l'asso
      if (aces.length > 0) {
        return {
          recommendedCard: aces[0],
          reason: `Team prende: butto asso ${aces[0].rank} di ${aces[0].suit}`,
        };
      }
    } else {
      // Il team NON prende: NON buttare asso se hai alternative
      const hasAlternatives = cards.some((card) => card.rank !== "A");
      if (aces.length > 0 && hasAlternatives) {
        const filteredCards = cards.filter((card) => card.rank !== "A");
        return {
          filteredCards: filteredCards,
          reason: "Team NON prende: conservo asso, uso alternative",
        };
      }
    }

    return { reason: "Gestione asso non applicabile" };
  }

  /**
   * Helper: Analizza posizione del compagno
   */
  private analyzeTeammatePosition(
    state: GameState,
    playerIndex: number
  ): { teammateIsWinning: boolean } {
    if (!state.currentTrick || state.currentTrick.length === 0) {
      return { teammateIsWinning: false };
    }

    // Trova chi sta vincendo la presa corrente
    let winnerIndex = 0;
    let highestTrumpOrder = -1;
    let highestLeadOrder = -1;
    let trumpPlayed = false;

    for (let i = 0; i < state.currentTrick.length; i++) {
      const card = state.currentTrick[i];
      if (card.suit === state.trumpSuit) {
        trumpPlayed = true;
        const order = this.getCardOrder(card);
        if (order > highestTrumpOrder) {
          highestTrumpOrder = order;
          winnerIndex = i;
        }
      } else if (card.suit === state.leadSuit && !trumpPlayed) {
        const order = this.getCardOrder(card);
        if (order > highestLeadOrder) {
          highestLeadOrder = order;
          winnerIndex = i;
        }
      }
    }

    const winnerPlayerIndex = ((state.leadPlayer ?? 0) + winnerIndex) % 4;
    const winnerTeam = state.players[winnerPlayerIndex].team;
    const myTeam = state.players[playerIndex].team;

    return {
      teammateIsWinning:
        winnerTeam === myTeam && winnerPlayerIndex !== playerIndex,
    };
  }

  /**
   * 🎯 METODO PRINCIPALE: Applica tutte le 4 regole nell'ordine giusto
   */
  applyGameRules(
    cards: Card[],
    state: GameState,
    playerIndex: number
  ): { recommendedCard?: Card; filteredCards?: Card[]; reason: string } {
    const currentTrick = state.currentTrick || [];

    // REGOLA 3: Ultimo giocatore (priorità massima)
    const lastPlayerResult = this.handleLastPlayer(
      cards,
      currentTrick,
      state,
      playerIndex
    );
    if (lastPlayerResult.recommendedCard) {
      return lastPlayerResult;
    }

    // REGOLA 4: Gestione asso e carte di valore non-briscola
    const aceResult = this.handleAceByTeamStatus(
      cards,
      currentTrick,
      state,
      playerIndex
    );
    if (aceResult.recommendedCard) {
      return aceResult;
    }
    if (aceResult.filteredCards) {
      cards = aceResult.filteredCards; // Filtra gli assi se necessario
    }

    // REGOLA 2: Quando non hai il seme di uscita
    const noLeadSuitResult = this.handleNoLeadSuit(
      cards,
      currentTrick,
      state,
      playerIndex
    );
    if (noLeadSuitResult.recommendedCard) {
      return noLeadSuitResult;
    }

    // REGOLA 1: Non sprecare assi (controllo finale)
    const aces = cards.filter((card) => card.rank === "A");
    for (const ace of aces) {
      const aceWasteResult = this.shouldNotWasteAce(
        ace,
        cards,
        currentTrick,
        state,
        playerIndex
      );
      if (aceWasteResult.shouldAvoid && aceWasteResult.alternative) {
        return {
          recommendedCard: aceWasteResult.alternative,
          reason: aceWasteResult.reason,
        };
      }
    }

    return { reason: "Nessuna regola applicabile, usa logica normale" };
  }
}
