import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { X, Plus, Search, Users, Trophy, Clock, Crown } from "lucide-react";
import ActionButton from "@/components/ui/ActionButton";
import { useAuth } from "@/context/auth-context";
import { useToast } from "@/hooks/use-toast";
import { useAudio } from "@/hooks/useAudio";
import {
  createPrivateGameSession,
  joinPrivateGameSession,
  getAvailablePrivateGames,
} from "@/services/onlineGameService";
import { PrivateGameSettings, GameSession } from "@/types/game";
import {
  VICTORY_POINTS_OPTIONS,
  MULTIPLAYER_CONFIG,
} from "@/config/multiplayer";

interface MultiplayerModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const MultiplayerModal: React.FC<MultiplayerModalProps> = ({
  isOpen,
  onClose,
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const { playSound } = useAudio();

  // States
  const [activeTab, setActiveTab] = useState<"join" | "create">("join");
  const [roomName, setRoomName] = useState("");
  const [victoryPoints, setVictoryPoints] = useState<number>(31);
  const [isLoading, setIsLoading] = useState(false);
  const [availableGames, setAvailableGames] = useState<GameSession[]>([]);
  const [loadingGames, setLoadingGames] = useState(false);

  // Load available games when modal opens
  useEffect(() => {
    if (isOpen && activeTab === "join") {
      loadAvailableGames();
    }
  }, [isOpen, activeTab]);

  const loadAvailableGames = async () => {
    setLoadingGames(true);
    try {
      const games = await getAvailablePrivateGames();
      setAvailableGames(games);
    } catch (error) {
      console.error("Error loading available games:", error);
      setAvailableGames([]);
    } finally {
      setLoadingGames(false);
    }
  };

  const handleCreateGame = async () => {
    console.log("🎮 Attempting to create game...", { user, roomName });

    if (!user) {
      toast({
        title: "Accesso richiesto",
        description: "Devi essere loggato per creare partite",
        variant: "destructive",
      });
      return;
    }

    // Simple validation instead of using validateRoomName
    if (!roomName.trim()) {
      toast({
        title: "Nome richiesto",
        description: "Inserisci un nome per la stanza",
        variant: "destructive",
      });
      return;
    }

    if (roomName.trim().length < 3) {
      toast({
        title: "Nome troppo corto",
        description: "Il nome deve avere almeno 3 caratteri",
        variant: "destructive",
      });
      return;
    }

    if (roomName.trim().length > 20) {
      toast({
        title: "Nome troppo lungo",
        description: "Il nome non può superare i 20 caratteri",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const settings: PrivateGameSettings = {
        roomName: roomName.trim(),
        victoryPoints,
        maxPlayers: 4,
      };

      const session = await createPrivateGameSession(
        user.id,
        user.email?.split("@")[0] || "Giocatore",
        settings
      );

      playSound("buttonClick");

      // Navigate to lobby
      navigate("/lobby", {
        state: {
          sessionId: session.id,
          isHost: true,
          roomName: roomName.trim(),
        },
      });

      onClose();
    } catch (error) {
      console.error("Error creating private game:", error);
      toast({
        title: "Errore",
        description:
          error instanceof Error
            ? error.message
            : "Errore durante la creazione della stanza",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleJoinGame = async (gameRoomName?: string) => {
    if (!user) {
      toast({
        title: "Accesso richiesto",
        description: "Devi essere loggato per unirti a partite",
        variant: "destructive",
      });
      return;
    }

    const targetRoomName = gameRoomName || roomName.trim();
    if (!targetRoomName) {
      toast({
        title: "Nome richiesto",
        description: "Inserisci il nome della stanza",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const session = await joinPrivateGameSession(
        targetRoomName,
        user.id,
        user.email?.split("@")[0] || "Giocatore"
      );

      playSound("buttonClick");

      toast({
        title: "Entrato nella stanza!",
        description: `Ti sei unito alla stanza "${targetRoomName}"`,
      });

      // Navigate to lobby
      navigate("/lobby", {
        state: {
          sessionId: session.id,
          isHost: false,
          roomName: targetRoomName,
        },
      });

      onClose();
    } catch (error) {
      console.error("Error joining private game:", error);
      let errorMessage = "Errore durante l'accesso alla stanza";

      if (error instanceof Error) {
        if (error.message.includes("non trovata")) {
          errorMessage = "Stanza non trovata o scaduta";
        } else if (error.message.includes("piena")) {
          errorMessage = "La stanza è piena (massimo 4 giocatori)";
        } else if (error.message.includes("già in questa stanza")) {
          errorMessage = "Sei già in questa stanza";
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        title: "Impossibile entrare",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    playSound("buttonClick");
    onClose();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !isLoading && roomName.trim()) {
      if (activeTab === "join") {
        handleJoinGame();
      } else {
        handleCreateGame();
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl p-4 w-full max-w-lg shadow-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2
            className="text-2xl font-bold text-romagna-darkWood"
            style={{ fontFamily: "'DynaPuff', cursive" }}
          >
            Multiplayer
          </h2>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex mb-6 bg-gray-100 rounded-xl p-1">
          <button
            onClick={() => setActiveTab("join")}
            className={`flex-1 py-2 px-4 rounded-lg font-semibold transition-all ${
              activeTab === "join"
                ? "bg-white text-blue-600 shadow-sm"
                : "text-gray-600 hover:text-gray-800"
            }`}
          >
            <Search className="inline h-4 w-4 mr-2" />
            Unisciti
          </button>
          <button
            onClick={() => setActiveTab("create")}
            className={`flex-1 py-2 px-4 rounded-lg font-semibold transition-all ${
              activeTab === "create"
                ? "bg-white text-green-600 shadow-sm"
                : "text-gray-600 hover:text-gray-800"
            }`}
          >
            <Plus className="inline h-4 w-4 mr-2" />
            Crea
          </button>
        </div>

        {/* Join Tab Content */}
        {activeTab === "join" && (
          <div className="space-y-6">
            {/* Quick Join */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Unisciti per Nome Stanza
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={roomName}
                  onChange={(e) => setRoomName(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Nome stanza..."
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
                  maxLength={20}
                  disabled={isLoading}
                />
                <ActionButton
                  onClick={() => handleJoinGame()}
                  disabled={isLoading || !roomName.trim()}
                  className="px-4 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl font-bold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? "..." : "Entra"}
                </ActionButton>
              </div>
            </div>

            {/* Available Games List */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-semibold text-gray-700">
                  Partite Disponibili
                </label>
                <button
                  onClick={loadAvailableGames}
                  disabled={loadingGames}
                  className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                >
                  {loadingGames ? "Caricando..." : "Aggiorna"}
                </button>
              </div>

              <div className="space-y-2 max-h-48 overflow-y-auto">
                {loadingGames ? (
                  <div className="text-center py-8 text-gray-500">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    Caricando partite...
                  </div>
                ) : availableGames.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Nessuna partita disponibile</p>
                    <p className="text-xs">Crea una nuova stanza!</p>
                  </div>
                ) : (
                  availableGames.map((game) => (
                    <div
                      key={game.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-gray-800">
                            {game.room_name}
                          </span>
                          {game.host_id && (
                            <Crown className="h-3 w-3 text-yellow-600" />
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-xs text-gray-600 mt-1">
                          <span className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            {game.players?.length || 0}/4
                          </span>
                          <span className="flex items-center gap-1">
                            <Trophy className="h-3 w-3" />
                            {game.victory_points || 31}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            30s
                          </span>
                        </div>
                      </div>
                      <ActionButton
                        onClick={() => handleJoinGame(game.room_name)}
                        disabled={isLoading || (game.players?.length || 0) >= 4}
                        className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-lg font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {(game.players?.length || 0) >= 4 ? "Piena" : "Entra"}
                      </ActionButton>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        )}

        {/* Create Tab Content */}
        {activeTab === "create" && (
          <div className="space-y-6">
            {/* Room Name */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Nome Stanza
              </label>
              <input
                type="text"
                value={roomName}
                onChange={(e) => setRoomName(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="es. bar_savio, gambettola1"
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none transition-all"
                maxLength={20}
                disabled={isLoading}
              />
              <p className="text-xs text-gray-500 mt-1">
                {roomName.length}/20 caratteri
              </p>
            </div>

            {/* Victory Points */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                <Trophy className="inline h-4 w-4 mr-1" />
                Punti Vittoria
              </label>
              <div className="flex gap-2">
                {VICTORY_POINTS_OPTIONS.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => setVictoryPoints(option.value)}
                    disabled={isLoading}
                    className={`flex-1 py-2 px-3 rounded-lg font-semibold transition-all ${
                      victoryPoints === option.value
                        ? "bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }`}
                    title={option.description}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Create Button */}
            <ActionButton
              onClick={handleCreateGame}
              disabled={isLoading || !roomName.trim()}
              className="w-full py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-xl font-bold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? "Creando..." : "Crea Stanza"}
            </ActionButton>
          </div>
        )}

        {/* Info Footer */}
        {/* <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="bg-gray-50 p-3 rounded-xl">
            <div className="flex items-start gap-3">
              <Users className="h-4 w-4 text-gray-600 mt-0.5" />
              <div className="text-xs text-gray-600">
                <p className="font-semibold mb-1">Come funziona:</p>
                <ul className="space-y-1">
                  <li>• Massimo 4 giocatori (2 per squadra)</li>
                  <li>• Timer fisso: 30 secondi per turno</li>
                  <li>• Le stanze scadono dopo 10 minuti</li>
                  <li>• L'host può avviare quando tutti sono pronti</li>
                </ul>
              </div>
            </div>
          </div>
        </div> */}
      </div>
    </div>
  );
};

export default MultiplayerModal;
